@echo off
REM Main Cron Job Runner for Windows
REM รันงานหลักทั้งหมด: ดึง transaction, ตรวจสลิป, เช็ค subscription

echo ========================================
echo Main Cron Job - Starting
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Try to find PHP executable
set PHP_PATH=php
if exist "C:\xampp\php\php.exe" set PHP_PATH=C:\xampp\php\php.exe
if exist "C:\wamp64\bin\php\php8.2.12\php.exe" set PHP_PATH=C:\wamp64\bin\php\php8.2.12\php.exe
if exist "C:\laragon\bin\php\php8.2.12\php.exe" set PHP_PATH=C:\laragon\bin\php\php8.2.12\php.exe

echo Using PHP: %PHP_PATH%
echo.

REM Run the main cron script
"%PHP_PATH%" main_cron.php

REM Check if PHP command failed
if %ERRORLEVEL% EQU 9009 (
    echo.
    echo ERROR: PHP not found! Please run setup_windows.bat first
    pause
    exit /b 9009
)

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Main Cron Job - COMPLETED SUCCESSFULLY
    echo Time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Main Cron Job - COMPLETED WITH ERRORS
    echo Exit Code: %ERRORLEVEL%
    echo Time: %date% %time%
    echo ========================================
)

REM Keep window open for 3 seconds if run manually
timeout /t 3 /nobreak >nul

exit /b %ERRORLEVEL%
