@echo off
echo ========================================
echo Testing Laragon PHP Detection
echo ========================================

cd /d "%~dp0"

echo Current directory: %CD%
echo.

echo Searching for Laragon PHP...
echo.

REM Check versioned folders
echo Checking versioned folders:
for %%v in (php-8.3.* php-8.2.* php-8.1.* php-8.0.* php-7.4.*) do (
    if exist "C:\laragon\bin\php\%%v\php.exe" (
        echo [FOUND] C:\laragon\bin\php\%%v\php.exe
        "C:\laragon\bin\php\%%v\php.exe" -v
        echo.
        set LARAGON_PHP=C:\laragon\bin\php\%%v\php.exe
        goto test_cron
    ) else (
        echo [NOT FOUND] C:\laragon\bin\php\%%v\
    )
)

echo.
echo Checking direct path:
if exist "C:\laragon\bin\php\php.exe" (
    echo [FOUND] C:\laragon\bin\php\php.exe
    "C:\laragon\bin\php\php.exe" -v
    set LARAGON_PHP=C:\laragon\bin\php\php.exe
    goto test_cron
) else (
    echo [NOT FOUND] C:\laragon\bin\php\php.exe
)

echo.
echo ERROR: No Laragon PHP found!
echo.
echo Please check if Laragon is installed and PHP is available
echo Common Laragon PHP locations:
echo - C:\laragon\bin\php\php-8.3.x\php.exe
echo - C:\laragon\bin\php\php-8.2.x\php.exe
echo - C:\laragon\bin\php\php.exe
echo.
pause
exit /b 1

:test_cron
echo.
echo ========================================
echo Testing Cron Scripts with: %LARAGON_PHP%
echo ========================================

echo.
echo Testing Fast Cron...
"%LARAGON_PHP%" fast_cron.php
echo Fast Cron Exit Code: %ERRORLEVEL%

echo.
echo Testing Slow Cron...
"%LARAGON_PHP%" slow_cron.php
echo Slow Cron Exit Code: %ERRORLEVEL%

echo.
echo ========================================
echo Test completed!
echo ========================================
echo.
echo If tests passed, you can now run:
echo - start_fast_cron_laragon.bat (for continuous fast cron)
echo - run_slow_cron_laragon.bat (for one-time slow cron)
echo.

pause
