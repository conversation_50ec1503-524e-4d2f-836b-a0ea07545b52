/* Media Server Management System - Custom Fonts */

/* Font Face Declarations */
@font-face {
    font-family: 'MN Bak kut teh';
    src: url('../../fonts/MN Bak kut teh.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'MN Bak kut teh';
    src: url('../../fonts/MN Bak kut teh Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

/* TH Sarabun Font */
@import url('https://fonts.googleapis.com/css2?family=Sarabun:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Global Font Application */
/* Default font for all pages (TH Sarabun) */
body {
    font-family: 'Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: 0.3px;
}

/* Homepage specific font (MN Bak kut teh) */
body.homepage {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: 0.3px;
}

/* Headings - Default (TH Sarabun) */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Headings - Homepage (MN Bak kut teh) */
body.homepage h1,
body.homepage h2,
body.homepage h3,
body.homepage h4,
body.homepage h5,
body.homepage h6 {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
}

h1 {
    font-size: 2.5rem;
    font-style: italic;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

h2 {
    font-size: 2rem;
    font-weight: 700;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
    font-weight: 600;
}

/* Navigation - Default (TH Sarabun) */
.navbar-brand {
    font-family: 'Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    letter-spacing: 1px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.nav-link {
    font-family: 'Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* Navigation - Homepage (MN Bak kut teh) */
body.homepage .navbar-brand {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 700;
    font-size: 1.8rem;
    letter-spacing: 1px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

body.homepage .nav-link {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* Buttons - Default (TH Sarabun) */
.btn {
    font-family: 'Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: none;
}

/* Buttons - Homepage (MN Bak kut teh) */
body.homepage .btn {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: none;
}

.btn-lg {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Cards */
.card-title {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.card-text {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Forms */
.form-label {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.form-control {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-control::placeholder {
    font-style: italic;
    opacity: 0.7;
}

/* Tables */
.table th {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.table td {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Badges */
.badge {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Alerts */
.alert {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.alert-heading {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Hero Section */
.hero-section h1 {
    font-style: italic;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    font-weight: 700;
}

.hero-section .lead {
    font-size: 1.2rem;
    font-weight: 400;
    line-height: 1.7;
}

/* Display Text */
.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 700;
    letter-spacing: 1px;
}

/* Lead Text */
.lead {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 400;
    line-height: 1.7;
}

/* Small Text */
small, .small {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dropdown */
.dropdown-item {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 400;
}

/* Modal */
.modal-title {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.modal-body {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Breadcrumb */
.breadcrumb-item {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Pagination */
.page-link {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
}

/* List Group */
.list-group-item {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Progress */
.progress-bar {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
}

/* Tooltip & Popover */
.tooltip-inner {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.popover-body {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Special Text Effects */
.text-gradient {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.text-glow {
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* Responsive Font Sizes */
@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    .navbar-brand {
        font-size: 1.5rem;
    }
    
    .hero-section h1 {
        font-size: 2.2rem;
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: 1.8rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .navbar-brand {
        font-size: 1.3rem;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
}
