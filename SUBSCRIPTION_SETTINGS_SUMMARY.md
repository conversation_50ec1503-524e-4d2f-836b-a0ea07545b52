# สรุปการตั้งค่า Subscription สำหรับ Emby และ Jellyfin

## 📋 ภาพรวมการตั้งค่า

### 🎯 เป้าหมาย
- **ความปลอดภัย**: ซ่อนผู้ใช้เสมอ ต้องพิมพ์ username เอง
- **การจัดการ Remote Access**: ควบคุมการเข้าถึงจากภายนอกตาม subscription status
- **การจัดการ User Status**: แยกระหว่าง inactive และ expired

## 🔧 การตั้งค่าตามสถานะ Subscription

### 1. 🆕 **สมัครใหม่ (New User)**
```
✅ IsDisabled = false (เปิดใช้งาน)
✅ IsHidden = true (ซ่อนเพื่อความปลอดภัย)
✅ IsHiddenRemotely = true (ซ่อนจาก remote login)
✅ IsHiddenFromUnusedDevices = true (ซ่อนจาก unused devices)
✅ EnableRemoteAccess = true (เปิด "Allow remote connections")
✅ EnableLocalNetworkAccess = true (เปิด local network)
```

### 2. 🔶 **Subscription Inactive**
```
✅ IsDisabled = false (ยังใช้งานได้)
✅ IsHidden = true (ซ่อนเพื่อความปลอดภัย)
✅ IsHiddenRemotely = true (ซ่อนจาก remote login)
✅ IsHiddenFromUnusedDevices = true (ซ่อนจาก unused devices)
❌ EnableRemoteAccess = false (ปิด "Allow remote connections")
✅ EnableLocalNetworkAccess = true (ยังใช้ local ได้)
```

### 3. ❌ **Subscription Expired**
```
❌ IsDisabled = true (ปิดใช้งาน "Disable This user")
✅ IsHidden = true (ซ่อนเพื่อความปลอดภัย)
✅ IsHiddenRemotely = true (ซ่อนจาก remote login)
✅ IsHiddenFromUnusedDevices = true (ซ่อนจาก unused devices)
❌ EnableRemoteAccess = false (ปิด "Allow remote connections")
❌ EnableLocalNetworkAccess = false (ปิด local network)
```

## 🎬 ฟังก์ชันใน EmbyAPI

### สำหรับผู้ใช้ใหม่:
```php
$emby_api->setUserPolicyWithRemoteAccess($userId);
```

### สำหรับ Subscription Inactive:
```php
$emby_api->disableRemoteAccessForInactiveSubscription($userId);
```

### สำหรับ Subscription Expired:
```php
$emby_api->disableUserForExpiredSubscription($userId);
```

### สำหรับเปิดใช้งานใหม่:
```php
$emby_api->enableUserForSubscription($userId);
```

## 🐙 ฟังก์ชันใน JellyfinAPI

### สำหรับผู้ใช้ใหม่:
```php
$jellyfin_api->setUserPolicyWithRemoteAccess($userId);
```

### สำหรับ Subscription Inactive:
```php
$jellyfin_api->disableRemoteAccessForInactiveSubscription($userId);
```

### สำหรับ Subscription Expired:
```php
$jellyfin_api->disableUserForExpiredSubscription($userId);
```

### สำหรับเปิดใช้งานใหม่:
```php
$jellyfin_api->enableUserForSubscription($userId);
```

## 🔄 Flow การทำงาน

### เมื่อสมัครใหม่:
1. สร้าง user ใหม่
2. เรียก `setUserPolicyWithRemoteAccess()`
3. ผู้ใช้สามารถเข้าถึงจาก remote ได้
4. ผู้ใช้ถูกซ่อนจากหน้า login (ต้องพิมพ์ username)

### เมื่อ Subscription เปลี่ยนเป็น Inactive:
1. เรียก `disableRemoteAccessForInactiveSubscription()`
2. ปิดการเข้าถึงจาก remote
3. ยังสามารถใช้งาน local network ได้
4. ผู้ใช้ยังคงถูกซ่อนจากหน้า login

### เมื่อ Subscription หมดอายุ:
1. เรียก `disableUserForExpiredSubscription()`
2. ปิดการใช้งาน user ทั้งหมด
3. ปิดการเข้าถึงทั้ง remote และ local
4. ผู้ใช้ยังคงถูกซ่อนจากหน้า login

### เมื่อต่อ Subscription:
1. เรียก `enableUserForSubscription()`
2. เปิดการใช้งาน user
3. เปิดการเข้าถึงจาก remote
4. ผู้ใช้ยังคงถูกซ่อนจากหน้า login

## 🛡️ ความปลอดภัย

### การซ่อนผู้ใช้:
- **IsHidden = true**: ซ่อนจากหน้า login หลัก
- **IsHiddenRemotely = true**: ซ่อนจาก remote login
- **IsHiddenFromUnusedDevices = true**: ซ่อนจาก unused devices

### ประโยชน์:
- ผู้ใช้ต้องพิมพ์ username เอง
- ลดความเสี่ยงจากการเดาชื่อผู้ใช้
- เพิ่มความปลอดภัยในการเข้าถึง

## 📊 การทดสอบ

### ไฟล์ทดสอบ:
```
test_emby_settings.php
```

### การรันทดสอบ:
1. เปิด `http://localhost/your-project/test_emby_settings.php`
2. ระบบจะทดสอบ:
   - การสร้างผู้ใช้ใหม่
   - การตั้งค่า subscription inactive
   - การตั้งค่า subscription expired
   - การลบผู้ใช้ทดสอบ

### ผลลัพธ์ที่คาดหวัง:
- ✅ ผู้ใช้ใหม่: remote access เปิด, ซ่อนผู้ใช้
- ✅ Inactive: remote access ปิด, local ยังใช้ได้, ซ่อนผู้ใช้
- ✅ Expired: user disabled, ทุกการเข้าถึงปิด, ซ่อนผู้ใช้

## 🔧 การบำรุงรักษา

### Log Files:
- ตรวจสอบ PHP error log สำหรับข้อผิดพลาด
- ดู log ของ Emby/Jellyfin สำหรับการเปลี่ยนแปลง policy

### การ Debug:
- ใช้ `getUserPolicy()` เพื่อตรวจสอบการตั้งค่าปัจจุบัน
- ตรวจสอบ API response สำหรับข้อผิดพลาด

### การอัปเดต:
- เมื่อมีการเปลี่ยนแปลง Emby/Jellyfin API
- เมื่อต้องการเพิ่มฟีเจอร์ความปลอดภัยใหม่
