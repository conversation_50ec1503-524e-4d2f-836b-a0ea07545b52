<?php
session_start();

// ตรวจสอบสิทธิ์แอดมิน
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    die("❌ ไม่มีสิทธิ์เข้าถึง");
}

echo "<h1>🧪 ทดสอบ Form ง่ายๆ</h1>";

// แสดง POST data
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h2>✅ POST Request ได้รับแล้ว!</h2>";
    echo "<h3>📋 POST Data:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    if (isset($_POST['subscription_id']) && isset($_POST['new_status'])) {
        echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;'>";
        echo "🎉 ข้อมูลครบถ้วน!";
        echo "<br>subscription_id: " . $_POST['subscription_id'];
        echo "<br>new_status: " . $_POST['new_status'];
        echo "<br>user_id: " . ($_POST['user_id'] ?? 'ไม่มี');
        echo "</div>";
    }
} else {
    echo "<h2>📝 ยังไม่มี POST Request</h2>";
}

echo "<hr>";
echo "<h3>🧪 ทดสอบ Form แบบเดียวกับ admin_subscriptions</h3>";

// ฟอร์มทดสอบแบบเดียวกัน
echo '<div style="margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 8px;">';
echo '<h4>ฟอร์มทดสอบแบบ Dropdown:</h4>';

echo '<div class="dropdown">';
echo '<button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">ทดสอบ Dropdown</button>';
echo '<ul class="dropdown-menu">';

// Active
echo '<li>';
echo '<form method="POST" class="d-inline">';
echo '<input type="hidden" name="subscription_id" value="4">';
echo '<input type="hidden" name="user_id" value="40">';
echo '<input type="hidden" name="new_status" value="active">';
echo '<button type="submit" name="update_status" class="dropdown-item" onclick="return confirm(\'เปลี่ยนสถานะเป็น ใช้งานอยู่ หรือไม่?\')">ทดสอบ Active</button>';
echo '</form>';
echo '</li>';

// Inactive
echo '<li>';
echo '<form method="POST" class="d-inline">';
echo '<input type="hidden" name="subscription_id" value="4">';
echo '<input type="hidden" name="user_id" value="40">';
echo '<input type="hidden" name="new_status" value="inactive">';
echo '<button type="submit" name="update_status" class="dropdown-item" onclick="return confirm(\'เปลี่ยนสถานะเป็น ไม่ใช้งาน หรือไม่?\')">ทดสอบ Inactive</button>';
echo '</form>';
echo '</li>';

echo '</ul>';
echo '</div>';
echo '</div>';

echo "<hr>";
echo "<h3>🔗 ลิงก์</h3>";
echo "<ul>";
echo "<li><a href='?page=admin_subscriptions'>กลับหน้า Admin Subscriptions</a></li>";
echo "<li><a href='test_simple_form.php'>รีเฟรชหน้านี้</a></li>";
echo "</ul>";

// เพิ่ม Bootstrap CSS และ JS
echo '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">';
echo '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>';

echo "<hr>";
echo "<p><small>⚠️ ไฟล์นี้เป็นไฟล์ทดสอบ ให้ลบออกหลังใช้เสร็จ</small></p>";
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Test Simple Form: DOM loaded');
    
    // ตรวจสอบ form submissions
    const forms = document.querySelectorAll('form');
    console.log('🔍 Test Simple Form: Found ' + forms.length + ' forms');
    
    forms.forEach(function(form, index) {
        form.addEventListener('submit', function(e) {
            console.log('🔥 TEST FORM SUBMIT EVENT! Form index:', index);
            const formData = new FormData(form);
            for (let [key, value] of formData.entries()) {
                console.log('  - ' + key + ': ' + value);
            }
        });
    });
    
    // ตรวจสอบ buttons
    const buttons = document.querySelectorAll('button[name="update_status"]');
    console.log('🔍 Test Simple Form: Found ' + buttons.length + ' update_status buttons');
    
    buttons.forEach(function(btn, index) {
        btn.addEventListener('click', function(e) {
            console.log('🔥 TEST BUTTON CLICKED! Button index:', index);
            console.log('🔍 Button text:', btn.textContent.trim());
        });
    });
});
</script>
