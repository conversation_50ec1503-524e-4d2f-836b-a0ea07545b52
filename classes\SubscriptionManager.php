<?php
require_once 'config/db_config.php';
require_once 'classes/EmbyAPI.php';
require_once 'classes/JellyfinAPI.php';

class SubscriptionManager {
    private $conn;
    private $settings;
    private $emby_api;
    private $jellyfin_api;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();

        // Initialize APIs with actual settings
        $emby_url = 'https://emby.embyjames.xyz';
        $emby_key = 'd2499d0eacfe4ccbba940836be91a9f1';
        $jellyfin_url = 'https://jellyfin.embyjames.xyz';
        $jellyfin_key = 'e30753ff625847e0bf7163df609ebaf6';

        if($emby_url && $emby_key) {
            try {
                $this->emby_api = new EmbyAPI($emby_url, $emby_key);
                error_log('SubscriptionManager: Emby API initialized');
            } catch (Exception $e) {
                error_log('SubscriptionManager: Failed to initialize Emby API: ' . $e->getMessage());
            }
        } else {
            error_log('SubscriptionManager: Emby API not configured');
        }

        if($jellyfin_url && $jellyfin_key) {
            try {
                $this->jellyfin_api = new JellyfinAPI($jellyfin_url, $jellyfin_key);
                error_log('SubscriptionManager: Jellyfin API initialized');
            } catch (Exception $e) {
                error_log('SubscriptionManager: Failed to initialize Jellyfin API: ' . $e->getMessage());
            }
        } else {
            error_log('SubscriptionManager: Jellyfin API not configured');
        }
    }
    
    public function checkExpiredSubscriptions() {
        try {
            // Get all expired subscriptions
            $query = "SELECT us.*, u.username, u.id as user_id 
                      FROM user_subscriptions us 
                      JOIN users u ON us.user_id = u.id 
                      WHERE us.status = 'active' AND us.end_date < CURDATE()";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $expired_subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $results = [];
            
            foreach($expired_subscriptions as $subscription) {
                $result = $this->handleExpiredSubscription($subscription);
                $results[] = [
                    'user_id' => $subscription['user_id'],
                    'username' => $subscription['username'],
                    'result' => $result
                ];
            }
            
            return [
                'success' => true,
                'processed' => count($results),
                'results' => $results
            ];
            
        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function handleExpiredSubscription($subscription) {
        try {
            $this->conn->beginTransaction();

            // Mark subscription as expired
            $query = "UPDATE user_subscriptions SET status = 'expired' WHERE id = :subscription_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':subscription_id', $subscription['id']);
            $stmt->execute();

            // Disable users for expired subscription (set IsDisabled=true, IsHidden=true)
            // ไม่ต้องเปลี่ยนรหัสผ่านแล้ว เพราะใช้การ Disable This user แทน
            $disable_users_result = $this->disableUsersForExpiredSubscription($subscription['user_id']);

            $this->conn->commit();

            // Log the action
            $this->logSubscriptionAction($subscription['user_id'], 'subscription_expired',
                'Subscription expired, users disabled (Disable This user) - ' .
                'Emby disabled: ' . ($disable_users_result['emby'] ? 'success' : 'failed') . ', ' .
                'Jellyfin disabled: ' . ($disable_users_result['jellyfin'] ? 'success' : 'failed'));

            return [
                'success' => true,
                'users_disabled' => $disable_users_result
            ];

        } catch(Exception $e) {
            $this->conn->rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    public function checkInactiveSubscriptions() {
        try {
            // Get all users with inactive subscriptions but still have remote access enabled
            $query = "SELECT DISTINCT u.id as user_id, u.username,
                             ea.emby_user_id, ja.jellyfin_user_id
                      FROM users u
                      LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active' AND us.end_date >= CURDATE()
                      LEFT JOIN emby_accounts ea ON u.id = ea.user_id
                      LEFT JOIN jellyfin_accounts ja ON u.id = ja.user_id
                      WHERE us.id IS NULL
                      AND (ea.emby_user_id IS NOT NULL OR ja.jellyfin_user_id IS NOT NULL)";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $inactive_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $results = [];

            foreach($inactive_users as $user) {
                // Check if user actually has remote access enabled
                $has_remote_access = $this->checkUserRemoteAccessStatus($user['user_id']);

                if($has_remote_access['has_remote_access']) {
                    $result = $this->handleInactiveSubscription($user);
                    $results[] = [
                        'user_id' => $user['user_id'],
                        'username' => $user['username'],
                        'result' => $result
                    ];
                }
            }

            return [
                'success' => true,
                'processed' => count($results),
                'results' => $results
            ];

        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private function handleInactiveSubscription($user) {
        try {
            // Disable remote access for inactive subscription
            $remote_access_result = $this->disableRemoteAccess($user['user_id']);

            // Log the action
            $this->logSubscriptionAction($user['user_id'], 'subscription_inactive',
                'Remote access disabled due to inactive subscription - Emby: ' .
                ($remote_access_result['emby'] ?? 'N/A') . ', Jellyfin: ' .
                ($remote_access_result['jellyfin'] ?? 'N/A'));

            return [
                'success' => true,
                'remote_access_disabled' => $remote_access_result
            ];

        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private function checkUserRemoteAccessStatus($user_id) {
        try {
            $has_remote_access = false;
            $details = [];

            // Check Emby remote access status
            if($this->emby_api) {
                $emby_query = "SELECT emby_user_id FROM emby_accounts WHERE user_id = :user_id";
                $emby_stmt = $this->conn->prepare($emby_query);
                $emby_stmt->bindParam(':user_id', $user_id);
                $emby_stmt->execute();

                if($emby_stmt->rowCount() > 0) {
                    $emby_account = $emby_stmt->fetch(PDO::FETCH_ASSOC);
                    $emby_policy = $this->emby_api->getUserPolicy($emby_account['emby_user_id']);

                    if($emby_policy['success'] && isset($emby_policy['data']['EnableRemoteAccess'])) {
                        $emby_remote = $emby_policy['data']['EnableRemoteAccess'];
                        $details['emby'] = $emby_remote;
                        if($emby_remote) $has_remote_access = true;
                    }
                }
            }

            // Check Jellyfin remote access status
            if($this->jellyfin_api) {
                $jellyfin_query = "SELECT jellyfin_user_id FROM jellyfin_accounts WHERE user_id = :user_id";
                $jellyfin_stmt = $this->conn->prepare($jellyfin_query);
                $jellyfin_stmt->bindParam(':user_id', $user_id);
                $jellyfin_stmt->execute();

                if($jellyfin_stmt->rowCount() > 0) {
                    $jellyfin_account = $jellyfin_stmt->fetch(PDO::FETCH_ASSOC);
                    $jellyfin_policy = $this->jellyfin_api->getUserPolicy($jellyfin_account['jellyfin_user_id']);

                    if($jellyfin_policy['success'] && isset($jellyfin_policy['data']['EnableRemoteAccess'])) {
                        $jellyfin_remote = $jellyfin_policy['data']['EnableRemoteAccess'];
                        $details['jellyfin'] = $jellyfin_remote;
                        if($jellyfin_remote) $has_remote_access = true;
                    }
                }
            }

            return [
                'has_remote_access' => $has_remote_access,
                'details' => $details
            ];

        } catch(Exception $e) {
            return [
                'has_remote_access' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function checkRenewedSubscriptions() {
        try {
            // Get users who have renewed their subscriptions (have active subscription but disabled accounts)
            $query = "SELECT us.*, u.username, u.id as user_id,
                             ea.emby_user_id, ja.jellyfin_user_id
                      FROM user_subscriptions us
                      JOIN users u ON us.user_id = u.id
                      LEFT JOIN emby_accounts ea ON u.id = ea.user_id
                      LEFT JOIN jellyfin_accounts ja ON u.id = ja.user_id
                      WHERE us.status = 'active'
                      AND us.end_date >= CURDATE()
                      AND (ea.status = 'inactive' OR ja.status = 'inactive')";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $renewed_subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $results = [];
            
            foreach($renewed_subscriptions as $subscription) {
                $result = $this->handleRenewedSubscription($subscription);
                $results[] = [
                    'user_id' => $subscription['user_id'],
                    'username' => $subscription['username'],
                    'result' => $result
                ];
            }
            
            return [
                'success' => true,
                'processed' => count($results),
                'results' => $results
            ];
            
        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function handleRenewedSubscription($subscription) {
        try {
            $this->conn->beginTransaction();

            // Enable users for renewed subscription (set IsDisabled=false)
            // ไม่ต้อง restore รหัสผ่านแล้ว เพราะไม่ได้เปลี่ยนตอนหมดอายุ
            $enable_users_result = $this->enableUsersForRenewedSubscription($subscription['user_id']);

            $this->conn->commit();

            // Log the action
            $this->logSubscriptionAction($subscription['user_id'], 'subscription_renewed',
                'Subscription renewed, users enabled (Unchecked Disable This user) - ' .
                'Emby enabled: ' . ($enable_users_result['emby'] ? 'success' : 'failed') . ', ' .
                'Jellyfin enabled: ' . ($enable_users_result['jellyfin'] ? 'success' : 'failed'));

            return [
                'success' => true,
                'users_enabled' => $enable_users_result
            ];

        } catch(Exception $e) {
            $this->conn->rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    // ลบ password related methods แล้ว เพราะไม่ใช้การเปลี่ยนรหัสผ่านแล้ว
    // ใช้การ Disable This user แทน
    
    public function getUserActiveSubscription($user_id) {
        $query = "SELECT us.*, p.name as package_name, p.description 
                  FROM user_subscriptions us 
                  JOIN packages p ON us.package_id = p.id 
                  WHERE us.user_id = :user_id AND us.status = 'active' AND us.end_date >= CURDATE()
                  ORDER BY us.end_date DESC LIMIT 1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        if($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return null;
    }
    
    /**
     * เปิดใช้งาน remote access สำหรับ user ทั้ง Emby และ Jellyfin
     */
    public function enableRemoteAccess($user_id) {
        try {
            $results = [];

            // Enable remote access for Emby
            if($this->emby_api) {
                $emby_query = "SELECT emby_user_id FROM emby_accounts WHERE user_id = :user_id AND status = 'active'";
                $emby_stmt = $this->conn->prepare($emby_query);
                $emby_stmt->bindParam(':user_id', $user_id);
                $emby_stmt->execute();

                if($emby_stmt->rowCount() > 0) {
                    $emby_account = $emby_stmt->fetch(PDO::FETCH_ASSOC);
                    $emby_result = $this->emby_api->enableRemoteAccess($emby_account['emby_user_id']);
                    $results['emby'] = $emby_result['success'];
                }
            }

            // Enable remote access for Jellyfin
            if($this->jellyfin_api) {
                $jellyfin_query = "SELECT jellyfin_user_id FROM jellyfin_accounts WHERE user_id = :user_id AND status = 'active'";
                $jellyfin_stmt = $this->conn->prepare($jellyfin_query);
                $jellyfin_stmt->bindParam(':user_id', $user_id);
                $jellyfin_stmt->execute();

                if($jellyfin_stmt->rowCount() > 0) {
                    $jellyfin_account = $jellyfin_stmt->fetch(PDO::FETCH_ASSOC);
                    $jellyfin_result = $this->jellyfin_api->enableRemoteAccess($jellyfin_account['jellyfin_user_id']);
                    $results['jellyfin'] = $jellyfin_result['success'];
                }
            }

            return $results;

        } catch(Exception $e) {
            error_log('Failed to enable remote access: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ปิดใช้งาน remote access สำหรับ user ทั้ง Emby และ Jellyfin
     */
    public function disableRemoteAccess($user_id) {
        try {
            $results = [];

            // Disable remote access for Emby using improved method
            if($this->emby_api) {
                $emby_query = "SELECT emby_user_id FROM emby_accounts WHERE user_id = :user_id";
                $emby_stmt = $this->conn->prepare($emby_query);
                $emby_stmt->bindParam(':user_id', $user_id);
                $emby_stmt->execute();

                if($emby_stmt->rowCount() > 0) {
                    $emby_account = $emby_stmt->fetch(PDO::FETCH_ASSOC);

                    // Try standard method first
                    $emby_result = $this->emby_api->disableRemoteAccess($emby_account['emby_user_id']);

                    // If standard method fails, try alternative
                    if(!$emby_result['success']) {
                        $emby_result = $this->emby_api->disableRemoteAccessAlternative($emby_account['emby_user_id']);
                    }

                    $results['emby'] = $emby_result['success'];

                    // Log the result for debugging
                    error_log('Emby remote access disable for user ' . $user_id . ': ' .
                             ($emby_result['success'] ? 'success' : 'failed - ' . ($emby_result['message'] ?? 'unknown error')));
                }
            }

            // Disable remote access for Jellyfin
            if($this->jellyfin_api) {
                $jellyfin_query = "SELECT jellyfin_user_id FROM jellyfin_accounts WHERE user_id = :user_id";
                $jellyfin_stmt = $this->conn->prepare($jellyfin_query);
                $jellyfin_stmt->bindParam(':user_id', $user_id);
                $jellyfin_stmt->execute();

                if($jellyfin_stmt->rowCount() > 0) {
                    $jellyfin_account = $jellyfin_stmt->fetch(PDO::FETCH_ASSOC);
                    $jellyfin_result = $this->jellyfin_api->disableRemoteAccess($jellyfin_account['jellyfin_user_id']);
                    $results['jellyfin'] = $jellyfin_result['success'];

                    // Log the result for debugging
                    error_log('Jellyfin remote access disable for user ' . $user_id . ': ' .
                             ($jellyfin_result['success'] ? 'success' : 'failed - ' . ($jellyfin_result['message'] ?? 'unknown error')));
                }
            }

            return $results;

        } catch(Exception $e) {
            error_log('Failed to disable remote access: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * เปิดใช้งาน users เมื่อ subscription ต่ออายุ
     */
    public function enableUsersForRenewedSubscription($user_id) {
        try {
            error_log('SubscriptionManager: Enabling users for renewed subscription - User ID: ' . $user_id);

            $results = [];

            // Enable Emby user
            $emby_query = "SELECT emby_user_id FROM emby_accounts WHERE user_id = :user_id";
            $emby_stmt = $this->conn->prepare($emby_query);
            $emby_stmt->bindParam(':user_id', $user_id);
            $emby_stmt->execute();

            if($emby_stmt->rowCount() > 0) {
                $emby_account = $emby_stmt->fetch(PDO::FETCH_ASSOC);
                error_log('SubscriptionManager: Found Emby account - User ID: ' . $emby_account['emby_user_id']);

                if($this->emby_api) {
                    $emby_result = $this->emby_api->enableUserForSubscription($emby_account['emby_user_id']);
                    $results['emby'] = $emby_result['success'];
                    error_log('SubscriptionManager: Emby enable result: ' . ($emby_result['success'] ? 'success' : 'failed - ' . ($emby_result['message'] ?? 'unknown')));

                    // Update local account status
                    if($emby_result['success']) {
                        $update_query = "UPDATE emby_accounts SET status = 'active' WHERE user_id = :user_id";
                        $update_stmt = $this->conn->prepare($update_query);
                        $update_stmt->bindParam(':user_id', $user_id);
                        $update_stmt->execute();
                    }
                } else {
                    error_log('SubscriptionManager: Emby API not available');
                    $results['emby'] = false;
                }
            } else {
                error_log('SubscriptionManager: No Emby account found for user ' . $user_id);
                $results['emby'] = false;
            }

            // Enable Jellyfin user
            $jellyfin_query = "SELECT jellyfin_user_id FROM jellyfin_accounts WHERE user_id = :user_id";
            $jellyfin_stmt = $this->conn->prepare($jellyfin_query);
            $jellyfin_stmt->bindParam(':user_id', $user_id);
            $jellyfin_stmt->execute();

            if($jellyfin_stmt->rowCount() > 0) {
                $jellyfin_account = $jellyfin_stmt->fetch(PDO::FETCH_ASSOC);
                error_log('SubscriptionManager: Found Jellyfin account - User ID: ' . $jellyfin_account['jellyfin_user_id']);

                if($this->jellyfin_api) {
                    $jellyfin_result = $this->jellyfin_api->enableUserForSubscription($jellyfin_account['jellyfin_user_id']);
                    $results['jellyfin'] = $jellyfin_result['success'];
                    error_log('SubscriptionManager: Jellyfin enable result: ' . ($jellyfin_result['success'] ? 'success' : 'failed - ' . ($jellyfin_result['message'] ?? 'unknown')));

                    // Update local account status
                    if($jellyfin_result['success']) {
                        $update_query = "UPDATE jellyfin_accounts SET status = 'active' WHERE user_id = :user_id";
                        $update_stmt = $this->conn->prepare($update_query);
                        $update_stmt->bindParam(':user_id', $user_id);
                        $update_stmt->execute();
                    }
                } else {
                    error_log('SubscriptionManager: Jellyfin API not available');
                    $results['jellyfin'] = false;
                }
            } else {
                error_log('SubscriptionManager: No Jellyfin account found for user ' . $user_id);
                $results['jellyfin'] = false;
            }

            $success = ($results['emby'] || $results['jellyfin']);

            error_log('SubscriptionManager: Enable users completed - Emby: ' . ($results['emby'] ? 'success' : 'failed') . ', Jellyfin: ' . ($results['jellyfin'] ? 'success' : 'failed'));

            return [
                'success' => $success,
                'emby' => $results['emby'],
                'jellyfin' => $results['jellyfin']
            ];

        } catch(Exception $e) {
            error_log('SubscriptionManager: Failed to enable users for renewed subscription: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ปิดใช้งาน users เมื่อ subscription หมดอายุ
     */
    public function disableUsersForExpiredSubscription($user_id) {
        try {
            error_log('SubscriptionManager: Disabling users for expired subscription - User ID: ' . $user_id);

            $results = [];

            // Disable Emby user
            $emby_query = "SELECT emby_user_id FROM emby_accounts WHERE user_id = :user_id";
            $emby_stmt = $this->conn->prepare($emby_query);
            $emby_stmt->bindParam(':user_id', $user_id);
            $emby_stmt->execute();

            if($emby_stmt->rowCount() > 0) {
                $emby_account = $emby_stmt->fetch(PDO::FETCH_ASSOC);
                error_log('SubscriptionManager: Found Emby account - User ID: ' . $emby_account['emby_user_id']);

                if($this->emby_api) {
                    $emby_result = $this->emby_api->disableUserForExpiredSubscription($emby_account['emby_user_id']);
                    $results['emby'] = $emby_result['success'];
                    error_log('SubscriptionManager: Emby disable result: ' . ($emby_result['success'] ? 'success' : 'failed - ' . ($emby_result['message'] ?? 'unknown')));

                    // Update local account status
                    if($emby_result['success']) {
                        $update_query = "UPDATE emby_accounts SET status = 'inactive' WHERE user_id = :user_id";
                        $update_stmt = $this->conn->prepare($update_query);
                        $update_stmt->bindParam(':user_id', $user_id);
                        $update_stmt->execute();
                    }
                } else {
                    error_log('SubscriptionManager: Emby API not available');
                    $results['emby'] = false;
                }
            } else {
                error_log('SubscriptionManager: No Emby account found for user ' . $user_id);
                $results['emby'] = false;
            }

            // Disable Jellyfin user
            $jellyfin_query = "SELECT jellyfin_user_id FROM jellyfin_accounts WHERE user_id = :user_id";
            $jellyfin_stmt = $this->conn->prepare($jellyfin_query);
            $jellyfin_stmt->bindParam(':user_id', $user_id);
            $jellyfin_stmt->execute();

            if($jellyfin_stmt->rowCount() > 0) {
                $jellyfin_account = $jellyfin_stmt->fetch(PDO::FETCH_ASSOC);
                error_log('SubscriptionManager: Found Jellyfin account - User ID: ' . $jellyfin_account['jellyfin_user_id']);

                if($this->jellyfin_api) {
                    $jellyfin_result = $this->jellyfin_api->disableUserForExpiredSubscription($jellyfin_account['jellyfin_user_id']);
                    $results['jellyfin'] = $jellyfin_result['success'];
                    error_log('SubscriptionManager: Jellyfin disable result: ' . ($jellyfin_result['success'] ? 'success' : 'failed - ' . ($jellyfin_result['message'] ?? 'unknown')));

                    // Update local account status
                    if($jellyfin_result['success']) {
                        $update_query = "UPDATE jellyfin_accounts SET status = 'inactive' WHERE user_id = :user_id";
                        $update_stmt = $this->conn->prepare($update_query);
                        $update_stmt->bindParam(':user_id', $user_id);
                        $update_stmt->execute();
                    }
                } else {
                    error_log('SubscriptionManager: Jellyfin API not available');
                    $results['jellyfin'] = false;
                }
            } else {
                error_log('SubscriptionManager: No Jellyfin account found for user ' . $user_id);
                $results['jellyfin'] = false;
            }

            $success = ($results['emby'] || $results['jellyfin']);

            error_log('SubscriptionManager: Disable users completed - Emby: ' . ($results['emby'] ? 'success' : 'failed') . ', Jellyfin: ' . ($results['jellyfin'] ? 'success' : 'failed'));

            return [
                'success' => $success,
                'emby' => $results['emby'],
                'jellyfin' => $results['jellyfin']
            ];

        } catch(Exception $e) {
            error_log('SubscriptionManager: Failed to disable users for expired subscription: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    private function logSubscriptionAction($user_id, $action, $description) {
        $query = "INSERT INTO usage_logs (user_id, action, description)
                  VALUES (:user_id, :action, :description)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':description', $description);

        return $stmt->execute();
    }
}
?>
