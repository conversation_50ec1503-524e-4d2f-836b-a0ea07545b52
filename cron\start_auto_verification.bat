@echo off
echo ========================================
echo   Automatic Slip Verification Service
echo   รันทุก 5 วินาที
echo ========================================
echo.

REM เปลี่ยนไปยัง directory ของโปรเจค
cd /d "%~dp0\.."

REM ค้นหา PHP ในตำแหน่งต่างๆ
set PHP_PATH=""

REM ลองหา PHP ใน PATH ก่อน
php --version >nul 2>&1
if not errorlevel 1 (
    set PHP_PATH=php
    goto php_found
)

REM ลองหาใน Laragon
if exist "C:\laragon\bin\php\php-8.1.10-Win32-vs16-x64\php.exe" (
    set PHP_PATH="C:\laragon\bin\php\php-8.1.10-Win32-vs16-x64\php.exe"
    goto php_found
)

if exist "C:\laragon\bin\php\php-8.2.12-Win32-vs16-x64\php.exe" (
    set PHP_PATH="C:\laragon\bin\php\php-8.2.12-Win32-vs16-x64\php.exe"
    goto php_found
)

REM ลองหาใน XAMPP
if exist "C:\xampp\php\php.exe" (
    set PHP_PATH="C:\xampp\php\php.exe"
    goto php_found
)

REM ลองหาใน WAMP
if exist "C:\wamp64\bin\php\php8.1.0\php.exe" (
    set PHP_PATH="C:\wamp64\bin\php\php8.1.0\php.exe"
    goto php_found
)

echo ❌ ไม่พบ PHP ในระบบ
echo กรุณาติดตั้ง PHP หรือแก้ไข path ในสคริปนี้
echo ตำแหน่งที่ค้นหา:
echo - C:\laragon\bin\php\
echo - C:\xampp\php\
echo - C:\wamp64\bin\php\
pause
exit /b 1

:php_found
echo ✅ พบ PHP แล้ว: %PHP_PATH%
echo 🚀 เริ่มการตรวจสอบสลิปอัตโนมัติ...
echo 📝 Log จะถูกบันทึกใน cron/logs/slip_verification_auto.log
echo.
echo กด Ctrl+C เพื่อหยุดการทำงาน
echo.

:loop
    echo [%date% %time%] กำลังตรวจสอบ...
    %PHP_PATH% cron/slip_verification_auto.php
    
    REM รอ 5 วินาที
    timeout /t 5 /nobreak >nul
    
    REM ตรวจสอบว่าผู้ใช้กด Ctrl+C หรือไม่
    if errorlevel 1 goto end
    
goto loop

:end
echo.
echo 🛑 หยุดการทำงานแล้ว
pause
