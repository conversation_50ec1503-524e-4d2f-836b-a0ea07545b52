# การตรวจสอบสลิปอัตโนมัติทุก 5 วินาที

## ไฟล์ที่เกี่ยวข้อง

### สคริปหลัก
- `slip_verification_auto.php` - สคริป PHP สำหรับตรวจสอบสลิป
- `start_auto_verification.bat` - สคริป Batch สำหรับ Windows (แนะนำ)
- `start_auto_verification.sh` - สคริป Shell สำหรับ Linux/Mac
- `start_auto_verification.ps1` - สคริป PowerShell สำหรับ Windows

### Log Files
- `logs/slip_verification_auto.log` - Log การทำงานของระบบ

## วิธีการใช้งาน

### สำหรับ Windows

#### วิธีที่ 1: ใช้ Batch Script (แนะนำ)
```cmd
# เปิด Command Prompt และไปยัง directory ของโปรเจค
cd C:\laragon\www\your-project

# รันสคริป
cron\start_auto_verification.bat
```

#### วิธีที่ 2: ใช้ PowerShell
```powershell
# เปิด PowerShell และไปยัง directory ของโปรเจค
cd C:\laragon\www\your-project

# อนุญาตให้รัน PowerShell script (ครั้งแรกเท่านั้น)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# รันสคริป
.\cron\start_auto_verification.ps1
```

#### วิธีที่ 3: รันด้วย PHP โดยตรง
```cmd
# รันครั้งเดียว
php cron\slip_verification_auto.php

# รันแบบ loop (manual)
while true; do php cron\slip_verification_auto.php; sleep 5; done
```

### สำหรับ Linux/Mac

#### ใช้ Shell Script
```bash
# ไปยัง directory ของโปรเจค
cd /path/to/your-project

# ทำให้ script executable (ครั้งแรกเท่านั้น)
chmod +x cron/start_auto_verification.sh

# รันสคริป
./cron/start_auto_verification.sh
```

#### ใช้ Cron Job (รันในพื้นหลัง)
```bash
# แก้ไข crontab
crontab -e

# เพิ่มบรรทัดนี้เพื่อรันทุก 5 วินาที (ไม่แนะนำ เพราะ cron ไม่รองรับ sub-minute)
# แทนที่จะใช้ systemd timer หรือ supervisor

# หรือรันทุกนาที
* * * * * /usr/bin/php /path/to/your-project/cron/slip_verification_auto.php
```

## การทำงานของระบบ

### ขั้นตอนการทำงาน
1. **เชื่อมต่อฐานข้อมูล** - ตรวจสอบการเชื่อมต่อ
2. **ดึงข้อมูลธุรกรรม** - จาก API หรือไฟล์ backup
3. **ค้นหาการจับคู่** - หาการเติมเงินที่ตรงกับธุรกรรม
4. **อนุมัติอัตโนมัติ** - อัปเดตสถานะและเพิ่มเงิน
5. **บันทึก Log** - เก็บประวัติการทำงาน

### การ Logging
- ✅ **สำเร็จ**: แสดงจำนวนที่ประมวลผลและอนุมัติ
- ⚠️ **คำเตือน**: แสดงรายการที่ไม่สามารถอนุมัติได้
- ❌ **ข้อผิดพลาด**: แสดงข้อผิดพลาดและตำแหน่ง

### การจัดการ Log
- เก็บ Log ล่าสุด 1000 บรรทัด
- ทำความสะอาดอัตโนมัติ
- แสดงทั้งในไฟล์และ console

## การหยุดการทำงาน

### Windows
- กด `Ctrl + C` ใน Command Prompt/PowerShell
- ปิดหน้าต่าง Command Prompt

### Linux/Mac
- กด `Ctrl + C` ใน Terminal
- ใช้คำสั่ง `kill` หาก run ในพื้นหลัง

## การตรวจสอบสถานะ

### ดู Log แบบ Real-time
```bash
# Linux/Mac
tail -f cron/logs/slip_verification_auto.log

# Windows (PowerShell)
Get-Content cron\logs\slip_verification_auto.log -Wait -Tail 10
```

### ตรวจสอบการทำงาน
1. ดูไฟล์ log ล่าสุด
2. ตรวจสอบ timestamp ของ log
3. ดูจำนวนการอนุมัติในฐานข้อมูล

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

#### 1. ไม่พบ PHP
```
❌ ไม่พบ PHP ในระบบ
```
**วิธีแก้**: ติดตั้ง PHP หรือเพิ่ม PHP ใน PATH

#### 2. ไม่สามารถเชื่อมต่อฐานข้อมูล
```
❌ การตรวจสอบล้มเหลว: Database connection failed
```
**วิธีแก้**: ตรวจสอบการตั้งค่าฐานข้อมูลใน `config/db_config.php`

#### 3. ไม่พบไฟล์ธุรกรรม
```
⚠️ ไม่สามารถอนุมัติ: No transaction data available
```
**วิธีแก้**: ตรวจสอบไฟล์ `api/data/transactions.json` หรือ API connection

#### 4. Foreign Key Error
```
❌ Foreign key constraint fails
```
**วิธีแก้**: รัน `sql/fix_foreign_key.sql`

## การปรับแต่ง

### เปลี่ยนความถี่การตรวจสอบ
แก้ไขค่า `sleep 5` หรือ `timeout /t 5` ในสคริป:
- `sleep 10` = ทุก 10 วินาที
- `sleep 30` = ทุก 30 วินาที
- `sleep 60` = ทุกนาที

### เปลี่ยนจำนวน Log ที่เก็บ
แก้ไขค่าใน `slip_verification_auto.php`:
```php
// เก็บเฉพาะ log ล่าสุด 2000 บรรทัด
if (count($lines) > 2000) {
    $recent_lines = array_slice($lines, -2000);
```

### เพิ่มการแจ้งเตือน
เพิ่มการส่งอีเมลใน `slip_verification_auto.php`:
```php
// ส่งอีเมลเมื่อมีการอนุมัติ
if ($approved > 0) {
    mail('<EMAIL>', 'Slip Approved', "อนุมัติ {$approved} รายการ");
}
```

## ความปลอดภัย

### การป้องกัน
- ✅ ตรวจสอบ CLI mode
- ✅ ป้องกันการเรียกจาก web browser
- ✅ บันทึก log ทุกการทำงาน
- ✅ จำกัดขนาดไฟล์ log

### ข้อควรระวัง
- อย่ารันหลายตัวพร้อมกัน
- ตรวจสอบ log เป็นประจำ
- สำรองข้อมูลก่อนใช้งาน
- ทดสอบในสภาพแวดล้อม development ก่อน
