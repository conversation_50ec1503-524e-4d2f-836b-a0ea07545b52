<?php
require_once 'classes/PaymentManager.php';

// Handle approve/reject actions
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $paymentManager = new PaymentManager();

    if(isset($_POST['approve_topup'])) {
        $topup_id = $_POST['topup_id'] ?? 0;
        $result = $paymentManager->approveTopUp($topup_id, $user['id']);

        if($result['success']) {
            $success_message = $result['message'];
            // Redirect to refresh the page and show updated data
            header('Location: ?page=admin_topups&success=' . urlencode($result['message']));
            exit;
        } else {
            $error_message = $result['message'];
        }
    }

    if(isset($_POST['reject_topup'])) {
        $topup_id = $_POST['topup_id'] ?? 0;
        $reason = $_POST['reason'] ?? '';
        $result = $paymentManager->rejectTopUp($topup_id, $user['id'], $reason);

        if($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    }
}

// Check for success message from redirect
if(isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

// Get pending top-ups
$paymentManager = new PaymentManager();
$pending_topups = $paymentManager->getPendingTopUps();

// Get all top-ups for history
$database = new Database();
$conn = $database->getConnection();

$query = "SELECT t.*, u.username, u.full_name, a.username as approved_by_username
          FROM top_ups t 
          JOIN users u ON t.user_id = u.id 
          LEFT JOIN users a ON t.approved_by = a.id
          WHERE t.status IN ('approved', 'rejected')
          ORDER BY t.updated_at DESC 
          LIMIT 50";

$stmt = $conn->prepare($query);
$stmt->execute();
$processed_topups = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">จัดการการเติมเงิน</h2>
            <p class="text-muted">อนุมัติหรือปฏิเสธการเติมเงินของผู้ใช้</p>
        </div>
    </div>
    
    <!-- Messages -->
    <?php if(isset($success_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if(isset($error_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Pending Top-ups -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>รายการรอดำเนินการ 
                        <span class="badge bg-dark"><?php echo count($pending_topups); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if(!empty($pending_topups)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>วันที่</th>
                                    <th>ผู้ใช้</th>
                                    <th>จำนวนเงิน</th>
                                    <th>QR Code</th>
                                    <th>การดำเนินการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($pending_topups as $topup): ?>
                                <tr>
                                    <td>
                                        <?php echo date('d/m/Y H:i', strtotime($topup['created_at'])); ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($topup['username']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($topup['full_name']); ?></small>
                                    </td>
                                    <td>
                                        <h6 class="text-primary mb-0"><?php echo number_format($topup['amount'], 2); ?> ฿</h6>
                                    </td>
                                    <td>
                                        <?php if($topup['qr_code_url']): ?>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                data-bs-toggle="modal" data-bs-target="#qrModal<?php echo $topup['id']; ?>">
                                            <i class="fas fa-qrcode me-1"></i>ดู QR
                                        </button>
                                        
                                        <!-- QR Code Modal -->
                                        <div class="modal fade" id="qrModal<?php echo $topup['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog modal-sm">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h6 class="modal-title">QR Code - <?php echo number_format($topup['amount'], 2); ?> ฿</h6>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body text-center">
                                                        <img src="<?php echo htmlspecialchars($topup['qr_code_url']); ?>" 
                                                             alt="QR Code" class="img-fluid">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- Approve Form -->
                                            <form method="POST" class="d-inline" style="margin-right: 5px;">
                                                <input type="hidden" name="topup_id" value="<?php echo $topup['id']; ?>">
                                                <input type="hidden" name="approve_topup" value="yes">
                                                <button type="submit" class="btn btn-sm btn-success"
                                                        onclick="return confirm('คุณต้องการอนุมัติการเติมเงิน <?php echo number_format($topup['amount'], 2); ?> ฿ ของ <?php echo htmlspecialchars($topup['username']); ?> หรือไม่?')">
                                                    <i class="fas fa-check me-1"></i>อนุมัติ
                                                </button>
                                            </form>

                                            <!-- Reject Button -->
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    data-bs-toggle="modal" data-bs-target="#rejectModal<?php echo $topup['id']; ?>">
                                                <i class="fas fa-times me-1"></i>ปฏิเสธ
                                            </button>
                                        </div>
                                        
                                        <!-- Reject Modal -->
                                        <div class="modal fade" id="rejectModal<?php echo $topup['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h6 class="modal-title">ปฏิเสธการเติมเงิน</h6>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <form method="POST">
                                                        <div class="modal-body">
                                                            <input type="hidden" name="topup_id" value="<?php echo $topup['id']; ?>">
                                                            <input type="hidden" name="reject_topup" value="1">
                                                            <p>คุณต้องการปฏิเสธการเติมเงิน <?php echo number_format($topup['amount'], 2); ?> ฿ ของ <?php echo htmlspecialchars($topup['username']); ?> หรือไม่?</p>
                                                            <div class="mb-3">
                                                                <label for="reason<?php echo $topup['id']; ?>" class="form-label">เหตุผล (ไม่บังคับ)</label>
                                                                <textarea class="form-control" id="reason<?php echo $topup['id']; ?>" name="reason" rows="3"></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                                            <button type="submit" class="btn btn-danger">ปฏิเสธ</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">ไม่มีรายการรอดำเนินการ</h6>
                        <p class="text-muted">รายการเติมเงินทั้งหมดได้รับการดำเนินการแล้ว</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Processed Top-ups History -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>ประวัติการดำเนินการ (50 รายการล่าสุด)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if(!empty($processed_topups)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>วันที่สร้าง</th>
                                    <th>วันที่ดำเนินการ</th>
                                    <th>ผู้ใช้</th>
                                    <th>จำนวนเงิน</th>
                                    <th>สถานะ</th>
                                    <th>ดำเนินการโดย</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($processed_topups as $topup): ?>
                                <tr>
                                    <td>
                                        <small><?php echo date('d/m/Y H:i', strtotime($topup['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <small><?php echo $topup['approved_at'] ? date('d/m/Y H:i', strtotime($topup['approved_at'])) : '-'; ?></small>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($topup['username']); ?></strong>
                                    </td>
                                    <td>
                                        <?php echo number_format($topup['amount'], 2); ?> ฿
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $topup['status'] == 'approved' ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $topup['status'] == 'approved' ? 'อนุมัติแล้ว' : 'ปฏิเสธ'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo htmlspecialchars($topup['approved_by_username'] ?? '-'); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-history text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">ยังไม่มีประวัติการดำเนินการ</h6>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
