@echo off
REM Main Cron Job Runner for Windows
REM รันงานหลักทั้งหมด: ดึง transaction, ตรวจสลิป, เช็ค subscription

echo ========================================
echo Main Cron Job - Starting
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Run the main cron script
php main_cron.php

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Main Cron Job - COMPLETED SUCCESSFULLY
    echo Time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Main Cron Job - COMPLETED WITH ERRORS
    echo Exit Code: %ERRORLEVEL%
    echo Time: %date% %time%
    echo ========================================
)

REM Keep window open for 3 seconds if run manually
timeout /t 3 /nobreak >nul

exit /b %ERRORLEVEL%
