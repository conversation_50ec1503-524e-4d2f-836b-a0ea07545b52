@echo off
REM Slip Verification Runner - รันทุก 5 วินาที

echo ========================================
echo Slip Verification - Every 5 seconds
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Try to find PHP executable (Laragon first)
set PHP_PATH=php

REM Check Laragon (common versions)
for %%v in (php-8.3.* php-8.2.* php-8.1.* php-8.0.* php-7.4.*) do (
    if exist "C:\laragon\bin\php\%%v\php.exe" (
        set PHP_PATH=C:\laragon\bin\php\%%v\php.exe
        goto php_found
    )
)

REM Check Laragon direct path
if exist "C:\laragon\bin\php\php.exe" set PHP_PATH=C:\laragon\bin\php\php.exe & goto php_found

REM Check other locations
if exist "C:\xampp\php\php.exe" set PHP_PATH=C:\xampp\php\php.exe
if exist "C:\wamp64\bin\php\php8.2.12\php.exe" set PHP_PATH=C:\wamp64\bin\php\php8.2.12\php.exe

:php_found
echo Using PHP: %PHP_PATH%
echo.

REM Test PHP
"%PHP_PATH%" -v >nul 2>&1
if %ERRORLEVEL% EQU 9009 (
    echo ERROR: PHP not found! Please check PHP installation
    echo.
    echo Try running: find_laragon_php.bat
    echo Or use: start_slip_verification_5sec_custom.bat
    pause
    goto end
)

echo กำลังรันงาน:
echo - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
echo.
echo กด Ctrl+C เพื่อหยุด
echo ========================================

REM Create logs directory if not exists
if not exist "logs" mkdir logs

:loop
    REM Run slip verification
    "%PHP_PATH%" slip_verification_cron_5sec.php
    
    REM Check if command failed
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ERROR: Slip verification failed with exit code %ERRORLEVEL%
        echo Check logs/slip_verification_5sec.log for details
        echo.
        pause
        goto end
    )
    
    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul
    
goto loop

:end
echo.
echo ========================================
echo Slip Verification - Stopped
echo Time: %date% %time%
echo ========================================
