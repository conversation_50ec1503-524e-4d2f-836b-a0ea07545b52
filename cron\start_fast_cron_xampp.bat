@echo off
REM Fast Cron Runner for XAMPP - รันทุก 5 วินาที

echo ========================================
echo Fast Cron Job - XAMPP Version
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Set XAMPP PHP path
set PHP_PATH=C:\xampp\php\php.exe

REM Check if XAMPP PHP exists
if not exist "%PHP_PATH%" (
    echo ERROR: XAMPP PHP not found at %PHP_PATH%
    echo.
    echo Please check if XAMPP is installed or update the PHP_PATH
    echo.
    pause
    exit /b 1
)

echo Using PHP: %PHP_PATH%
echo.
echo กำลังรันงาน:
echo - ดึงข้อมูล Transaction จาก API
echo - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
echo.
echo กด Ctrl+C เพื่อหยุด
echo ========================================

REM Create logs directory if not exists
if not exist "logs" mkdir logs

:loop
    REM Run fast cron
    "%PHP_PATH%" fast_cron.php
    
    REM Check if command failed
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ERROR: Fast cron failed with exit code %ERRORLEVEL%
        echo Check logs/fast_cron.log for details
        echo.
        pause
        goto end
    )
    
    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul
    
goto loop

:end
echo.
echo ========================================
echo Fast Cron Job - Stopped
echo Time: %date% %time%
echo ========================================
