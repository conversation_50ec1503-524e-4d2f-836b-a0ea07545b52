# PayNoi API Cron Job - Quick Start Guide

## เริ่มต้นใช้งานอย่างรวดเร็ว

### 🚀 สำหรับ Windows (Laragon)

#### 1. ทดสอบการทำงาน
```batch
# รันครั้งเดียว
run_cron.bat

# รันต่อเนื่องทุก 5 วินาที
run_continuous_cron.bat
```

#### 2. ตั้งค่า Scheduled Task
```batch
# ใช้ตัวช่วยตั้งค่า
setup_windows_scheduler.bat
```

### 🐧 สำหรับ Linux/macOS

#### 1. ทดสอบการทำงาน
```bash
# รันครั้งเดียว
php cron_api_fetch.php

# รันต่อเนื่องทุก 5 วินาที
chmod +x run_continuous_cron.sh
./run_continuous_cron.sh
```

#### 2. ตั้งค่า Cron Job
```bash
# ใช้ตัวช่วยตั้งค่า
chmod +x setup_cron.sh
./setup_cron.sh

# หรือตั้งค่าเอง
crontab -e
# เพิ่มบรรทัด: */5 * * * * /usr/bin/php /path/to/cron_api_fetch.php
```

## 📊 ดูผลลัพธ์

### หน้าเว็บสำหรับดูข้อมูล
- **ข้อมูลธุรกรรม**: `http://localhost/api/view_data.php`
- **Log ปกติ**: `http://localhost/api/view_logs.php`
- **Log Continuous**: `http://localhost/api/view_continuous_logs.php`

### ข้อมูลที่แสดง
- Transaction ID
- วันที่และเวลา
- หมายเลขบัญชี
- จำนวนเงิน
- สกุลเงิน
- ยอดคงเหลือ
- ประเภทธุรกรรม

## ⚙️ การกำหนดค่า

### แก้ไข API Key และ Record Key
แก้ไขในไฟล์ `cron_api_fetch.php` และ `continuous_cron.php`:
```php
$config = [
    'api_key' => 'your_api_key_here',
    'record_key' => 'your_record_key_here',
    // ...
];
```

### ปรับความถี่ของ Continuous Cron
แก้ไขในไฟล์ `continuous_cron.php`:
```php
'interval_seconds' => 5, // เปลี่ยนเป็นจำนวนวินาทีที่ต้องการ
```

## 🔧 การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

#### 1. PHP ไม่พบ
```batch
# Windows: แก้ไข path ใน batch files
set PHP_PATH=C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe
```

#### 2. Permission denied (Linux/macOS)
```bash
chmod +x *.sh
chmod 755 *.php
```

#### 3. ไม่สามารถสร้างโฟลเดอร์ได้
```bash
chmod 755 .
```

### ตรวจสอบสถานะ

#### Windows Task Scheduler
```batch
# ดู task ทั้งหมด
schtasks /query

# รัน task ทันที
schtasks /run /tn "PayNoi_API_Task"

# ลบ task
schtasks /delete /tn "PayNoi_API_Task" /f
```

#### Linux/macOS Cron
```bash
# ดู cron jobs
crontab -l

# ดู log ของ cron
sudo tail -f /var/log/cron.log
```

## 📁 โครงสร้างไฟล์

```
api/
├── cron_api_fetch.php          # สคริปต์หลัก (รันครั้งเดียว)
├── continuous_cron.php         # สคริปต์ต่อเนื่อง (ทุก 5 วินาที)
├── view_data.php               # หน้าดูข้อมูล
├── view_logs.php               # หน้าดู log ปกติ
├── view_continuous_logs.php    # หน้าดู log continuous
├── run_cron.bat               # Windows: รันครั้งเดียว
├── run_continuous_cron.bat    # Windows: รันต่อเนื่อง
├── setup_windows_scheduler.bat # Windows: ตั้งค่า scheduler
├── run_continuous_cron.sh     # Linux/macOS: รันต่อเนื่อง
├── setup_cron.sh              # Linux/macOS: ตั้งค่า cron
├── data/
│   └── transactions.json      # ข้อมูลธุรกรรม
└── logs/
    ├── api_fetch.log          # Log ปกติ
    └── continuous_cron.log    # Log continuous
```

## 🎯 เลือกวิธีการที่เหมาะสม

### ใช้ Regular Cron เมื่อ:
- ต้องการดึงข้อมูลทุก 5-15 นาที หรือมากกว่า
- ต้องการประหยัดทรัพยากรระบบ
- ไม่ต้องการข้อมูลแบบ real-time

### ใช้ Continuous Cron เมื่อ:
- ต้องการข้อมูลแบบ real-time (ทุก 5 วินาที)
- ต้องการตรวจสอบการเปลี่ยนแปลงอย่างต่อเนื่อง
- มีทรัพยากรระบบเพียงพอ

## 📞 การสนับสนุน

หากพบปัญหา:
1. ตรวจสอบ Log files
2. ตรวจสอบการตั้งค่า API Key และ Record Key
3. ทดสอบการเชื่อมต่อ API ด้วย `debug_api.php`
4. ตรวจสอบ PHP version และ extensions ที่จำเป็น
