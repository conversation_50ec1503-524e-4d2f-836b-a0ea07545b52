# Slip Verification Runner for PowerShell - รันทุก 5 วินาที

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Slip Verification - Every 5 seconds" -ForegroundColor Green
Write-Host "Time: $(Get-Date)" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan

# Change to script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

# Try to find PHP executable
$PhpPath = "php"

# Check Laragon (common versions)
$LaragonVersions = @("php-8.3.*", "php-8.2.*", "php-8.1.*", "php-8.0.*", "php-7.4.*")
foreach ($version in $LaragonVersions) {
    $LaragonPath = Get-ChildItem "C:\laragon\bin\php\$version\php.exe" -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($LaragonPath) {
        $PhpPath = $LaragonPath.FullName
        break
    }
}

# Check Laragon direct path
if (Test-Path "C:\laragon\bin\php\php.exe") {
    $PhpPath = "C:\laragon\bin\php\php.exe"
}

# Check other locations
if (Test-Path "C:\xampp\php\php.exe") {
    $PhpPath = "C:\xampp\php\php.exe"
}

Write-Host "Using PHP: $PhpPath" -ForegroundColor Yellow
Write-Host ""

# Test PHP
try {
    $phpTest = Start-Process -FilePath $PhpPath -ArgumentList "-v" -Wait -PassThru -NoNewWindow -RedirectStandardOutput "nul" -RedirectStandardError "nul"
    if ($phpTest.ExitCode -ne 0) {
        throw "PHP test failed"
    }
} catch {
    Write-Host "ERROR: PHP not found or failed to run!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Try running: find_laragon_php.bat" -ForegroundColor Yellow
    Write-Host "Or check your PHP installation" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "กำลังรันงาน:" -ForegroundColor Green
Write-Host "- ตรวจสอบและอนุมัติสลิปอัตโนมัติ" -ForegroundColor White
Write-Host ""
Write-Host "กด Ctrl+C เพื่อหยุด" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan

# Create logs directory if not exists
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
}

# Trap Ctrl+C
$null = Register-EngineEvent PowerShell.Exiting -Action {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Slip Verification - Stopped" -ForegroundColor Red
    Write-Host "Time: $(Get-Date)" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
}

try {
    # Main loop
    while ($true) {
        # Run slip verification
        $process = Start-Process -FilePath $PhpPath -ArgumentList "slip_verification_cron_5sec.php" -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -ne 0) {
            Write-Host ""
            Write-Host "ERROR: Slip verification failed with exit code $($process.ExitCode)" -ForegroundColor Red
            Write-Host "Check logs/slip_verification_5sec.log for details" -ForegroundColor Yellow
            Write-Host ""
            Read-Host "Press Enter to exit"
            break
        }
        
        # Wait 5 seconds
        Start-Sleep -Seconds 5
    }
} catch {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Slip Verification - Stopped (Error)" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Time: $(Get-Date)" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
}
