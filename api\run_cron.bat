@echo off
REM PayNoi API Cron Job Runner for Windows
REM This batch file runs the PHP script to fetch API data

echo ========================================
echo PayNoi API Data Fetcher
echo ========================================
echo.

REM Set the PHP path (adjust this path according to your Laragon installation)
set PHP_PATH=C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe

REM Check if PHP exists
if not exist "%PHP_PATH%" (
    echo Error: PHP not found at %PHP_PATH%
    echo Please update the PHP_PATH in this batch file
    pause
    exit /b 1
)

echo PHP found at: %PHP_PATH%
echo.

REM Run the cron script
echo Running API fetch script...
"%PHP_PATH%" cron_api_fetch.php

REM Check the exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo SUCCESS: API data fetched successfully!
    echo ========================================
    echo.
    echo You can view the data at:
    echo - Data: http://localhost/api/view_data.php
    echo - Logs: http://localhost/api/view_logs.php
) else (
    echo.
    echo ========================================
    echo ERROR: Failed to fetch API data
    echo ========================================
    echo Check the logs for more information
)

echo.
echo Press any key to exit...
pause >nul
