<?php
/**
 * Cron Job: Check and handle expired/renewed subscriptions
 * Run this script daily via cron job
 * Example crontab entry: 0 2 * * * /usr/bin/php /path/to/your/project/cron/check_subscriptions.php
 */

// Set the working directory to the project root
$project_root = dirname(__DIR__);
chdir($project_root);

require_once 'classes/SubscriptionManager.php';

// Log file for cron job
$log_file = 'logs/cron_subscriptions.log';

// Create logs directory if it doesn't exist
if (!file_exists('logs')) {
    mkdir('logs', 0755, true);
}

function writeLog($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry;
}

try {
    writeLog("Starting subscription check cron job");
    
    $subscriptionManager = new SubscriptionManager();
    
    // Check for inactive subscriptions first (auto-disable remote access)
    writeLog("Checking for inactive subscriptions...");
    $inactive_result = $subscriptionManager->checkInactiveSubscriptions();

    if($inactive_result['success']) {
        writeLog("Auto-disabled remote access for {$inactive_result['processed']} inactive users");

        foreach($inactive_result['results'] as $result) {
            if($result['result']['success']) {
                writeLog("Successfully disabled remote access for inactive user: {$result['username']}");
            } else {
                writeLog("Failed to disable remote access for inactive user: {$result['username']} - {$result['result']['message']}");
            }
        }
    } else {
        writeLog("Error checking inactive subscriptions: " . $inactive_result['message']);
    }

    // Check for expired subscriptions
    writeLog("Checking for expired subscriptions...");
    $expired_result = $subscriptionManager->checkExpiredSubscriptions();
    
    if($expired_result['success']) {
        writeLog("Processed {$expired_result['processed']} expired subscriptions");
        
        foreach($expired_result['results'] as $result) {
            if($result['result']['success']) {
                writeLog("Successfully handled expired subscription for user: {$result['username']}");
            } else {
                writeLog("Failed to handle expired subscription for user: {$result['username']} - {$result['result']['message']}");
            }
        }
    } else {
        writeLog("Error checking expired subscriptions: " . $expired_result['message']);
    }
    
    // Check for renewed subscriptions
    writeLog("Checking for renewed subscriptions...");
    $renewed_result = $subscriptionManager->checkRenewedSubscriptions();
    
    if($renewed_result['success']) {
        writeLog("Processed {$renewed_result['processed']} renewed subscriptions");
        
        foreach($renewed_result['results'] as $result) {
            if($result['result']['success']) {
                writeLog("Successfully handled renewed subscription for user: {$result['username']}");
            } else {
                writeLog("Failed to handle renewed subscription for user: {$result['username']} - {$result['result']['message']}");
            }
        }
    } else {
        writeLog("Error checking renewed subscriptions: " . $renewed_result['message']);
    }
    
    writeLog("Subscription check cron job completed successfully");
    
} catch(Exception $e) {
    writeLog("Fatal error in subscription check cron job: " . $e->getMessage());
    exit(1);
}

exit(0);
?>
