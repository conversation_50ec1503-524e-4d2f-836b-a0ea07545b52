# Remote Access Control System

## 📋 Overview

ระบบควบคุม Remote Access สำหรับ Emby และ Jellyfin ที่ทำงานตาม subscription status

## 🎯 Features

### ✅ การทำงานอัตโนมัติ
- **เมื่อซื้อ package** → เปิด remote access อัตโนมัติ
- **เมื่อ subscription หมดอายุ** → ปิด remote access อัตโนมัติ  
- **เมื่อต่ออายุ** → เปิด remote access อัตโนมัติ

### ✅ การจัดการด้วยตนเอง
- เปิด/ปิด remote access สำหรับ user แต่ละคน
- ตรวจสอบสถานะ real-time จาก media servers
- แสดงสถานะ subscription และ remote access

### ✅ Multiple API Methods
- ลองหลายวิธีการในการปิด remote access
- Fallback mechanisms เมื่อวิธีแรกไม่สำเร็จ
- Support ทั้ง Emby และ Jellyfin

## 🔧 Technical Implementation

### API Methods Used

#### Emby API:
```php
// Standard method
$emby_api->disableRemoteAccess($user_id)

// Alternative method (if standard fails)
$emby_api->disableRemoteAccessAlternative($user_id)

// Get users with multiple endpoints
$emby_api->getAllUsers()
```

#### Jellyfin API:
```php
// Standard method
$jellyfin_api->disableRemoteAccess($user_id)

// Get users with multiple endpoints  
$jellyfin_api->getAllUsers()
```

### Policy Settings

#### Initial Settings (After Registration):
```json
{
  "EnableRemoteAccess": false,           // ❌ Remote connections disabled
  "EnableLocalNetworkAccess": true,      // ✅ Local access enabled
  "IsHidden": false,                     // ✅ Visible in local network
  "IsHiddenRemotely": true              // ❌ Hidden from remote login
}
```

#### After Package Purchase:
```json
{
  "EnableRemoteAccess": true,            // ✅ Remote connections enabled
  "EnableLocalNetworkAccess": true,      // ✅ Local access enabled
  "IsHidden": false,                     // ✅ Visible in local network
  "IsHiddenRemotely": true              // ❌ Still hidden from remote login
}
```

#### After Subscription Expires:
```json
{
  "EnableRemoteAccess": false,           // ❌ Remote connections disabled
  "EnableLocalNetworkAccess": true,      // ✅ Local access enabled
  "IsHidden": false,                     // ✅ Visible in local network
  "IsHiddenRemotely": true              // ❌ Hidden from remote login
}
```

## 🚀 Usage

### Admin Interface

1. **เข้าหน้า Admin Settings**
   ```
   index.php?page=admin_settings
   ```

2. **คลิก "Remote Access Control"**
   ```
   index.php?page=remote_access_control
   ```

3. **ดูสถานะและจัดการ users**
   - ✅ เปิด remote access (ปุ่มเขียว)
   - ❌ ปิด remote access (ปุ่มเหลือง)
   - 🔄 ตรวจสอบ subscription ทั้งหมด

### Automatic Processing

#### Cron Job:
```bash
# Run every hour
0 * * * * /usr/bin/php /path/to/project/cron_subscription_check.php
```

#### Manual Check:
```php
$subscriptionManager = new SubscriptionManager();

// Check expired subscriptions
$expired = $subscriptionManager->checkExpiredSubscriptions();

// Check renewed subscriptions  
$renewed = $subscriptionManager->checkRenewedSubscriptions();
```

## 🔍 Troubleshooting

### หากปิด Remote Access ไม่ได้

#### วิธีที่ 1: Manual Override
1. เข้า Emby/Jellyfin Dashboard โดยตรง
2. ไปที่ Users → เลือก user → Edit
3. Uncheck "Allow remote connections to this server"
4. Save การตั้งค่า

#### วิธีที่ 2: Check API Settings
1. ตรวจสอบ API URL และ API Key
2. ตรวจสอบ network connectivity
3. ดู error logs ใน PHP error log

#### วิธีที่ 3: Check User Permissions
1. ตรวจสอบว่า user เป็น administrator หรือไม่
2. Administrator อาจไม่สามารถปิด remote access ได้
3. ลองกับ regular user

### Common Issues

#### "Failed to get users list"
- ตรวจสอบ API Key และ URL
- ลอง endpoint อื่น: `/Users`, `/Users/<USER>/Users?EnableImages=false`

#### "Policy Error"  
- API Key อาจไม่มีสิทธิ์เพียงพอ
- Server version อาจไม่รองรับ endpoint นี้

#### "API ไม่พร้อมใช้งาน"
- ตรวจสอบ network connectivity
- ตรวจสอบ server status

## 📊 Monitoring

### Log Files
```bash
# PHP Error Log
tail -f /var/log/php_errors.log | grep "Emby\|Jellyfin"

# System Logs  
SELECT * FROM system_logs WHERE action LIKE '%remote_access%' ORDER BY created_at DESC;

# Usage Logs
SELECT * FROM usage_logs WHERE action LIKE '%remote_access%' ORDER BY created_at DESC;
```

### Status Indicators
- 🌐 **Remote: ON** - Remote access enabled
- 🏠 **Remote: OFF** - Remote access disabled  
- ❓ **Remote: Unknown** - Cannot determine status
- ❌ **API ไม่พร้อมใช้งาน** - API connection failed

## 🎉 Success Criteria

### ✅ System Working Correctly When:
1. Users สามารถซื้อ package และได้ remote access
2. Remote access ปิดอัตโนมัติเมื่อ subscription หมดอายุ
3. Remote access เปิดอัตโนมัติเมื่อต่ออายุ
4. Admin สามารถจัดการ remote access ด้วยตนเอง
5. สถานะแสดงถูกต้อง real-time

### 🔧 Manual Intervention Required When:
1. API ไม่สามารถปิด remote access ได้ (server-level settings)
2. User เป็น administrator ที่มี permanent remote access
3. Network หรือ API connectivity issues

---

**ระบบพร้อมใช้งานแล้ว!** 🎬✨
