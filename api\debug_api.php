<?php
$api_key = "6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446";
$record_key = "100568";
$api_url = "https://paynoi.com/api_line?api_key=" . urlencode($api_key) . "&record_key=" . urlencode($record_key);

echo "API URL: " . $api_url . "\n\n";

$ch = curl_init($api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: " . $http_code . "\n";
echo "Response: " . $response . "\n\n";

if ($response === false || $http_code != 200) {
    echo "Error: Failed to fetch data (HTTP $http_code)\n";
} else {
    $data = json_decode($response, true);
    echo "Decoded data:\n";
    print_r($data);
    
    if (isset($data['status'])) {
        echo "\nStatus: " . $data['status'] . "\n";
        
        if ($data['status'] === 'success' && isset($data['data'])) {
            echo "Data structure:\n";
            print_r($data['data']);
        }
    }
}
?>
