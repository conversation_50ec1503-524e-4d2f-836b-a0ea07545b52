<?php
/**
 * ตรวจสอบการอัปเดตสถานะ subscription
 */

session_start();

// ตรวจสอบสิทธิ์แอดมิน
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    die("❌ ไม่มีสิทธิ์เข้าถึง");
}

require_once 'config/db_config.php';

echo "<h1>🔍 ตรวจสอบการอัปเดตสถานะ Subscription</h1>";

$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
}

echo "<p>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";

// ตรวจสอบตาราง user_subscriptions
try {
    $check_table = "SHOW TABLES LIKE 'user_subscriptions'";
    $stmt = $conn->prepare($check_table);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ ไม่พบตาราง user_subscriptions</p>";
        echo "<p><strong>วิธีแก้:</strong> รัน <code>sql/create_user_subscriptions_table.sql</code></p>";
        exit();
    }
    
    echo "<p>✅ พบตาราง user_subscriptions</p>";
    
    // ตรวจสอบโครงสร้างตาราง
    $describe = "DESCRIBE user_subscriptions";
    $stmt = $conn->prepare($describe);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>📋 โครงสร้างตาราง</h2>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบว่ามี status 'inactive' หรือไม่
    $status_column = array_filter($columns, function($col) {
        return $col['Field'] == 'status';
    });
    
    if (!empty($status_column)) {
        $status_info = array_values($status_column)[0];
        if (strpos($status_info['Type'], 'inactive') !== false) {
            echo "<p>✅ มีสถานะ 'inactive' แล้ว</p>";
        } else {
            echo "<p>❌ ยังไม่มีสถานะ 'inactive'</p>";
            echo "<p><strong>วิธีแก้:</strong> รัน <code>sql/add_inactive_status.sql</code></p>";
        }
    }
    
    // ดูข้อมูล subscriptions
    $query = "SELECT COUNT(*) as count FROM user_subscriptions";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo "<h2>📊 ข้อมูล Subscriptions</h2>";
    echo "<p>จำนวนรวม: {$count} รายการ</p>";
    
    if ($count > 0) {
        // แสดงข้อมูลตัวอย่าง
        $query = "SELECT us.*, u.username 
                  FROM user_subscriptions us 
                  LEFT JOIN users u ON us.user_id = u.id 
                  LIMIT 5";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>ข้อมูลตัวอย่าง:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>User</th><th>Package ID</th><th>Status</th><th>Start</th><th>End</th></tr>";
        foreach ($samples as $sample) {
            echo "<tr>";
            echo "<td>{$sample['id']}</td>";
            echo "<td>" . ($sample['username'] ?? 'N/A') . "</td>";
            echo "<td>{$sample['package_id']}</td>";
            echo "<td>{$sample['status']}</td>";
            echo "<td>{$sample['start_date']}</td>";
            echo "<td>{$sample['end_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // ทดสอบการอัปเดต
        if (isset($_POST['test_update'])) {
            $test_id = $_POST['test_id'];
            $test_status = $_POST['test_status'];
            
            echo "<h3>🧪 ทดสอบการอัปเดต</h3>";
            
            try {
                $update_query = "UPDATE user_subscriptions SET status = :status, updated_at = NOW() WHERE id = :id";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bindParam(':status', $test_status);
                $update_stmt->bindParam(':id', $test_id);
                $result = $update_stmt->execute();
                
                if ($result) {
                    $affected = $update_stmt->rowCount();
                    echo "<p>✅ อัปเดตสำเร็จ: {$affected} รายการ</p>";
                } else {
                    echo "<p>❌ อัปเดตล้มเหลว</p>";
                }
                
            } catch (Exception $e) {
                echo "<p>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
            }
        }
        
        // ฟอร์มทดสอบ
        echo "<h3>🧪 ทดสอบการอัปเดต</h3>";
        echo "<form method='POST'>";
        echo "<label>Subscription ID: </label>";
        echo "<select name='test_id'>";
        foreach ($samples as $sample) {
            echo "<option value='{$sample['id']}'>{$sample['id']} - {$sample['username']}</option>";
        }
        echo "</select><br><br>";
        
        echo "<label>สถานะใหม่: </label>";
        echo "<select name='test_status'>";
        echo "<option value='active'>Active</option>";
        echo "<option value='inactive'>Inactive</option>";
        echo "<option value='expired'>Expired</option>";
        echo "<option value='cancelled'>Cancelled</option>";
        echo "</select><br><br>";
        
        echo "<button type='submit' name='test_update'>ทดสอบอัปเดต</button>";
        echo "</form>";
        
    } else {
        echo "<p>ไม่มีข้อมูล subscriptions</p>";
        echo "<p>ให้สร้างข้อมูลทดสอบก่อน</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
}

echo "<h2>🔗 ลิงก์</h2>";
echo "<ul>";
echo "<li><a href='?page=admin_subscriptions'>หน้า Admin Subscriptions</a></li>";
echo "<li><a href='?page=admin_users'>หน้า Admin Users</a></li>";
echo "</ul>";

// ลบไฟล์นี้หลังใช้เสร็จ
echo "<hr>";
echo "<p><small>⚠️ ไฟล์นี้เป็นไฟล์ทดสอบ ให้ลบออกหลังใช้เสร็จ</small></p>";
?>
