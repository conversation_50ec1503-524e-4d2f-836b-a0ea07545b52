<?php
// Get server status and latest media
require_once 'classes/ServerStatus.php';

$serverStatus = new ServerStatus();
$emby_status = $serverStatus->checkEmbyStatus();
$jellyfin_status = $serverStatus->checkJellyfinStatus();
$web_status = $serverStatus->checkWebStatus();
$latest_media = $serverStatus->getLatestMedia(8);
?>



<!-- Hero Section -->
<section class="hero-section" style="background: url('banner/avengers.gif') center/cover no-repeat; position: relative; min-height: 600px; padding: 80px 0;">
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.75); z-index: 1;"></div>
    <div class="container" style="position: relative; z-index: 2;">
        <div class="row align-items-center">
            <div class="col-lg-12" data-animate="slide-in-left">
                <div class="text-center mb-5">
                    <h2 class="fw-bold mb-4" style="color: #ffffff; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">ไม่พลาดทุกความสนุก! สมัครแพ็กเกจดูหนังออนไลน์วันนี้ คุ้มกว่าที่เคย!</h2>
                    <p class="mb-4" style="color: #e0e0e0; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);">สัมผัสประสบการณ์ความบันเทิงเหนือระดับ! รวมหนังและซีรีส์ฮิตไว้ในที่เดียว สมัครตอนนี้ แล้วเริ่มดูได้ทันที!</p>

                    <!-- CTA Buttons -->
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <?php if($is_logged_in): ?>
                            <a href="?page=packages" class="btn btn-outline-light btn-lg px-4 py-3" style="background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.3); backdrop-filter: blur(10px);">
                                <i class="fas fa-box me-2"></i>ดูแพ็กเกจทั้งหมด
                            </a>
                            <a href="?page=dashboard" class="btn btn-outline-light btn-lg px-4 py-3" style="background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.3); backdrop-filter: blur(10px);">
                                <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด
                            </a>
                        <?php else: ?>
                            <a href="?page=register" class="btn btn-outline-light btn-lg px-4 py-3" style="background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.3); backdrop-filter: blur(10px);">
                                <i class="fas fa-user-plus me-2"></i>สมัครสมาชิกฟรี
                            </a>
                            <a href="?page=login" class="btn btn-outline-light btn-lg px-4 py-3" style="background: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.3); backdrop-filter: blur(10px);">
                                <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Features in Hero -->
                <div class="row g-3">
                    <div class="col-md-4" data-animate="fade-in" style="animation-delay: 0.2s;">
                        <?php if($is_logged_in): ?>
                        <a href="?page=topup" class="text-decoration-none">
                        <?php endif; ?>
                            <div class="card h-100 border-0 <?php echo $is_logged_in ? 'card-hover' : ''; ?>" style="background: rgba(255, 255, 255, 0.15); backdrop-filter: blur(10px); cursor: <?php echo $is_logged_in ? 'pointer' : 'default'; ?>; transition: all 0.3s ease;">
                                <div class="card-body text-center p-3">
                                    <div class="mb-2">
                                        <i class="fas fa-qrcode text-success" style="font-size: 2rem;"></i>
                                    </div>
                                    <h6 class="card-title text-white mb-2">เติมเงินด้วย PromptPay</h6>
                                    <p class="card-text text-light small">เติมเงินง่ายๆ ด้วย QR Code PromptPay</p>
                                </div>
                            </div>
                        <?php if($is_logged_in): ?>
                        </a>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-4" data-animate="fade-in" style="animation-delay: 0.3s;">
                        <?php if($is_logged_in): ?>
                        <a href="?page=dashboard" class="text-decoration-none">
                        <?php endif; ?>
                            <div class="card h-100 border-0 <?php echo $is_logged_in ? 'card-hover' : ''; ?>" style="background: rgba(255, 255, 255, 0.15); backdrop-filter: blur(10px); cursor: <?php echo $is_logged_in ? 'pointer' : 'default'; ?>; transition: all 0.3s ease;">
                                <div class="card-body text-center p-3">
                                    <div class="mb-2">
                                        <img src="icons/emby.png" alt="Emby" style="width: 32px; height: 32px; object-fit: contain;">
                                    </div>
                                    <h6 class="card-title text-white mb-2">Emby Server</h6>
                                    <p class="card-text text-light small">เข้าถึงคอนเทนต์ผ่าน Emby Server</p>
                                </div>
                            </div>
                        <?php if($is_logged_in): ?>
                        </a>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-4" data-animate="fade-in" style="animation-delay: 0.4s;">
                        <?php if($is_logged_in): ?>
                        <a href="?page=dashboard" class="text-decoration-none">
                        <?php endif; ?>
                            <div class="card h-100 border-0 <?php echo $is_logged_in ? 'card-hover' : ''; ?>" style="background: rgba(255, 255, 255, 0.15); backdrop-filter: blur(10px); cursor: <?php echo $is_logged_in ? 'pointer' : 'default'; ?>; transition: all 0.3s ease;">
                                <div class="card-body text-center p-3">
                                    <div class="mb-2">
                                        <img src="icons/jellyfin.png" alt="Jellyfin" style="width: 32px; height: 32px; object-fit: contain;">
                                    </div>
                                    <h6 class="card-title text-white mb-2">Jellyfin Server</h6>
                                    <p class="card-text text-light small">ทางเลือกที่ยอดเยี่ยมด้วย Jellyfin</p>
                                </div>
                            </div>
                        <?php if($is_logged_in): ?>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Hero background slideshow
document.addEventListener('DOMContentLoaded', function() {
    let heroSection = document.querySelector('.hero-section');
    let images = ['banner/avengers.gif', 'banner/avengers2.gif', 'banner/dc.gif'];
    let currentIndex = 0;
    let showDuration = 5000; // 5 seconds per image

    function showNextImage() {
        if (heroSection && images.length > 0) {
            // Show current image
            heroSection.style.backgroundImage = `url('${images[currentIndex]}')`;
            console.log(`Showing image ${currentIndex + 1}/${images.length}: ${images[currentIndex]}`);

            // Move to next image
            currentIndex++;

            // If we've shown all images, reset to start
            if (currentIndex >= images.length) {
                currentIndex = 0;
                console.log('Completed full cycle, starting over...');
            }
        }
    }

    // Show first image immediately
    showNextImage();

    // Change to next image every 5 seconds
    setInterval(showNextImage, showDuration);
});
</script>




<!-- Latest Media Section -->
<section class="py-5 bg-dark text-white">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col">
                <h2 class="fw-bold">หนังและซีรีส์ล่าสุด</h2>
                <p class="text-muted">ตัวอย่างคอนเทนต์ที่คุณจะได้รับชม</p>
            </div>
        </div>

        <div class="row g-3">
            <?php foreach(array_slice($latest_media, 0, 8) as $index => $media): ?>
            <div class="col-6 col-md-3" data-animate="fade-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                <div class="card bg-transparent border-0 text-white">
                    <div class="position-relative">
                        <img src="<?php echo htmlspecialchars($media['poster']); ?>"
                             class="card-img-top rounded"
                             style="height: 300px; object-fit: cover;"
                             alt="<?php echo htmlspecialchars($media['title']); ?>">
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-<?php echo $media['type'] == 'movie' ? 'primary' : 'info'; ?>">
                                <?php echo $media['type'] == 'movie' ? 'หนัง' : 'ซีรีส์'; ?>
                            </span>
                        </div>
                        <div class="position-absolute bottom-0 start-0 end-0 bg-gradient-dark p-3">
                            <h6 class="card-title mb-1"><?php echo htmlspecialchars($media['title']); ?></h6>
                            <small class="text-muted"><?php echo $media['year']; ?></small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>


    </div>
</section>

<!-- Packages Preview -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col">
                <h2 class="fw-bold">แพ็คเกจของเรา</h2>
                <p class="text-muted">เลือกแพ็คเกจที่เหมาะสมกับความต้องการของคุณ</p>
            </div>
        </div>
        
        <?php
        // Get packages for preview
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "SELECT * FROM packages WHERE status = 'active' ORDER BY price ASC LIMIT 3";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $packages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        ?>
        
        <div class="row g-4">
            <?php foreach($packages as $package): ?>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <h5 class="card-title text-primary"><?php echo htmlspecialchars($package['name']); ?></h5>
                        <div class="mb-3">
                            <span class="h2 text-dark"><?php echo number_format($package['price'], 0); ?></span>
                            <span class="text-muted">฿</span>
                        </div>
                        <p class="card-text"><?php echo htmlspecialchars($package['description']); ?></p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i><?php echo $package['duration_days']; ?> วัน</li>
                            <li><i class="fas fa-check text-success me-2"></i><?php echo $package['max_devices']; ?> อุปกรณ์</li>
                            <li><i class="fas fa-check text-success me-2"></i>คุณภาพ <?php echo $package['quality_limit']; ?></li>
                        </ul>
                        <?php if($is_logged_in): ?>
                        <a href="?page=packages" class="btn btn-primary">เลือกแพ็คเกจ</a>
                        <?php else: ?>
                        <a href="?page=register" class="btn btn-outline-primary">สมัครสมาชิก</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <?php if($is_logged_in): ?>
        <div class="text-center mt-4">
            <a href="?page=packages" class="btn btn-primary btn-lg">ดูแพ็คเกจทั้งหมด</a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- CTA Section -->
<?php if(!$is_logged_in): ?>
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row text-center">
            <div class="col">
                <h2 class="fw-bold mb-3">พร้อมเริ่มต้นแล้วหรือยัง?</h2>
                <p class="lead mb-4">สมัครสมาชิกวันนี้และเริ่มใช้งาน Media Server ได้ทันที</p>
                <a href="?page=register" class="btn btn-light btn-lg">สมัครสมาชิกฟรี</a>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>
