-- Media Server Management System Database Schema
-- สำหรับระบบจัดการ Emby และ Jellyfin

CREATE DATABASE IF NOT EXISTS media_server_management;
USE media_server_management;

-- ตารางผู้ใช้งาน
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('admin', 'user') DEFAULT 'user',
    balance DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active'
);

-- ตารางแพ็คเกจ
CREATE TABLE packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration_days INT NOT NULL,
    max_devices INT DEFAULT 1,
    quality_limit VARCHAR(20) DEFAULT '1080p',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- ตารางการสมัครแพ็คเกจของผู้ใช้
CREATE TABLE user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE CASCADE
);

-- ตารางการเติมเงิน
CREATE TABLE top_ups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('promptpay', 'bank_transfer', 'cash') DEFAULT 'promptpay',
    transaction_ref VARCHAR(100),
    qr_code_url VARCHAR(255),
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    approved_by INT NULL,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ตารางบัญชี Emby
CREATE TABLE emby_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    emby_user_id VARCHAR(100),
    emby_username VARCHAR(50) NOT NULL,
    emby_password VARCHAR(255) NOT NULL,
    server_url VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ตารางบัญชี Jellyfin
CREATE TABLE jellyfin_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    jellyfin_user_id VARCHAR(100),
    jellyfin_username VARCHAR(50) NOT NULL,
    jellyfin_password VARCHAR(255) NOT NULL,
    server_url VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ตารางประวัติการใช้งาน
CREATE TABLE usage_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ตารางการตั้งค่าระบบ
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ข้อมูลเริ่มต้น
INSERT INTO packages (name, description, price, duration_days, max_devices, quality_limit) VALUES
('Basic', 'แพ็คเกจพื้นฐาน 1 เดือน', 99.00, 30, 1, '720p'),
('Standard', 'แพ็คเกจมาตรฐาน 1 เดือน', 199.00, 30, 2, '1080p'),
('Premium', 'แพ็คเกจพรีเมียม 1 เดือน', 299.00, 30, 4, '4K'),
('Basic 3M', 'แพ็คเกจพื้นฐาน 3 เดือน', 279.00, 90, 1, '720p'),
('Standard 3M', 'แพ็คเกจมาตรฐาน 3 เดือน', 549.00, 90, 2, '1080p'),
('Premium 3M', 'แพ็คเกจพรีเมียม 3 เดือน', 799.00, 90, 4, '4K');

-- การตั้งค่าระบบเริ่มต้น
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('emby_server_url', 'https://emby.embyjames.xyz', 'URL ของ Emby Server'),
('emby_api_key', 'd2499d0eacfe4ccbba940836be91a9f1', 'API Key สำหรับ Emby'),
('jellyfin_server_url', 'https://jellyfin.embyjames.xyz', 'URL ของ Jellyfin Server'),
('jellyfin_api_key', 'e30753ff625847e0bf7163df609ebaf6', 'API Key สำหรับ Jellyfin'),
('promptpay_phone', '0820722972', 'เบอร์โทรศัพท์ PromptPay'),
('expired_password', 'Wxmujwsofu@1234', 'รหัสผ่านสำหรับบัญชีที่หมดอายุ'),
('site_name', 'Media Server Management', 'ชื่อเว็บไซต์'),
('admin_email', '<EMAIL>', 'อีเมลผู้ดูแลระบบ');

-- สร้าง Admin User เริ่มต้น (password: admin123)
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin');
