<?php
/**
 * Continuous Cron Script for PayNoi API
 * 
 * This script runs continuously and fetches API data every 5 seconds
 * Use this instead of traditional cron for very frequent updates
 */

// Configuration
$config = [
    'api_key' => '6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446',
    'record_key' => '100568',
    'api_url' => 'https://paynoi.com/api_line',
    'log_file' => __DIR__ . '/logs/continuous_cron.log',
    'data_file' => __DIR__ . '/data/transactions.json',
    'interval_seconds' => 5, // Run every 5 seconds
    'max_runtime_hours' => 24, // Maximum runtime in hours (0 = unlimited)
    'memory_limit' => '128M'
];

// Set memory limit
ini_set('memory_limit', $config['memory_limit']);

// Create directories if they don't exist
$directories = [
    dirname($config['log_file']),
    dirname($config['data_file'])
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

/**
 * Log message with timestamp
 */
function logMessage($message, $level = 'INFO') {
    global $config;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    
    // Write to log file
    file_put_contents($config['log_file'], $logEntry, FILE_APPEND | LOCK_EX);
    
    // Also output to console if running from command line
    if (php_sapi_name() === 'cli') {
        echo $logEntry;
    }
}

/**
 * Fetch data from PayNoi API
 */
function fetchApiData() {
    global $config;
    
    // Build API URL with parameters
    $url = $config['api_url'] . '?' . http_build_query([
        'api_key' => $config['api_key'],
        'record_key' => $config['record_key']
    ]);
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_USERAGENT => 'PayNoi-Continuous-Fetcher/1.0',
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Check for cURL errors
    if ($response === false) {
        logMessage("cURL Error: $error", 'ERROR');
        return false;
    }
    
    // Check HTTP status code
    if ($httpCode !== 200) {
        logMessage("HTTP Error: Received status code $httpCode", 'ERROR');
        return false;
    }
    
    // Parse JSON response
    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        logMessage("JSON Parse Error: " . json_last_error_msg(), 'ERROR');
        return false;
    }
    
    // Check API response status
    if (!isset($data['status']) || $data['status'] !== 'success') {
        $status = $data['status'] ?? 'unknown';
        logMessage("API Error: Status is '$status'", 'ERROR');
        return false;
    }
    
    return $data;
}

/**
 * Process and save transaction data
 */
function processTransactionData($apiData) {
    global $config;
    
    $transactions = [];
    $totalTransactions = 0;
    
    foreach ($apiData['data'] as $transaction) {
        $transactions[] = [
            'trans_id' => $transaction['trans_id'] ?? null,
            'bankaccount' => $transaction['bankaccount'] ?? '',
            'amount' => $transaction['amount'] ?? 0,
            'currency' => $transaction['currency'] ?? '',
            'date' => $transaction['date'] ?? '',
            'balance' => $transaction['balance'] ?? 0,
            'type' => $transaction['type'] ?? '',
            'fetched_at' => date('Y-m-d H:i:s')
        ];
        $totalTransactions++;
    }
    
    // Save to JSON file
    $saveData = [
        'last_updated' => date('Y-m-d H:i:s'),
        'total_transactions' => $totalTransactions,
        'transactions' => $transactions
    ];
    
    $jsonData = json_encode($saveData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if (file_put_contents($config['data_file'], $jsonData, LOCK_EX) === false) {
        logMessage("Failed to save transaction data to file", 'ERROR');
        return false;
    }
    
    return true;
}

/**
 * Check if we should continue running
 */
function shouldContinue($startTime) {
    global $config;
    
    // Check maximum runtime
    if ($config['max_runtime_hours'] > 0) {
        $elapsedHours = (time() - $startTime) / 3600;
        if ($elapsedHours >= $config['max_runtime_hours']) {
            logMessage("Maximum runtime of {$config['max_runtime_hours']} hours reached", 'INFO');
            return false;
        }
    }
    
    // Check memory usage
    $memoryUsage = memory_get_usage(true);
    $memoryLimit = ini_get('memory_limit');
    $memoryLimitBytes = return_bytes($memoryLimit);
    
    if ($memoryUsage > ($memoryLimitBytes * 0.9)) {
        logMessage("Memory usage approaching limit: " . formatBytes($memoryUsage) . " / $memoryLimit", 'WARNING');
    }
    
    return true;
}

/**
 * Convert memory limit string to bytes
 */
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}

/**
 * Format bytes to human readable format
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Signal handler for graceful shutdown
 */
function signalHandler($signal) {
    global $running;
    logMessage("Received signal $signal, shutting down gracefully...", 'INFO');
    $running = false;
}

// Set up signal handlers for graceful shutdown
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGTERM, 'signalHandler');
    pcntl_signal(SIGINT, 'signalHandler');
}

/**
 * Main continuous loop
 */
function main() {
    global $config, $running;
    
    $running = true;
    $startTime = time();
    $successCount = 0;
    $errorCount = 0;
    
    logMessage("=== Continuous Cron Started ===", 'INFO');
    logMessage("Interval: {$config['interval_seconds']} seconds", 'INFO');
    logMessage("Max runtime: " . ($config['max_runtime_hours'] > 0 ? $config['max_runtime_hours'] . " hours" : "unlimited"), 'INFO');
    
    while ($running && shouldContinue($startTime)) {
        $cycleStart = microtime(true);
        
        try {
            // Process signals
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }
            
            // Fetch data from API
            $apiData = fetchApiData();
            if ($apiData !== false) {
                // Process and save data
                if (processTransactionData($apiData)) {
                    $successCount++;
                    logMessage("Cycle #$successCount: Successfully processed " . count($apiData['data']) . " transactions", 'INFO');
                } else {
                    $errorCount++;
                    logMessage("Cycle failed: Could not save data", 'ERROR');
                }
            } else {
                $errorCount++;
                logMessage("Cycle failed: Could not fetch API data", 'ERROR');
            }
            
        } catch (Exception $e) {
            $errorCount++;
            logMessage("Cycle failed: " . $e->getMessage(), 'ERROR');
        }
        
        // Calculate sleep time
        $cycleTime = microtime(true) - $cycleStart;
        $sleepTime = max(0, $config['interval_seconds'] - $cycleTime);
        
        if ($sleepTime > 0) {
            usleep((int)($sleepTime * 1000000)); // Convert to microseconds
        }
        
        // Log statistics every 60 cycles (5 minutes at 5-second intervals)
        if (($successCount + $errorCount) % 60 === 0) {
            $totalCycles = $successCount + $errorCount;
            $uptime = time() - $startTime;
            $memoryUsage = formatBytes(memory_get_usage(true));
            logMessage("Statistics: $totalCycles cycles, $successCount success, $errorCount errors, uptime: {$uptime}s, memory: $memoryUsage", 'INFO');
        }
    }
    
    $totalCycles = $successCount + $errorCount;
    $uptime = time() - $startTime;
    logMessage("=== Continuous Cron Stopped ===", 'INFO');
    logMessage("Final statistics: $totalCycles cycles, $successCount success, $errorCount errors, uptime: {$uptime}s", 'INFO');
    
    return 0;
}

// Execute main function if script is run directly
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    exit(main());
}
?>
