-- สร้างตาราง user_subscriptions ถ้ายังไม่มี

CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'inactive', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_package_id (package_id),
    INDEX idx_status (status),
    INDEX idx_end_date (end_date)
);

-- เพิ่มข้อมูลตัวอย่าง (ถ้าต้องการ)
-- INSERT INTO user_subscriptions (user_id, package_id, start_date, end_date, status) 
-- VALUES 
-- (1, 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'active'),
-- (2, 2, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'active');

-- ตรวจสอบตาราง
DESCRIBE user_subscriptions;

-- แสดงข้อมูลในตาราง
SELECT COUNT(*) as total_subscriptions FROM user_subscriptions;
