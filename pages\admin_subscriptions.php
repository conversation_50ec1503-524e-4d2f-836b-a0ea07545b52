<?php
// Session already started in index.php

// ตรวจสอบสิทธิ์แอดมิน
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ?page=login');
    exit();
}

require_once 'config/db_config.php';
require_once 'classes/SubscriptionManager.php';

// Initialize variables
$message = '';
$error_message = '';

// Database connection
$database = new Database();
$conn = $database->getConnection();

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!$conn) {
    $error_message = "ไม่สามารถเชื่อมต่อฐานข้อมูลได้";
} else {
    // ตรวจสอบว่าตาราง user_subscriptions มีอยู่หรือไม่
    try {
        $check_table = "SHOW TABLES LIKE 'user_subscriptions'";
        $stmt = $conn->prepare($check_table);
        $stmt->execute();
        if ($stmt->rowCount() == 0) {
            $error_message = "ไม่พบตาราง user_subscriptions กรุณารัน SQL: sql/create_user_subscriptions_table.sql";
        }
    } catch (Exception $e) {
        $error_message = "ข้อผิดพลาดในการตรวจสอบตาราง: " . $e->getMessage();
    }
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['subscription_id']) && isset($_POST['new_status']) && $conn) {
    $subscription_id = intval($_POST['subscription_id']);
    $new_status = trim($_POST['new_status']);
    $user_id = intval($_POST['user_id']);

    if (empty($subscription_id) || empty($new_status) || empty($user_id)) {
        $error_message = "ข้อมูลไม่ครบถ้วน";
    } else {
        try {
            $conn->beginTransaction();

            // ตรวจสอบว่า subscription มีอยู่จริงหรือไม่
            $check_query = "SELECT * FROM user_subscriptions WHERE id = :id";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bindParam(':id', $subscription_id);
            $check_stmt->execute();
            $existing_sub = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$existing_sub) {
                throw new Exception("ไม่พบ subscription ID: $subscription_id");
            }

            // Update subscription status
            $query = "UPDATE user_subscriptions SET status = :status, updated_at = NOW() WHERE id = :id";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':status', $new_status);
            $stmt->bindParam(':id', $subscription_id);
            $result = $stmt->execute();

            if (!$result) {
                throw new Exception("ไม่สามารถอัปเดตสถานะในฐานข้อมูลได้");
            }

            $affected_rows = $stmt->rowCount();
            if ($affected_rows == 0) {
                // ตรวจสอบว่าสถานะเหมือนเดิมหรือไม่
                $check_stmt->execute();
                $updated_sub = $check_stmt->fetch(PDO::FETCH_ASSOC);
                if ($updated_sub['status'] != $new_status) {
                    throw new Exception("ไม่สามารถอัปเดตสถานะได้");
                }
            }

            // Handle media server accounts based on new status
            $subscription_manager = new SubscriptionManager();

            if ($new_status == 'expired') {
                // Disable users when subscription expires
                $disable_result = $subscription_manager->disableUsersForExpiredSubscription($user_id);
                if ($disable_result['success']) {
                    error_log("Admin: Successfully disabled users for expired subscription - User ID: $user_id");
                } else {
                    error_log("Admin: Failed to disable users for expired subscription - User ID: $user_id");
                }
            } elseif ($new_status == 'active') {
                // Enable users when subscription becomes active
                $enable_result = $subscription_manager->enableUsersForRenewedSubscription($user_id);
                if ($enable_result['success']) {
                    error_log("Admin: Successfully enabled users for active subscription - User ID: $user_id");
                } else {
                    error_log("Admin: Failed to enable users for active subscription - User ID: $user_id");
                }
            }

            $conn->commit();
            $message = "อัปเดตสถานะ subscription สำเร็จ";

        } catch (Exception $e) {
            if ($conn) {
                $conn->rollback();
            }
            $error_message = "เกิดข้อผิดพลาด: " . $e->getMessage();
        }
    }
}

// Get all subscriptions with user and package details
$subscriptions = [];
if ($conn && empty($error_message)) {
    try {
        $query = "SELECT us.*, u.username, u.full_name, u.email, p.name as package_name, p.price, p.duration_days
                  FROM user_subscriptions us
                  JOIN users u ON us.user_id = u.id
                  JOIN packages p ON us.package_id = p.id
                  ORDER BY us.created_at DESC";

        $stmt = $conn->prepare($query);
        $stmt->execute();
        $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        $error_message = "ข้อผิดพลาดในการดึงข้อมูล subscriptions: " . $e->getMessage();
    }
}

// Get statistics
$stats = ['total' => 0, 'active' => 0, 'inactive' => 0, 'expired' => 0, 'cancelled' => 0];
if ($conn && empty($error_message)) {
    try {
        $stats_query = "SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive,
                        SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                        FROM user_subscriptions";
        $stats_stmt = $conn->prepare($stats_query);
        $stats_stmt->execute();
        $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $error_message = "ข้อผิดพลาดในการดึงสถิติ: " . $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col">
            <h2><i class="fas fa-calendar-alt me-2"></i>จัดการ User Subscriptions</h2>
            <p class="text-muted">จัดการสถานะการสมัครแพ็คเกจของผู้ใช้</p>
        </div>
    </div>



    <!-- Messages -->
    <?php if($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        <?php if(strpos($error_message, 'user_subscriptions') !== false): ?>
        <hr>
        <small>
            <strong>วิธีแก้:</strong><br>
            1. รัน SQL: <code>sql/create_user_subscriptions_table.sql</code><br>
            2. หรือรัน SQL: <code>sql/add_inactive_status.sql</code>
        </small>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['total']; ?></h4>
                            <p class="mb-0">รวมทั้งหมด</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['active']; ?></h4>
                            <p class="mb-0">ใช้งานอยู่</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['inactive']; ?></h4>
                            <p class="mb-0">ไม่ใช้งาน</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pause-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['expired']; ?></h4>
                            <p class="mb-0">หมดอายุ</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscriptions Table -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>รายการ Subscriptions ทั้งหมด</h5>
                </div>
                <div class="card-body">
                    <?php if(empty($subscriptions)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">ไม่มี Subscription</h5>
                        <p class="text-muted">ยังไม่มีผู้ใช้สมัครแพ็คเกจ</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>ผู้ใช้</th>
                                    <th>แพ็คเกจ</th>
                                    <th>วันที่เริ่ม</th>
                                    <th>วันที่สิ้นสุด</th>
                                    <th>สถานะ</th>
                                    <th>การจัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($subscriptions as $sub): ?>
                                <tr>
                                    <td><?php echo $sub['id']; ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($sub['username']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($sub['full_name']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($sub['package_name']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo number_format($sub['price'], 2); ?> ฿ / <?php echo $sub['duration_days']; ?> วัน</small>
                                        </div>
                                    </td>
                                    <td><?php echo date('d/m/Y', strtotime($sub['start_date'])); ?></td>
                                    <td>
                                        <?php 
                                        $end_date = strtotime($sub['end_date']);
                                        $today = strtotime('today');
                                        $days_left = ceil(($end_date - $today) / (60 * 60 * 24));
                                        ?>
                                        <?php echo date('d/m/Y', $end_date); ?>
                                        <br>
                                        <small class="<?php echo $days_left > 0 ? 'text-success' : 'text-danger'; ?>">
                                            <?php echo $days_left > 0 ? "เหลือ {$days_left} วัน" : "หมดอายุแล้ว"; ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch($sub['status']) {
                                            case 'active':
                                                $status_class = 'bg-success';
                                                $status_text = 'ใช้งานอยู่';
                                                break;
                                            case 'inactive':
                                                $status_class = 'bg-warning';
                                                $status_text = 'ไม่ใช้งาน';
                                                break;
                                            case 'expired':
                                                $status_class = 'bg-danger';
                                                $status_text = 'หมดอายุ';
                                                break;
                                            case 'cancelled':
                                                $status_class = 'bg-secondary';
                                                $status_text = 'ยกเลิก';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                    <td>
                                        <!-- ปุ่มธรรมดาแทน dropdown -->
                                        <div class="btn-group-vertical" style="gap: 2px;">
                                            <?php if($sub['status'] != 'active'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="subscription_id" value="<?php echo $sub['id']; ?>">
                                                <input type="hidden" name="user_id" value="<?php echo $sub['user_id']; ?>">
                                                <input type="hidden" name="new_status" value="active">
                                                <button type="submit" name="update_status" class="btn btn-sm btn-success"
                                                        onclick="return confirm('เปลี่ยนสถานะเป็น ใช้งานอยู่ หรือไม่?')">
                                                    <i class="fas fa-check-circle me-1"></i>Active
                                                </button>
                                            </form>
                                            <?php endif; ?>

                                            <?php if($sub['status'] != 'inactive'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="subscription_id" value="<?php echo $sub['id']; ?>">
                                                <input type="hidden" name="user_id" value="<?php echo $sub['user_id']; ?>">
                                                <input type="hidden" name="new_status" value="inactive">
                                                <button type="submit" name="update_status" class="btn btn-sm btn-warning"
                                                        onclick="return confirm('เปลี่ยนสถานะเป็น ไม่ใช้งาน หรือไม่?')">
                                                    <i class="fas fa-pause-circle me-1"></i>Inactive
                                                </button>
                                            </form>
                                            <?php endif; ?>

                                            <?php if($sub['status'] != 'expired'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="subscription_id" value="<?php echo $sub['id']; ?>">
                                                <input type="hidden" name="user_id" value="<?php echo $sub['user_id']; ?>">
                                                <input type="hidden" name="new_status" value="expired">
                                                <button type="submit" name="update_status" class="btn btn-sm btn-danger"
                                                        onclick="return confirm('เปลี่ยนสถานะเป็น หมดอายุ หรือไม่?')">
                                                    <i class="fas fa-times-circle me-1"></i>Expired
                                                </button>
                                            </form>
                                            <?php endif; ?>

                                            <?php if($sub['status'] != 'cancelled'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="subscription_id" value="<?php echo $sub['id']; ?>">
                                                <input type="hidden" name="user_id" value="<?php echo $sub['user_id']; ?>">
                                                <input type="hidden" name="new_status" value="cancelled">
                                                <button type="submit" name="update_status" class="btn btn-sm btn-secondary"
                                                        onclick="return confirm('เปลี่ยนสถานะเป็น ยกเลิก หรือไม่?')">
                                                    <i class="fas fa-ban me-1"></i>Cancelled
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn-group-vertical {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 100%;
}

.btn-group-vertical .btn {
    margin-bottom: 2px;
    border-radius: 0.25rem !important;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

.btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
</style>


