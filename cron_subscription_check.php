<?php
/**
 * <PERSON>ron Job สำหรับตรวจสอบและจัดการ subscription status
 * ควรรันทุก 1 ชั่วโมง หรือตามความเหมาะสม
 * 
 * Cron command: 0 * * * * /usr/bin/php /path/to/your/project/cron_subscription_check.php
 */

// Prevent direct web access
if(isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

require_once __DIR__ . '/config/db_config.php';
require_once __DIR__ . '/classes/SubscriptionManager.php';

echo "[" . date('Y-m-d H:i:s') . "] Starting subscription check...\n";

try {
    $subscriptionManager = new SubscriptionManager();
    
    // Check for expired subscriptions
    echo "[" . date('Y-m-d H:i:s') . "] Checking expired subscriptions...\n";
    $expired_result = $subscriptionManager->checkExpiredSubscriptions();
    
    if($expired_result['success']) {
        echo "[" . date('Y-m-d H:i:s') . "] Processed " . $expired_result['processed'] . " expired subscriptions\n";
        
        if($expired_result['processed'] > 0) {
            foreach($expired_result['results'] as $result) {
                $status = $result['result']['success'] ? 'SUCCESS' : 'FAILED';
                echo "  - User ID {$result['user_id']} ({$result['username']}): {$status}\n";
                
                if(isset($result['result']['remote_access_disabled'])) {
                    $remote_status = $result['result']['remote_access_disabled'];
                    echo "    Remote access disabled - Emby: " . ($remote_status['emby'] ?? 'N/A') . 
                         ", Jellyfin: " . ($remote_status['jellyfin'] ?? 'N/A') . "\n";
                }
            }
        }
    } else {
        echo "[" . date('Y-m-d H:i:s') . "] ERROR checking expired subscriptions: " . $expired_result['message'] . "\n";
    }
    
    // Check for inactive subscriptions and disable remote access
    echo "[" . date('Y-m-d H:i:s') . "] Checking inactive subscriptions...\n";
    $inactive_result = $subscriptionManager->checkInactiveSubscriptions();

    if($inactive_result['success']) {
        echo "[" . date('Y-m-d H:i:s') . "] Processed " . $inactive_result['processed'] . " inactive subscriptions\n";

        if($inactive_result['processed'] > 0) {
            foreach($inactive_result['results'] as $result) {
                $status = $result['result']['success'] ? 'SUCCESS' : 'FAILED';
                echo "  - User ID {$result['user_id']} ({$result['username']}): Remote Access Disabled - {$status}\n";

                if(isset($result['result']['remote_access_disabled'])) {
                    $remote_status = $result['result']['remote_access_disabled'];
                    echo "    Remote access disabled - Emby: " . ($remote_status['emby'] ?? 'N/A') .
                         ", Jellyfin: " . ($remote_status['jellyfin'] ?? 'N/A') . "\n";
                }
            }
        }
    } else {
        echo "[" . date('Y-m-d H:i:s') . "] ERROR checking inactive subscriptions: " . $inactive_result['message'] . "\n";
    }

    // Check for renewed subscriptions
    echo "[" . date('Y-m-d H:i:s') . "] Checking renewed subscriptions...\n";
    $renewed_result = $subscriptionManager->checkRenewedSubscriptions();
    
    if($renewed_result['success']) {
        echo "[" . date('Y-m-d H:i:s') . "] Processed " . $renewed_result['processed'] . " renewed subscriptions\n";
        
        if($renewed_result['processed'] > 0) {
            foreach($renewed_result['results'] as $result) {
                $status = $result['result']['success'] ? 'SUCCESS' : 'FAILED';
                echo "  - User ID {$result['user_id']} ({$result['username']}): {$status}\n";
                
                if(isset($result['result']['remote_access_enabled'])) {
                    $remote_status = $result['result']['remote_access_enabled'];
                    echo "    Remote access enabled - Emby: " . ($remote_status['emby'] ?? 'N/A') . 
                         ", Jellyfin: " . ($remote_status['jellyfin'] ?? 'N/A') . "\n";
                }
            }
        }
    } else {
        echo "[" . date('Y-m-d H:i:s') . "] ERROR checking renewed subscriptions: " . $renewed_result['message'] . "\n";
    }
    
    // Summary
    $total_processed = $expired_result['processed'] + $renewed_result['processed'];
    echo "[" . date('Y-m-d H:i:s') . "] Subscription check completed. Total processed: {$total_processed}\n";
    
    // Log to database
    $query = "INSERT INTO system_logs (action, description, created_at) 
              VALUES ('subscription_check', :description, NOW())";
    
    $stmt = $conn->prepare($query);
    $description = "Subscription check completed - Expired: {$expired_result['processed']}, Renewed: {$renewed_result['processed']}";
    $stmt->bindParam(':description', $description);
    $stmt->execute();
    
} catch(Exception $e) {
    $error_message = "[" . date('Y-m-d H:i:s') . "] FATAL ERROR: " . $e->getMessage() . "\n";
    echo $error_message;
    
    // Log error to database
    try {
        $query = "INSERT INTO system_logs (action, description, created_at) 
                  VALUES ('subscription_check_error', :description, NOW())";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':description', $error_message);
        $stmt->execute();
    } catch(Exception $log_error) {
        echo "[" . date('Y-m-d H:i:s') . "] Failed to log error to database: " . $log_error->getMessage() . "\n";
    }
    
    exit(1);
}

echo "[" . date('Y-m-d H:i:s') . "] Script completed successfully.\n";
exit(0);
?>
