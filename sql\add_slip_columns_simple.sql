-- Simple version: Add columns to topups table
-- Run these one by one, ignore errors if columns already exist

-- Add slip_data column
ALTER TABLE top_ups
ADD COLUMN slip_data TEXT COMMENT 'JSON data from slip verification API';

-- Add approved_at column
ALTER TABLE top_ups
ADD COLUMN approved_at TIMESTAMP NULL COMMENT 'When the topup was approved';

-- Create processed_transactions table
CREATE TABLE IF NOT EXISTS processed_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trans_id VARCHAR(255) NOT NULL UNIQUE,
    topup_id INT,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_trans_id (trans_id),
    INDEX idx_topup_id (topup_id),
    INDEX idx_processed_at (processed_at),
    
    FOREIGN KEY (topup_id) REFERENCES top_ups(id) ON DELETE SET NULL
);
