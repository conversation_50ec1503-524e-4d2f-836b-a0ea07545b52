<?php
// Session already started in index.php

// ตรวจสอบสิทธิ์แอดมิน
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo "<h1>❌ Access Denied</h1>";
    echo "<p>คุณต้องเป็น admin เพื่อเข้าหน้านี้</p>";
    echo "<p><a href='?page=login'>เข้าสู่ระบบ</a></p>";
    exit();
}

echo "<h1>🕐 ทดสอบเวลาไทย</h1>";

// ทดสอบ PHP timezone
echo "<h2>📅 PHP Timezone</h2>";
echo "<p><strong>Current timezone:</strong> " . date_default_timezone_get() . "</p>";
echo "<p><strong>Current time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Current timestamp:</strong> " . time() . "</p>";

// ทดสอบ MySQL timezone
echo "<h2>🗄️ MySQL Timezone</h2>";
require_once 'config/db_config.php';
$database = new Database();
$conn = $database->getConnection();

if ($conn) {
    try {
        // ตรวจสอบ timezone ของ MySQL
        $query = "SELECT @@global.time_zone as global_tz, @@session.time_zone as session_tz, NOW() as mysql_time";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p><strong>MySQL Global Timezone:</strong> " . $result['global_tz'] . "</p>";
        echo "<p><strong>MySQL Session Timezone:</strong> " . $result['session_tz'] . "</p>";
        echo "<p><strong>MySQL Current Time:</strong> " . $result['mysql_time'] . "</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ ข้อผิดพลาด MySQL: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</p>";
}

// ทดสอบการซื้อ package (simulation)
echo "<h2>🛒 ทดสอบการซื้อ Package</h2>";

if (isset($_POST['test_purchase'])) {
    echo "<h3>📊 ผลการทดสอบ:</h3>";
    
    // Set timezone
    date_default_timezone_set('Asia/Bangkok');
    
    $start_date = date('Y-m-d');
    $duration_days = 30;
    $end_date = date('Y-m-d', strtotime("+{$duration_days} days"));
    
    echo "<p><strong>Start Date:</strong> $start_date</p>";
    echo "<p><strong>Duration:</strong> $duration_days days</p>";
    echo "<p><strong>End Date:</strong> $end_date</p>";
    echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
    
    // ทดสอบบันทึกลงฐานข้อมูล
    if ($conn) {
        try {
            $test_query = "SELECT NOW() as db_time, CURDATE() as db_date";
            $test_stmt = $conn->prepare($test_query);
            $test_stmt->execute();
            $test_result = $test_stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Database Time:</strong> " . $test_result['db_time'] . "</p>";
            echo "<p><strong>Database Date:</strong> " . $test_result['db_date'] . "</p>";
            
        } catch (Exception $e) {
            echo "<p>❌ ข้อผิดพลาดในการทดสอบฐานข้อมูล: " . $e->getMessage() . "</p>";
        }
    }
}

// ทดสอบ PaymentManager
echo "<h2>💳 ทดสอบ PaymentManager</h2>";
try {
    require_once 'classes/PaymentManager.php';

    // สร้าง instance
    $paymentManager = new PaymentManager();
    echo "<p>✅ สร้าง PaymentManager สำเร็จ</p>";

    // ทดสอบการคำนวณวันที่
    date_default_timezone_set('Asia/Bangkok');
    $test_start = date('Y-m-d');
    $test_duration = 30;
    $test_end = date('Y-m-d', strtotime("+{$test_duration} days"));

    echo "<p><strong>Test Start Date:</strong> $test_start</p>";
    echo "<p><strong>Test End Date:</strong> $test_end</p>";
    echo "<p><strong>Current Thailand Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

    // ทดสอบการเชื่อมต่อฐานข้อมูลของ PaymentManager
    if ($conn) {
        try {
            $timezone_test = "SELECT @@session.time_zone as pm_timezone, NOW() as pm_time";
            $tz_stmt = $conn->prepare($timezone_test);
            $tz_stmt->execute();
            $tz_result = $tz_stmt->fetch(PDO::FETCH_ASSOC);

            echo "<p><strong>PaymentManager DB Timezone:</strong> " . $tz_result['pm_timezone'] . "</p>";
            echo "<p><strong>PaymentManager DB Time:</strong> " . $tz_result['pm_time'] . "</p>";

        } catch (Exception $e) {
            echo "<p>⚠️ ไม่สามารถทดสอบ timezone ของ PaymentManager: " . $e->getMessage() . "</p>";
        }
    }

} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาด PaymentManager: " . $e->getMessage() . "</p>";
}

// แสดงข้อมูล subscriptions ล่าสุด
echo "<h2>📋 Subscriptions ล่าสุด</h2>";
if ($conn) {
    try {
        $query = "SELECT us.*, u.username, p.name as package_name,
                         us.created_at, us.updated_at
                  FROM user_subscriptions us
                  JOIN users u ON us.user_id = u.id
                  JOIN packages p ON us.package_id = p.id
                  ORDER BY us.created_at DESC
                  LIMIT 5";
        
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($subscriptions)) {
            echo "<p>❌ ไม่มี subscriptions</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>User</th>";
            echo "<th style='padding: 8px;'>Package</th>";
            echo "<th style='padding: 8px;'>Start Date</th>";
            echo "<th style='padding: 8px;'>End Date</th>";
            echo "<th style='padding: 8px;'>Created At</th>";
            echo "<th style='padding: 8px;'>Updated At</th>";
            echo "</tr>";
            
            foreach ($subscriptions as $sub) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$sub['id']}</td>";
                echo "<td style='padding: 8px;'>{$sub['username']}</td>";
                echo "<td style='padding: 8px;'>{$sub['package_name']}</td>";
                echo "<td style='padding: 8px;'>{$sub['start_date']}</td>";
                echo "<td style='padding: 8px;'>{$sub['end_date']}</td>";
                echo "<td style='padding: 8px;'>{$sub['created_at']}</td>";
                echo "<td style='padding: 8px;'>{$sub['updated_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ ข้อผิดพลาดในการดึงข้อมูล subscriptions: " . $e->getMessage() . "</p>";
    }
}

// ทดสอบการสร้าง timestamp
if (isset($_POST['test_timestamp'])) {
    echo "<h3>🕐 ทดสอบการสร้าง Timestamp</h3>";

    date_default_timezone_set('Asia/Bangkok');
    $thailand_time = date('Y-m-d H:i:s');

    echo "<p><strong>PHP Thailand Time:</strong> $thailand_time</p>";

    if ($conn) {
        try {
            // ทดสอบการ insert ด้วยเวลาไทย
            $test_insert = "INSERT INTO usage_logs (user_id, action, description, created_at)
                           VALUES (1, 'timezone_test', 'Testing Thailand timezone', :created_at)";
            $insert_stmt = $conn->prepare($test_insert);
            $insert_stmt->bindParam(':created_at', $thailand_time);

            if ($insert_stmt->execute()) {
                $log_id = $conn->lastInsertId();
                echo "<p>✅ บันทึก test log สำเร็จ (ID: $log_id)</p>";

                // ดึงข้อมูลที่เพิ่งบันทึก
                $select_test = "SELECT created_at FROM usage_logs WHERE id = :id";
                $select_stmt = $conn->prepare($select_test);
                $select_stmt->bindParam(':id', $log_id);
                $select_stmt->execute();
                $saved_record = $select_stmt->fetch(PDO::FETCH_ASSOC);

                echo "<p><strong>Saved Time in DB:</strong> " . $saved_record['created_at'] . "</p>";

                // ลบ test record
                $delete_test = "DELETE FROM usage_logs WHERE id = :id";
                $delete_stmt = $conn->prepare($delete_test);
                $delete_stmt->bindParam(':id', $log_id);
                $delete_stmt->execute();
                echo "<p>🗑️ ลบ test record แล้ว</p>";

            } else {
                echo "<p>❌ ไม่สามารถบันทึก test log ได้</p>";
            }

        } catch (Exception $e) {
            echo "<p>❌ ข้อผิดพลาดในการทดสอบ timestamp: " . $e->getMessage() . "</p>";
        }
    }
}

// ฟอร์มทดสอบ
echo "<h2>🧪 ทดสอบระบบ</h2>";
echo "<form method='POST' style='margin-bottom: 10px;'>";
echo "<button type='submit' name='test_purchase' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;'>ทดสอบการซื้อ Package</button>";
echo "</form>";

echo "<form method='POST'>";
echo "<button type='submit' name='test_timestamp' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>ทดสอบ Timestamp</button>";
echo "</form>";

echo "<hr>";
echo "<h3>🔗 ลิงก์</h3>";
echo "<ul>";
echo "<li><a href='?page=admin_subscriptions'>กลับหน้า Admin Subscriptions</a></li>";
echo "<li><a href='?page=packages'>หน้าซื้อ Package</a></li>";
echo "<li><a href='?page=test_timezone'>รีเฟรชหน้านี้</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>⚠️ ไฟล์นี้เป็นไฟล์ทดสอบ ให้ลบออกหลังใช้เสร็จ</small></p>";
?>
