@echo off
REM Slow Cron Runner for XAMPP - รันทุก 30 นาที

echo ========================================
echo Slow Cron Job - XAMPP Version
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Set XAMPP PHP path
set PHP_PATH=C:\xampp\php\php.exe

REM Check if XAMPP PHP exists
if not exist "%PHP_PATH%" (
    echo ERROR: XAMPP PHP not found at %PHP_PATH%
    echo.
    echo Please check if XAMPP is installed or update the PHP_PATH
    echo.
    pause
    exit /b 1
)

echo Using PHP: %PHP_PATH%
echo.

REM Create logs directory if not exists
if not exist "logs" mkdir logs

REM Run the slow cron script
"%PHP_PATH%" slow_cron.php

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED SUCCESSFULLY
    echo Time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED WITH ERRORS
    echo Exit Code: %ERRORLEVEL%
    echo Time: %date% %time%
    echo Check logs/slow_cron.log for details
    echo ========================================
)

REM Keep window open for 3 seconds if run manually
timeout /t 3 /nobreak >nul

exit /b %ERRORLEVEL%
