<?php
class JellyfinAPI {
    private $server_url;
    private $api_key;
    
    public function __construct($server_url, $api_key) {
        $this->server_url = rtrim($server_url, '/');
        $this->api_key = $api_key;
    }
    
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->server_url . $endpoint;

        $headers = [
            'X-Emby-Token: ' . $this->api_key,
            'Authorization: MediaBrowser Token="' . $this->api_key . '"',
            'Content-Type: application/json'
        ];

        // Check if cURL is available
        if(function_exists('curl_init')) {
            return $this->makeRequestWithCurl($url, $method, $data, $headers, $endpoint);
        } else {
            return $this->makeRequestWithFileGetContents($url, $method, $data, $headers, $endpoint);
        }
    }

    private function makeRequestWithCurl($url, $method, $data, $headers, $endpoint = '') {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        switch($method) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if($response === false) {
            throw new Exception('cURL Error: ' . curl_error($ch));
        }

        $decoded = json_decode($response, true);

        // Log API calls for debugging (only for policy-related endpoints)
        if(!empty($endpoint) && strpos($endpoint, '/Policy') !== false) {
            error_log('Jellyfin API Debug: ' . $method . ' ' . $endpoint .
                     ' - HTTP ' . $http_code .
                     ' - Response: ' . substr($response, 0, 200));
        }

        return [
            'success' => $http_code >= 200 && $http_code < 300,
            'http_code' => $http_code,
            'data' => $decoded,
            'raw' => $response,
            'message' => $http_code >= 400 ? 'HTTP Error ' . $http_code : null
        ];
    }

    private function makeRequestWithFileGetContents($url, $method, $data, $headers, $endpoint = '') {
        // For now, return a mock response when cURL is not available
        return [
            'success' => false,
            'http_code' => 500,
            'data' => null,
            'raw' => '',
            'error' => 'cURL extension not available. Please enable cURL in PHP.'
        ];
    }
    
    public function createUser($username, $password, $name = '') {
        // Try to create user with password first
        $data = [
            'Name' => $username,
            'Password' => $password
        ];

        if($name) {
            $data['Name'] = $name;
        }

        error_log('Jellyfin: Creating user ' . $username . ' with password');
        $result = $this->makeRequest('/Users/<USER>', 'POST', $data);

        // If creating with password fails, try without password then set it
        if(!$result['success']) {
            error_log('Jellyfin: Creating user with password failed, trying without password');
            $dataWithoutPassword = [
                'Name' => $username
            ];

            if($name) {
                $dataWithoutPassword['Name'] = $name;
            }

            $result = $this->makeRequest('/Users/<USER>', 'POST', $dataWithoutPassword);
        }

        // Set initial user policy and password if user creation successful
        if($result['success'] && isset($result['data']['Id'])) {
            $userId = $result['data']['Id'];

            // Set password if provided and not already set
            $passwordSet = false;
            if(!empty($password)) {
                error_log('Jellyfin: Attempting to set password for user ' . $username . ' (ID: ' . $userId . ')');
                $passwordResult = $this->updateUserPassword($userId, $password);
                if($passwordResult['success']) {
                    error_log('Jellyfin: Successfully set password for user ' . $username);
                    $passwordSet = true;
                } else {
                    error_log('Jellyfin: Failed to set password for user ' . $username . ': ' . ($passwordResult['message'] ?? 'Unknown error'));
                }
            }

            // Set initial user policy with remote access enabled
            $this->setUserPolicyWithRemoteAccess($userId);

            // Return enhanced result
            return [
                'success' => true,
                'data' => $result['data'],
                'message' => 'User created successfully',
                'password_set' => $passwordSet,
                'user_id' => $userId
            ];
        }

        return $result;
    }

    /**
     * ตั้งค่า user policy สำหรับผู้ใช้ใหม่ พร้อมเปิด remote access
     */
    private function setUserPolicyWithRemoteAccess($userId) {
        error_log('JellyfinAPI: Setting user policy with remote access enabled for user ' . $userId);

        try {
            // Get current policy
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];
            } else {
                // Create default policy if none exists
                $policy = [];
            }

            // Set policy settings with user disabled initially until package is assigned
            $policy['IsAdministrator'] = false;
            $policy['IsDisabled'] = true;  // Checked "Disable This user" for new users
            $policy['IsHidden'] = true;    // ซ่อนจากหน้า login เพื่อความปลอดภัย
            $policy['IsHiddenRemotely'] = true;  // ซ่อนจาก remote login
            $policy['IsHiddenFromUnusedDevices'] = true;  // ซ่อนจาก unused devices
            $policy['EnableRemoteAccess'] = true;  // Enable remote access (Allow remote connections)
            $policy['EnableRemoteControlOfOtherUsers'] = false;
            $policy['EnableLocalNetworkAccess'] = true;
            $policy['EnableMediaPlayback'] = true;
            $policy['EnableAudioPlaybackTranscoding'] = true;
            $policy['EnableVideoPlaybackTranscoding'] = true;
            $policy['EnablePlaybackRemuxing'] = true;
            $policy['EnableContentDeletion'] = false;
            $policy['EnableContentDownloading'] = true;
            $policy['EnableUserPreferenceAccess'] = true;
            $policy['LoginAttemptsBeforeLockout'] = -1;
            $policy['EnableLocalPassword'] = true;  // Enable local password
            $policy['EnableAllDevices'] = true;
            $policy['EnableAllChannels'] = true;
            $policy['EnableAllFolders'] = true;

            error_log('JellyfinAPI: Setting policy with user disabled initially until package is assigned');

            $updateResult = $this->updateUserPolicy($userId, $policy);

            if($updateResult['success']) {
                error_log('JellyfinAPI: User policy with remote access set successfully');
                return ['success' => true, 'message' => 'User policy set with remote access enabled'];
            } else {
                error_log('JellyfinAPI: Failed to set user policy: ' . ($updateResult['message'] ?? 'Unknown error'));
                return $updateResult;
            }

        } catch(Exception $e) {
            error_log('JellyfinAPI: Exception setting user policy with remote access: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ปิดการใช้งาน remote access สำหรับ user ที่ subscription inactive
     * (Unchecked "Allow remote connections")
     */
    public function disableRemoteAccessForInactiveSubscription($userId) {
        error_log('JellyfinAPI: Disabling remote access for inactive subscription for user ' . $userId);

        try {
            // Get current policy
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Keep user enabled but disable remote access
                $policy['IsDisabled'] = false;  // Keep user enabled
                $policy['IsHidden'] = true;     // ซ่อนจากหน้า login เพื่อความปลอดภัย
                $policy['IsHiddenRemotely'] = true;  // ซ่อนจาก remote login
                $policy['IsHiddenFromUnusedDevices'] = true;  // ซ่อนจาก unused devices

                // Disable remote access (Unchecked "Allow remote connections")
                $policy['EnableRemoteAccess'] = false;  // Unchecked "Allow remote connections"
                $policy['EnableLocalNetworkAccess'] = true;  // Keep local network access

                error_log('JellyfinAPI: Setting policy with remote access disabled for inactive subscription');

                $updateResult = $this->updateUserPolicy($userId, $policy);

                if($updateResult['success']) {
                    error_log('JellyfinAPI: Remote access disabled successfully for inactive subscription');
                    return ['success' => true, 'message' => 'Remote access disabled for inactive subscription'];
                } else {
                    error_log('JellyfinAPI: Failed to disable remote access: ' . ($updateResult['message'] ?? 'Unknown error'));
                    return $updateResult;
                }
            } else {
                error_log('JellyfinAPI: Failed to get user policy: ' . ($policyResult['message'] ?? 'Unknown error'));
                return $policyResult;
            }

        } catch(Exception $e) {
            error_log('JellyfinAPI: Exception disabling remote access for inactive subscription: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Enable user for active subscription
     */
    public function enableUserForSubscription($userId) {
        try {
            error_log('JellyfinAPI: Enabling user for subscription: ' . $userId);

            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Enable user when package is assigned (Unchecked "Disable This user")
                $policy['IsDisabled'] = false;  // Unchecked "Disable This user" when package assigned
                $policy['IsHidden'] = true;     // ซ่อนจากหน้า login เพื่อความปลอดภัย
                $policy['IsHiddenRemotely'] = true;  // ซ่อนจาก remote login
                $policy['IsHiddenFromUnusedDevices'] = true;  // ซ่อนจาก unused devices
                $policy['EnableRemoteAccess'] = true;  // Enable remote access (Allow remote connections)
                $policy['EnableLocalNetworkAccess'] = true;  // Allow local network access

                error_log('JellyfinAPI: Enabling user with package assigned (Unchecked Disable This user)');

                $updateResult = $this->updateUserPolicy($userId, $policy);

                if($updateResult['success']) {
                    error_log('JellyfinAPI: User enabled for subscription successfully');
                    return ['success' => true, 'message' => 'User enabled for subscription'];
                } else {
                    error_log('JellyfinAPI: Failed to enable user for subscription: ' . ($updateResult['message'] ?? 'Unknown error'));
                    return $updateResult;
                }
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('JellyfinAPI: Exception enabling user for subscription: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Disable user when subscription expires
     */
    public function disableUserForExpiredSubscription($userId) {
        try {
            error_log('JellyfinAPI: Disabling user for expired subscription: ' . $userId);

            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Disable user (Checked "Disable This user") and hide for security
                $policy['IsDisabled'] = true;   // This is the main setting - Disable this user

                // Hide user from login screens for security
                $policy['IsHidden'] = true;     // ซ่อนจากหน้า login เพื่อความปลอดภัย
                $policy['IsHiddenRemotely'] = true;  // ซ่อนจาก remote login
                $policy['IsHiddenFromUnusedDevices'] = true;  // ซ่อนจาก unused devices

                // Disable access (Unchecked "Allow remote connections")
                $policy['EnableRemoteAccess'] = false;  // Unchecked "Allow remote connections"
                $policy['EnableLocalNetworkAccess'] = false;  // Disable local network access

                error_log('JellyfinAPI: Setting policy with IsDisabled=true, hidden, and remote access disabled for expired subscription');

                $updateResult = $this->updateUserPolicy($userId, $policy);

                if($updateResult['success']) {
                    error_log('JellyfinAPI: User disabled for expired subscription successfully');
                    return ['success' => true, 'message' => 'User disabled for expired subscription'];
                } else {
                    error_log('JellyfinAPI: Failed to disable user for expired subscription: ' . ($updateResult['message'] ?? 'Unknown error'));
                    return $updateResult;
                }
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('JellyfinAPI: Exception disabling user for expired subscription: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ตั้งค่า policy เริ่มต้นสำหรับ user ใหม่
     * - Hide from login screens
     * - Disable remote access
     */
    public function setInitialUserPolicy($userId) {
        try {
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Hide user from login screens
                $policy['IsHidden'] = true;

                // Disable remote access initially
                $policy['EnableRemoteAccess'] = false;

                // Allow local network access
                $policy['EnableLocalNetworkAccess'] = true;

                // Basic permissions
                $policy['IsAdministrator'] = false;
                $policy['IsDisabled'] = false;

                return $this->updateUserPolicy($userId, $policy);
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('Jellyfin: Failed to set initial user policy: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * เปิดใช้งาน remote access สำหรับ user
     */
    public function enableRemoteAccess($userId) {
        try {
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];
                $policy['EnableRemoteAccess'] = true;

                return $this->updateUserPolicy($userId, $policy);
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('Jellyfin: Failed to enable remote access: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ปิดใช้งาน remote access สำหรับ user
     */
    public function disableRemoteAccess($userId) {
        try {
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];
                $policy['EnableRemoteAccess'] = false;

                return $this->updateUserPolicy($userId, $policy);
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('Jellyfin: Failed to disable remote access: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function getUserByName($username) {
        $result = $this->makeRequest('/Users');
        
        if($result['success'] && is_array($result['data'])) {
            foreach($result['data'] as $user) {
                if($user['Name'] === $username) {
                    return [
                        'success' => true,
                        'data' => $user
                    ];
                }
            }
        }
        
        return [
            'success' => false,
            'message' => 'User not found'
        ];
    }
    
    public function updateUserPassword($userId, $newPassword) {
        $data = [
            'Id' => $userId,
            'CurrentPw' => '',
            'NewPw' => $newPassword
        ];
        
        return $this->makeRequest('/Users/' . $userId . '/Password', 'POST', $data);
    }
    
    public function deleteUser($userId) {
        return $this->makeRequest('/Users/' . $userId, 'DELETE');
    }

    public function getUserPolicy($userId) {
        try {
            // Try multiple endpoints for getting user policy

            // Method 1: Standard policy endpoint
            $result = $this->makeRequest('/Users/' . $userId . '/Policy');
            if($result['success']) {
                return $result;
            }

            // Method 2: Get user data and extract policy
            $userResult = $this->makeRequest('/Users/' . $userId);
            if($userResult['success'] && isset($userResult['data']['Policy'])) {
                return [
                    'success' => true,
                    'data' => $userResult['data']['Policy']
                ];
            }

            // Method 3: Try alternative endpoint
            $altResult = $this->makeRequest('/Users/' . $userId . '/Configuration/Policy');
            if($altResult['success']) {
                return $altResult;
            }

            // Return error with details if all methods fail
            return [
                'success' => false,
                'message' => 'Failed to get user policy - HTTP ' . $result['http_code'],
                'http_code' => $result['http_code'],
                'details' => 'All policy endpoints failed'
            ];

        } catch(Exception $e) {
            error_log('Jellyfin: Exception in getUserPolicy: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    public function updateUserPolicy($userId, $policy) {
        try {
            // Try multiple methods for updating user policy

            // Method 1: Standard policy endpoint
            $result = $this->makeRequest('/Users/' . $userId . '/Policy', 'POST', $policy);
            if($result['success']) {
                return $result;
            }

            // Method 2: Try PUT method
            $putResult = $this->makeRequest('/Users/' . $userId . '/Policy', 'PUT', $policy);
            if($putResult['success']) {
                return $putResult;
            }

            // Method 3: Update through user endpoint
            $userResult = $this->makeRequest('/Users/' . $userId);
            if($userResult['success']) {
                $userData = $userResult['data'];
                $userData['Policy'] = $policy;

                $updateResult = $this->makeRequest('/Users/' . $userId, 'POST', $userData);
                if($updateResult['success']) {
                    return $updateResult;
                }
            }

            // Return error with details if all methods fail
            return [
                'success' => false,
                'message' => 'Failed to update user policy - HTTP ' . $result['http_code'],
                'http_code' => $result['http_code'],
                'details' => 'All policy update methods failed'
            ];

        } catch(Exception $e) {
            error_log('Jellyfin: Exception in updateUserPolicy: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    public function disableUser($userId) {
        $policy = $this->getUserPolicy($userId);

        if($policy['success']) {
            $policyData = $policy['data'];
            $policyData['IsDisabled'] = true;

            return $this->updateUserPolicy($userId, $policyData);
        }

        return $policy;
    }
    
    public function enableUser($userId) {
        $policy = $this->getUserPolicy($userId);
        
        if($policy['success']) {
            $policyData = $policy['data'];
            $policyData['IsDisabled'] = false;
            
            return $this->updateUserPolicy($userId, $policyData);
        }
        
        return $policy;
    }
    
    public function testConnection() {
        try {
            $result = $this->makeRequest('/System/Info');
            return $result['success'];
        } catch(Exception $e) {
            return false;
        }
    }
    
    public function getServerInfo() {
        return $this->makeRequest('/System/Info');
    }

    /**
     * Get user by ID with full details
     */
    public function getUserById($userId) {
        return $this->makeRequest('/Users/' . $userId);
    }

    /**
     * Public method to make API requests (for testing purposes)
     */
    public function makeApiRequest($endpoint, $method = 'GET', $data = null) {
        return $this->makeRequest($endpoint, $method, $data);
    }

    /**
     * Get all users with multiple fallback methods
     */
    public function getAllUsers() {
        // Try multiple endpoints to get users
        $endpoints = [
            '/Users',
            '/Users/<USER>',
            '/Users?EnableImages=false',
            '/Users/<USER>'
        ];

        foreach($endpoints as $endpoint) {
            $result = $this->makeRequest($endpoint);

            if($result['success']) {
                // Handle different response formats
                if(isset($result['data']['Items']) && is_array($result['data']['Items'])) {
                    return [
                        'success' => true,
                        'data' => $result['data']['Items'],
                        'endpoint' => $endpoint,
                        'format' => 'Items'
                    ];
                } elseif(is_array($result['data'])) {
                    return [
                        'success' => true,
                        'data' => $result['data'],
                        'endpoint' => $endpoint,
                        'format' => 'Array'
                    ];
                }
            }
        }

        // If all endpoints fail, return the last error
        return [
            'success' => false,
            'message' => 'All user endpoints failed',
            'last_error' => isset($result) ? $result : ['message' => 'No endpoints tried']
        ];
    }
}
?>
