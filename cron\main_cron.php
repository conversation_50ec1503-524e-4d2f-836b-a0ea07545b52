<?php
/**
 * Main Cron Script - รวมทุกงานใน 1 ไฟล์
 * 
 * งานที่ทำ:
 * 1. ดึงข้อมูล transaction จาก API
 * 2. ตรวจสอบและอนุมัติสลิปอัตโนมัติ
 * 3. ตรวจสอบ subscription ที่หมดอายุ
 * 
 * วิธีใช้:
 * - Windows: php main_cron.php
 * - Linux: php main_cron.php
 * - Cron: */5 * * * * php /path/to/main_cron.php
 */

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once __DIR__ . '/../config/db_config.php';

// Check if required classes exist
$required_classes = [
    __DIR__ . '/../classes/PaymentManager.php',
    __DIR__ . '/../classes/SubscriptionManager.php',
    __DIR__ . '/../classes/SlipVerificationManager.php'
];

foreach ($required_classes as $class_file) {
    if (file_exists($class_file)) {
        require_once $class_file;
    }
}

// Log file
$log_file = __DIR__ . '/logs/main_cron.log';

// Ensure log directory exists
if (!file_exists(dirname($log_file))) {
    mkdir(dirname($log_file), 0755, true);
}

/**
 * Log function
 */
function logMessage($message, $log_file) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry; // Also output to console
}

/**
 * 1. ดึงข้อมูล Transaction จาก API
 */
function fetchTransactions($log_file) {
    logMessage("=== เริ่มดึงข้อมูล Transaction ===", $log_file);

    try {
        // Check if API script exists
        $api_script = __DIR__ . '/../api/cron_api_fetch.php';

        if (!file_exists($api_script)) {
            logMessage("WARNING: ไม่พบไฟล์ API script: {$api_script} - ข้ามขั้นตอนนี้", $log_file);
            logMessage("=== ข้ามการดึงข้อมูล Transaction ===", $log_file);
            return true; // Return true to continue with other tasks
        }

        // Capture output from API script
        ob_start();
        $result = include $api_script;
        $output = ob_get_clean();

        if ($output) {
            logMessage("API Fetch Output: " . trim($output), $log_file);
        } else {
            logMessage("API Fetch: ทำงานเสร็จสิ้น (ไม่มี output)", $log_file);
        }

        logMessage("=== เสร็จสิ้นการดึงข้อมูล Transaction ===", $log_file);
        return true;

    } catch (Exception $e) {
        logMessage("ERROR: ข้อผิดพลาดในการดึงข้อมูล Transaction: " . $e->getMessage(), $log_file);
        return false;
    }
}

/**
 * 2. ตรวจสอบและอนุมัติสลิปอัตโนมัติ
 */
function processSlipVerification($log_file) {
    logMessage("=== เริ่มตรวจสอบสลิปอัตโนมัติ ===", $log_file);

    try {
        // Check if SlipVerificationManager class exists
        if (!class_exists('SlipVerificationManager')) {
            logMessage("WARNING: SlipVerificationManager class ไม่พบ - ข้ามขั้นตอนนี้", $log_file);
            logMessage("=== ข้ามการตรวจสอบสลิป ===", $log_file);
            return true;
        }

        $database = new Database();
        $conn = $database->getConnection();

        if (!$conn) {
            logMessage("ERROR: ไม่สามารถเชื่อมต่อฐานข้อมูลได้", $log_file);
            return false;
        }

        $slipManager = new SlipVerificationManager();
        $result = $slipManager->processAutoVerification();

        if ($result['success']) {
            logMessage("Slip Verification: " . $result['message'], $log_file);

            if (isset($result['processed']) && $result['processed'] > 0) {
                logMessage("อนุมัติสลิปอัตโนมัติ: {$result['processed']} รายการ", $log_file);
            } else {
                logMessage("ไม่มีสลิปที่ต้องอนุมัติ", $log_file);
            }
        } else {
            logMessage("ERROR: " . $result['message'], $log_file);
        }

        logMessage("=== เสร็จสิ้นการตรวจสอบสลิป ===", $log_file);
        return $result['success'];

    } catch (Exception $e) {
        logMessage("ERROR: ข้อผิดพลาดในการตรวจสอบสลิป: " . $e->getMessage(), $log_file);
        return false;
    }
}

/**
 * 3. ตรวจสอบ Subscription ที่หมดอายุ
 */
function checkExpiredSubscriptions($log_file) {
    logMessage("=== เริ่มตรวจสอบ Subscription ที่หมดอายุ ===", $log_file);

    try {
        // Check if SubscriptionManager class exists
        if (!class_exists('SubscriptionManager')) {
            logMessage("WARNING: SubscriptionManager class ไม่พบ - ข้ามขั้นตอนนี้", $log_file);
            logMessage("=== ข้ามการตรวจสอบ Subscription ===", $log_file);
            return true;
        }

        $database = new Database();
        $conn = $database->getConnection();

        if (!$conn) {
            logMessage("ERROR: ไม่สามารถเชื่อมต่อฐานข้อมูลได้", $log_file);
            return false;
        }

        $subscriptionManager = new SubscriptionManager();
        $result = $subscriptionManager->checkAndUpdateExpiredSubscriptions();

        if ($result['success']) {
            logMessage("Subscription Check: " . $result['message'], $log_file);

            if (isset($result['expired_count']) && $result['expired_count'] > 0) {
                logMessage("อัพเดท subscription ที่หมดอายุ: {$result['expired_count']} รายการ", $log_file);
            } else {
                logMessage("ไม่มี subscription ที่หมดอายุ", $log_file);
            }
        } else {
            logMessage("ERROR: " . $result['message'], $log_file);
        }

        logMessage("=== เสร็จสิ้นการตรวจสอบ Subscription ===", $log_file);
        return $result['success'];

    } catch (Exception $e) {
        logMessage("ERROR: ข้อผิดพลาดในการตรวจสอบ Subscription: " . $e->getMessage(), $log_file);
        return false;
    }
}

// ===== MAIN EXECUTION =====

logMessage("🚀 เริ่มต้น Main Cron Job", $log_file);
logMessage("เวลาปัจจุบัน: " . date('Y-m-d H:i:s'), $log_file);

$success_count = 0;
$total_tasks = 3;

// Task 1: Fetch Transactions
if (fetchTransactions($log_file)) {
    $success_count++;
}

// Task 2: Process Slip Verification
if (processSlipVerification($log_file)) {
    $success_count++;
}

// Task 3: Check Expired Subscriptions
if (checkExpiredSubscriptions($log_file)) {
    $success_count++;
}

// Summary
logMessage("", $log_file);
logMessage("📊 สรุปผลการทำงาน:", $log_file);
logMessage("✅ งานที่สำเร็จ: {$success_count}/{$total_tasks}", $log_file);
logMessage("⏰ เวลาที่เสร็จสิ้น: " . date('Y-m-d H:i:s'), $log_file);

if ($success_count == $total_tasks) {
    logMessage("🎉 Main Cron Job เสร็จสิ้นสมบูรณ์", $log_file);
    exit(0); // Success
} else {
    logMessage("⚠️ Main Cron Job เสร็จสิ้นแต่มีข้อผิดพลาดบางส่วน", $log_file);
    exit(1); // Partial failure
}
?>
