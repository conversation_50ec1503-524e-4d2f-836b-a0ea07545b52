#!/bin/bash
# Slow Cron Runner - รันทุก 30 นาที
# งาน: ตรวจสอบ subscription ที่หมดอายุ

echo "========================================"
echo "Slow Cron Job - Starting"
echo "Time: $(date)"
echo "========================================"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Run the slow cron script
php slow_cron.php
EXIT_CODE=$?

# Check exit code
if [ $EXIT_CODE -eq 0 ]; then
    echo ""
    echo "========================================"
    echo "Slow Cron Job - COMPLETED SUCCESSFULLY"
    echo "Time: $(date)"
    echo "========================================"
else
    echo ""
    echo "========================================"
    echo "Slow Cron Job - COMPLETED WITH ERRORS"
    echo "Exit Code: $EXIT_CODE"
    echo "Time: $(date)"
    echo "========================================"
fi

exit $EXIT_CODE
