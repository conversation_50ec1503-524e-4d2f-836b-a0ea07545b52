@echo off
REM Slow Cron Runner - รันทุก 30 นาที
REM งาน: ตรวจสอบ subscription ที่หมดอายุ

echo ========================================
echo Slow Cron Job - Starting
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Try to find PHP executable (Laragon first)
set PHP_PATH=php

REM Check Laragon (common versions)
for %%v in (php-8.3.* php-8.2.* php-8.1.* php-8.0.* php-7.4.*) do (
    if exist "C:\laragon\bin\php\%%v\php.exe" (
        set PHP_PATH=C:\laragon\bin\php\%%v\php.exe
        goto php_found
    )
)

REM Check Laragon direct path
if exist "C:\laragon\bin\php\php.exe" set PHP_PATH=C:\laragon\bin\php\php.exe & goto php_found

REM Check other locations
if exist "C:\xampp\php\php.exe" set PHP_PATH=C:\xampp\php\php.exe
if exist "C:\wamp64\bin\php\php8.2.12\php.exe" set PHP_PATH=C:\wamp64\bin\php\php8.2.12\php.exe

:php_found

echo Using PHP: %PHP_PATH%
echo.

REM Run the slow cron script
"%PHP_PATH%" slow_cron.php

REM Check if PHP command failed
if %ERRORLEVEL% EQU 9009 (
    echo.
    echo ERROR: PHP not found! Please install PHP or add it to PATH
    echo.
    echo Common PHP locations:
    echo - C:\xampp\php\php.exe
    echo - C:\wamp64\bin\php\[version]\php.exe
    echo - C:\laragon\bin\php\[version]\php.exe
    echo.
    echo Please update the PHP_PATH in this script or add PHP to your system PATH
    pause
    exit /b 9009
)

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED SUCCESSFULLY
    echo Time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED WITH ERRORS
    echo Exit Code: %ERRORLEVEL%
    echo Time: %date% %time%
    echo ========================================
)

REM Keep window open for 3 seconds if run manually
timeout /t 3 /nobreak >nul

exit /b %ERRORLEVEL%
