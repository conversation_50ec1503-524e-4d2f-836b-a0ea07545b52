<?php
/**
 * Slip Verification Cron - รันทุก 5 วินาที
 * 
 * งานที่ทำ:
 * - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
 * - รันต่อเนื่องทุก 5 วินาที
 * 
 * วิธีใช้:
 * - Windows: start_slip_verification_5sec.bat
 * - Linux: ./start_slip_verification_5sec.sh
 * - PowerShell: .\start_slip_verification_5sec.ps1
 */

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once __DIR__ . '/../config/db_config.php';

// Check if required classes exist
if (file_exists(__DIR__ . '/../classes/SlipVerificationManager.php')) {
    require_once __DIR__ . '/../classes/SlipVerificationManager.php';
}

// Log file
$log_file = __DIR__ . '/logs/slip_verification_5sec.log';

// Ensure log directory exists
if (!file_exists(dirname($log_file))) {
    mkdir(dirname($log_file), 0755, true);
}

/**
 * Log function
 */
function logMessage($message, $log_file) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry; // Also output to console
}

/**
 * ตรวจสอบและอนุมัติสลิปอัตโนมัติ
 */
function processSlipVerification($log_file) {
    try {
        // Check if SlipVerificationManager class exists
        if (!class_exists('SlipVerificationManager')) {
            return true; // Skip silently if class not found
        }
        
        $database = new Database();
        $conn = $database->getConnection();
        
        if (!$conn) {
            logMessage("ERROR: ไม่สามารถเชื่อมต่อฐานข้อมูลได้", $log_file);
            return false;
        }
        
        $slipManager = new SlipVerificationManager();
        $result = $slipManager->processAutoVerification();
        
        if ($result['success']) {
            if (isset($result['processed']) && $result['processed'] > 0) {
                logMessage("SLIP: อนุมัติ {$result['processed']} รายการ", $log_file);
            }
            // Don't log if no slips processed (to reduce log noise)
        } else {
            logMessage("ERROR SLIP: " . $result['message'], $log_file);
        }
        
        return $result['success'];
        
    } catch (Exception $e) {
        logMessage("ERROR SLIP: " . $e->getMessage(), $log_file);
        return false;
    }
}

// ===== MAIN EXECUTION =====

// Process slip verification
$success = processSlipVerification($log_file);

// Exit with appropriate code
if ($success) {
    exit(0); // Success
} else {
    exit(1); // Failure
}
?>
