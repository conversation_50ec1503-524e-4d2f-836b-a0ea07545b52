@echo off
REM Slow Cron Runner for Laragon - รันทุก 30 นาที

echo ========================================
echo Slow Cron Job - Laragon PHP 8.3.16
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Set the PHP path (adjust this path according to your Laragon installation)
set PHP_PATH=C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe

REM Check if PHP exists
if not exist "%PHP_PATH%" (
    echo ERROR: PHP not found at %PHP_PATH%
    echo.
    echo Please check if the path is correct or run find_laragon_php.bat
    echo to find your actual PHP installation path.
    echo.
    pause
    exit /b 1
)

echo Using PHP: %PHP_PATH%
echo.

REM Test PHP
"%PHP_PATH%" -v >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: PHP test failed!
    echo Path: %PHP_PATH%
    pause
    exit /b 1
)

echo [OK] PHP test passed
"%PHP_PATH%" -v | findstr "PHP"
echo.

REM Create logs directory if not exists
if not exist "logs" mkdir logs

REM Run the slow cron script
"%PHP_PATH%" slow_cron.php

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED SUCCESSFULLY
    echo Time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED WITH ERRORS
    echo Exit Code: %ERRORLEVEL%
    echo Time: %date% %time%
    echo Check logs/slow_cron.log for details
    echo ========================================
)

REM Keep window open for 3 seconds if run manually
timeout /t 3 /nobreak >nul

exit /b %ERRORLEVEL%
