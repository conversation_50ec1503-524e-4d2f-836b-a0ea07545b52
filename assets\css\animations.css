/* Media Server Management System - Animations & Fonts */

/* Font Face Declarations */
@font-face {
    font-family: 'MN Bak kut teh';
    src: url('../../fonts/MN Bak kut teh.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'MN Bak kut teh';
    src: url('../../fonts/MN Bak kut teh Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

/* CSS Variables for consistent theming */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    
    --font-primary: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Font Application */
body {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
}

.navbar-brand {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1.5rem;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Loading Spinner */
.loading-spinner-large {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

/* Dots Loading Animation */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

/* Pulse Loading */
.loading-pulse {
    animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Slide In Animations */
.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Bounce Animation */
.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Shake Animation */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

/* Card Hover Effects */
.card-hover {
    transition: var(--transition);
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Button Animations */
.btn-animated {
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animated:hover::before {
    left: 100%;
}

.btn-animated:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Progress Bar Animation */
.progress-animated .progress-bar {
    animation: progressFill 1.5s ease-in-out;
}

@keyframes progressFill {
    from { width: 0%; }
}

/* Alert Animations */
.alert-animated {
    animation: alertSlideIn 0.5s ease-out;
}

@keyframes alertSlideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Modal Animations */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

/* Navbar Animation */
.navbar {
    animation: navbarSlideDown 0.5s ease-out;
}

@keyframes navbarSlideDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

/* Hero Section Animation */
.hero-section {
    position: relative;
    overflow: hidden;
    min-height: 500px;
    padding: 80px 0;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/banner/avengers.gif') center/cover no-repeat;
    opacity: 0.3;
    animation: heroBackgroundSlide 10s infinite;
    z-index: 1;
}



.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h2,
.hero-section p {
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

@keyframes heroBackgroundSlide {
    0%, 45% {
        background-image: url('/banner/avengers.gif');
        opacity: 0.3;
    }
    50%, 95% {
        background-image: url('/banner/avengers2.gif');
        opacity: 0.3;
    }
    100% {
        background-image: url('/banner/avengers.gif');
        opacity: 0.3;
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Stats Counter Animation */
.counter {
    font-family: var(--font-primary);
    font-weight: 700;
}

.counter-animate {
    animation: countUp 2s ease-out;
}

@keyframes countUp {
    from { opacity: 0; transform: scale(0.5); }
    to { opacity: 1; transform: scale(1); }
}

/* QR Code Animation */
.qr-code-container {
    animation: qrCodeAppear 0.8s ease-out;
}

@keyframes qrCodeAppear {
    0% { transform: scale(0) rotate(180deg); opacity: 0; }
    50% { transform: scale(1.1) rotate(0deg); opacity: 0.8; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

.qr-code-animation {
    animation: qrCodeSpin 0.8s ease-out;
}

@keyframes qrCodeSpin {
    0% {
        transform: scale(0) rotate(360deg);
        opacity: 0;
        filter: blur(10px);
    }
    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 0.7;
        filter: blur(5px);
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
        filter: blur(0px);
    }
}

/* Table Row Hover */
.table-hover tbody tr {
    transition: var(--transition);
    position: relative;
}

.table-hover tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(102, 126, 234, 0.1), rgba(102, 126, 234, 0.05));
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.table-hover tbody tr:hover td {
    border-color: rgba(102, 126, 234, 0.2);
}

/* Table Animation on Load */
.table tbody tr {
    animation: tableRowSlideIn 0.5s ease-out;
    animation-fill-mode: both;
}

.table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.table tbody tr:nth-child(2) { animation-delay: 0.2s; }
.table tbody tr:nth-child(3) { animation-delay: 0.3s; }
.table tbody tr:nth-child(4) { animation-delay: 0.4s; }
.table tbody tr:nth-child(5) { animation-delay: 0.5s; }

@keyframes tableRowSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Badge Animations */
.badge {
    transition: var(--transition);
}

.badge:hover {
    transform: scale(1.1);
}

/* Form Input Focus Effects */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transition: var(--transition);
}

/* Dropdown Animation */
.dropdown-menu {
    animation: dropdownSlide 0.3s ease-out;
}

@keyframes dropdownSlide {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Animations */
@media (max-width: 768px) {
    .slide-in-left,
    .slide-in-right {
        animation: slideInUp 0.5s ease-out;
    }
    
    .hero-section {
        padding: 60px 0;
    }
}

/* Print Styles */
@media print {
    .fade-in,
    .slide-in-left,
    .slide-in-right,
    .slide-in-up,
    .bounce-in {
        animation: none;
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
