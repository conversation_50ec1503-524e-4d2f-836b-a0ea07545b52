@echo off
REM Slow Cron Runner with Custom PHP Path - รันทุก 30 นาที

echo ========================================
echo Slow Cron Job - Custom PHP Path
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Check if custom PHP path is already set
if exist "php_path.txt" (
    set /p PHP_PATH=<php_path.txt
    echo Using saved PHP path: %PHP_PATH%
    goto test_php
)

echo Please enter the full path to your PHP executable.
echo.
echo Examples:
echo - C:\laragon\bin\php\php-8.2.12\php.exe
echo - C:\xampp\php\php.exe
echo - C:\wamp64\bin\php\php8.2.12\php.exe
echo.
echo (Run find_laragon_php.bat first to find your PHP path)
echo.

set /p PHP_PATH=Enter PHP path: 

REM Save the path for future use
echo %PHP_PATH% > php_path.txt
echo.
echo PHP path saved to php_path.txt

:test_php
REM Test PHP
echo.
echo Testing PHP...
"%PHP_PATH%" -v >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: PHP test failed!
    echo Path: %PHP_PATH%
    echo.
    echo Please check the path and try again.
    echo Delete php_path.txt to enter a new path.
    pause
    exit /b 1
)

echo [OK] PHP test passed
"%PHP_PATH%" -v | findstr "PHP"
echo.

REM Create logs directory if not exists
if not exist "logs" mkdir logs

REM Run the slow cron script
"%PHP_PATH%" slow_cron.php

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED SUCCESSFULLY
    echo Time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED WITH ERRORS
    echo Exit Code: %ERRORLEVEL%
    echo Time: %date% %time%
    echo Check logs/slow_cron.log for details
    echo ========================================
)

REM Keep window open for 3 seconds if run manually
timeout /t 3 /nobreak >nul

exit /b %ERRORLEVEL%
