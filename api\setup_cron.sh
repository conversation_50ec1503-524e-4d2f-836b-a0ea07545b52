#!/bin/bash

# Setup script for PayNoi API Cron Job
# This script helps you set up the cron job for fetching API data

echo "=== PayNoi API Cron Job Setup ==="
echo ""

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PHP_SCRIPT="$SCRIPT_DIR/cron_api_fetch.php"

echo "Script location: $PHP_SCRIPT"
echo ""

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "Error: PHP is not installed or not in PATH"
    exit 1
fi

echo "PHP version: $(php -v | head -n 1)"
echo ""

# Test the script
echo "Testing the PHP script..."
php "$PHP_SCRIPT"
if [ $? -eq 0 ]; then
    echo "✓ Script test successful"
else
    echo "✗ Script test failed"
    exit 1
fi

echo ""
echo "=== Cron Job Configuration Examples ==="
echo ""
echo "Choose one of the following cron job configurations:"
echo ""
echo "1. Every 5 minutes:"
echo "   */5 * * * * /usr/bin/php $PHP_SCRIPT"
echo ""
echo "2. Every 15 minutes:"
echo "   */15 * * * * /usr/bin/php $PHP_SCRIPT"
echo ""
echo "3. Every 30 minutes:"
echo "   */30 * * * * /usr/bin/php $PHP_SCRIPT"
echo ""
echo "4. Every hour:"
echo "   0 * * * * /usr/bin/php $PHP_SCRIPT"
echo ""
echo "5. Every 6 hours:"
echo "   0 */6 * * * /usr/bin/php $PHP_SCRIPT"
echo ""
echo "6. Daily at 2:00 AM:"
echo "   0 2 * * * /usr/bin/php $PHP_SCRIPT"
echo ""

echo "To add a cron job:"
echo "1. Run: crontab -e"
echo "2. Add one of the lines above"
echo "3. Save and exit"
echo ""

echo "To view current cron jobs:"
echo "   crontab -l"
echo ""

echo "To remove all cron jobs:"
echo "   crontab -r"
echo ""

echo "Log files will be created at:"
echo "   $SCRIPT_DIR/logs/api_fetch.log"
echo ""

echo "Data files will be saved at:"
echo "   $SCRIPT_DIR/data/transactions.json"
echo ""

echo "=== Setup Complete ==="
