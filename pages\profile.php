<?php
// Handle profile update
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_profile'])) {
    $full_name = trim($_POST['full_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    $errors = [];
    
    if(empty($full_name)) {
        $errors[] = 'กรุณากรอกชื่อ-นามสกุล';
    }
    
    // If changing password
    if(!empty($new_password)) {
        if(empty($current_password)) {
            $errors[] = 'กรุณากรอกรหัสผ่านปัจจุบัน';
        } elseif(!password_verify($current_password, $user['password_hash'])) {
            $errors[] = 'รหัสผ่านปัจจุบันไม่ถูกต้อง';
        } elseif(strlen($new_password) < 6) {
            $errors[] = 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร';
        } elseif($new_password !== $confirm_password) {
            $errors[] = 'รหัสผ่านใหม่ไม่ตรงกัน';
        }
    }
    
    if(empty($errors)) {
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            if(!empty($new_password)) {
                // Update with new password
                $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                $query = "UPDATE users SET full_name = :full_name, phone = :phone, password_hash = :password_hash, updated_at = NOW() WHERE id = :user_id";
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':password_hash', $password_hash);
            } else {
                // Update without password change
                $query = "UPDATE users SET full_name = :full_name, phone = :phone, updated_at = NOW() WHERE id = :user_id";
                $stmt = $conn->prepare($query);
            }
            
            $stmt->bindParam(':full_name', $full_name);
            $stmt->bindParam(':phone', $phone);
            $stmt->bindParam(':user_id', $user['id']);
            
            if($stmt->execute()) {
                $success_message = 'อัปเดตโปรไฟล์สำเร็จ';

                // If password was changed, update Media Server passwords
                if(!empty($new_password)) {
                    $media_updates = [];

                    // Update Emby password
                    try {
                        $emby_query = "UPDATE emby_accounts SET emby_password = :password WHERE user_id = :user_id AND status = 'active'";
                        $emby_stmt = $conn->prepare($emby_query);
                        $emby_stmt->bindParam(':password', $new_password);
                        $emby_stmt->bindParam(':user_id', $user['id']);
                        if($emby_stmt->execute() && $emby_stmt->rowCount() > 0) {
                            $media_updates[] = 'อัปเดตรหัสผ่าน Emby สำเร็จ';
                        }
                    } catch(Exception $e) {
                        $media_updates[] = 'ไม่สามารถอัปเดตรหัสผ่าน Emby ได้';
                    }

                    // Update Jellyfin password
                    try {
                        $jellyfin_query = "UPDATE jellyfin_accounts SET jellyfin_password = :password WHERE user_id = :user_id AND status = 'active'";
                        $jellyfin_stmt = $conn->prepare($jellyfin_query);
                        $jellyfin_stmt->bindParam(':password', $new_password);
                        $jellyfin_stmt->bindParam(':user_id', $user['id']);
                        if($jellyfin_stmt->execute() && $jellyfin_stmt->rowCount() > 0) {
                            $media_updates[] = 'อัปเดตรหัสผ่าน Jellyfin สำเร็จ';
                        }
                    } catch(Exception $e) {
                        $media_updates[] = 'ไม่สามารถอัปเดตรหัสผ่าน Jellyfin ได้';
                    }

                    // Add media server update results to success message
                    if(!empty($media_updates)) {
                        $success_message .= '<br><small class="text-info">' . implode('<br>', $media_updates) . '</small>';
                    }
                }

                // Update user data in session
                $user['full_name'] = $full_name;
                $user['phone'] = $phone;

                // Log the action
                require_once 'classes/UserManager.php';
                $userManager = new UserManager();
                $userManager->logUserAction($user['id'], 'profile_update', 'Profile updated' . (!empty($new_password) ? ' with password change' : ''));
            } else {
                $errors[] = 'เกิดข้อผิดพลาดในการอัปเดตข้อมูล';
            }
            
        } catch(Exception $e) {
            $errors[] = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        }
    }
}

// Get user's media server accounts
$database = new Database();
$conn = $database->getConnection();

$query = "SELECT * FROM emby_accounts WHERE user_id = :user_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$emby_account = $stmt->fetch(PDO::FETCH_ASSOC);

$query = "SELECT * FROM jellyfin_accounts WHERE user_id = :user_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$jellyfin_account = $stmt->fetch(PDO::FETCH_ASSOC);

// Get user's subscription history
$query = "SELECT us.*, p.name as package_name, p.description 
          FROM user_subscriptions us 
          JOIN packages p ON us.package_id = p.id 
          WHERE us.user_id = :user_id 
          ORDER BY us.created_at DESC 
          LIMIT 10";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$subscription_history = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">โปรไฟล์ของฉัน</h2>
            <p class="text-muted">จัดการข้อมูลส่วนตัวและดูประวัติการใช้งาน</p>
        </div>
    </div>
    
    <!-- Messages -->
    <?php if(isset($success_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if(!empty($errors)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <ul class="mb-0">
                    <?php foreach($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- Profile Form -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>แก้ไขข้อมูลส่วนตัว</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">ชื่อผู้ใช้</label>
                                    <input type="text" class="form-control" id="username" 
                                           value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                                    <div class="form-text">ไม่สามารถเปลี่ยนชื่อผู้ใช้ได้</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">อีเมล <small class="text-muted">(ไม่บังคับ)</small></label>
                                    <input type="email" class="form-control" id="email"
                                           value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" readonly>
                                    <div class="form-text">
                                        <?php if(empty($user['email'])): ?>
                                            <span class="text-muted">ไม่ได้ระบุอีเมล</span>
                                        <?php else: ?>
                                            ไม่สามารถเปลี่ยนอีเมลได้
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label">ชื่อ-นามสกุล <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">เบอร์โทรศัพท์</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                        </div>
                        
                        <hr>
                        
                        <h6 class="mb-3">เปลี่ยนรหัสผ่าน (ไม่บังคับ)</h6>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>หมายเหตุ:</strong> เมื่อเปลี่ยนรหัสผ่านเว็บไซต์ ระบบจะอัปเดตรหัสผ่าน Emby และ Jellyfin ให้เป็นรหัสผ่านใหม่โดยอัตโนมัติ
                        </div>
                        
                        <div class="mb-3">
                            <label for="current_password" class="form-label">รหัสผ่านปัจจุบัน</label>
                            <input type="password" class="form-control" id="current_password" name="current_password">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">รหัสผ่านใหม่</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password">
                                    <div class="form-text">อย่างน้อย 6 ตัวอักษร</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">ยืนยันรหัสผ่านใหม่</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>บันทึกการเปลี่ยนแปลง
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Account Info -->
        <div class="col-md-4">
            <!-- Account Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>ข้อมูลบัญชี</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2">
                        <strong>สถานะ:</strong> 
                        <span class="badge <?php echo $user['status'] == 'active' ? 'bg-success' : 'bg-secondary'; ?>">
                            <?php echo $user['status'] == 'active' ? 'ใช้งานได้' : $user['status']; ?>
                        </span>
                    </p>
                    <p class="mb-2">
                        <strong>ยอดเงินคงเหลือ:</strong> 
                        <span class="text-primary"><?php
                            $balance = floatval($user['balance']);
                            echo number_format($balance, 0, '.', ',') . ' ฿';
                        ?></span>
                    </p>
                    <p class="mb-2">
                        <strong>สมัครสมาชิกเมื่อ:</strong><br>
                        <small><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></small>
                    </p>
                    <p class="mb-0">
                        <strong>อัปเดตล่าสุด:</strong><br>
                        <small><?php echo date('d/m/Y H:i', strtotime($user['updated_at'])); ?></small>
                    </p>
                </div>
            </div>
            
            <!-- Media Server Accounts -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-server me-2"></i>บัญชี Media Server</h6>
                </div>
                <div class="card-body">
                    <!-- Emby Account -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0"><i class="fas fa-tv me-1 text-info"></i>Emby</h6>
                            <?php if($emby_account): ?>
                                <span class="badge <?php echo $emby_account['status'] == 'active' ? 'bg-success' : 'bg-secondary'; ?>">
                                    <?php echo $emby_account['status']; ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if($emby_account): ?>
                            <p class="mb-1"><small><strong>Username:</strong> <?php echo htmlspecialchars($emby_account['emby_username']); ?></small></p>
                            <p class="mb-1"><small><strong>Password:</strong> <span class="text-info">ใช้รหัสผ่านเดียวกับเว็บ</span></small></p>
                            <p class="mb-0"><small><strong>Server:</strong> <?php echo htmlspecialchars($emby_account['server_url']); ?></small></p>
                        <?php else: ?>
                            <p class="text-muted mb-0"><small>ยังไม่มีบัญชี Emby</small></p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Jellyfin Account -->
                    <div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0"><i class="fas fa-play me-1 text-warning"></i>Jellyfin</h6>
                            <?php if($jellyfin_account): ?>
                                <span class="badge <?php echo $jellyfin_account['status'] == 'active' ? 'bg-success' : 'bg-secondary'; ?>">
                                    <?php echo $jellyfin_account['status']; ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if($jellyfin_account): ?>
                            <p class="mb-1"><small><strong>Username:</strong> <?php echo htmlspecialchars($jellyfin_account['jellyfin_username']); ?></small></p>
                            <p class="mb-1"><small><strong>Password:</strong> <span class="text-info">ใช้รหัสผ่านเดียวกับเว็บ</span></small></p>
                            <p class="mb-0"><small><strong>Server:</strong> <?php echo htmlspecialchars($jellyfin_account['server_url']); ?></small></p>
                        <?php else: ?>
                            <p class="text-muted mb-0"><small>ยังไม่มีบัญชี Jellyfin</small></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Subscription History -->
    <div class="row mt-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>ประวัติการซื้อแพ็คเกจ</h5>
                </div>
                <div class="card-body">
                    <?php if(!empty($subscription_history)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>วันที่ซื้อ</th>
                                    <th>แพ็คเกจ</th>
                                    <th>วันที่เริ่ม</th>
                                    <th>วันหมดอายุ</th>
                                    <th>สถานะ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($subscription_history as $subscription): ?>
                                <tr>
                                    <td>
                                        <small><?php echo date('d/m/Y', strtotime($subscription['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($subscription['package_name']); ?></strong>
                                    </td>
                                    <td>
                                        <small><?php echo date('d/m/Y', strtotime($subscription['start_date'])); ?></small>
                                    </td>
                                    <td>
                                        <small><?php echo date('d/m/Y', strtotime($subscription['end_date'])); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge <?php 
                                            echo $subscription['status'] == 'active' ? 'bg-success' : 
                                                ($subscription['status'] == 'expired' ? 'bg-warning' : 'bg-secondary'); 
                                        ?>">
                                            <?php 
                                            echo $subscription['status'] == 'active' ? 'ใช้งานได้' : 
                                                ($subscription['status'] == 'expired' ? 'หมดอายุ' : $subscription['status']); 
                                            ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-box-open text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">ยังไม่มีประวัติการซื้อแพ็คเกจ</h6>
                        <p class="text-muted">เริ่มซื้อแพ็คเกจเพื่อใช้งาน Media Server</p>
                        <a href="?page=packages" class="btn btn-primary">ดูแพ็คเกจ</a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword && confirmPassword !== '') {
        this.setCustomValidity('รหัสผ่านไม่ตรงกัน');
    } else {
        this.setCustomValidity('');
    }
});
</script>
