# Cron Jobs System - ระบบงานอัตโนมัติ

## 📋 ภาพรวม

ระบบแบ่งงานออกเป็น 2 ประเภทตามความถี่:

### 🚀 Fast Cron (ทุก 5 วินาที)
1. **ดึงข้อมูล Transaction** จาก API
2. **ตรวจสอบและอนุมัติสลิป** อัตโนมัติ

### 🕐 Slow Cron (ทุก 30 นาที)
1. **ตรวจสอบ Subscription** ที่หมดอายุและอัพเดทสถานะ

## 🚀 วิธีใช้งาน

### Fast Cron (ทุก 5 วินาที)

#### Windows
```bash
# รันต่อเนื่อง (แนะนำ)
start_fast_cron.bat

# รันครั้งเดียว
php fast_cron.php
```

#### Linux/macOS
```bash
# ให้สิทธิ์ execute (ครั้งแรกเท่านั้น)
chmod +x start_fast_cron.sh

# รันต่อเนื่อง (แนะนำ)
./start_fast_cron.sh

# รันครั้งเดียว
php fast_cron.php
```

### Slow Cron (ทุก 30 นาที)

#### Windows
```bash
# รันครั้งเดียว
run_slow_cron.bat

# หรือ PHP โดยตรง
php slow_cron.php
```

#### Linux/macOS
```bash
# ให้สิทธิ์ execute (ครั้งแรกเท่านั้น)
chmod +x run_slow_cron.sh

# รันครั้งเดียว
./run_slow_cron.sh

# หรือ PHP โดยตรง
php slow_cron.php
```

## ⏰ ตั้งค่าระบบอัตโนมัติ

### วิธีที่ 1: รันด้วยตนเอง (แนะนำสำหรับทดสอบ)

#### Fast Cron
```bash
# Windows
start_fast_cron.bat

# Linux/macOS
./start_fast_cron.sh
```

#### Slow Cron
ตั้ง Task Scheduler หรือ Cron Job ให้รันทุก 30 นาที

### วิธีที่ 2: Linux/macOS Cron

```bash
# แก้ไข crontab
crontab -e

# เพิ่มบรรทัดเหล่านี้:

# Fast Cron: รันทุก 5 วินาที (ใช้ while loop)
# สร้างสคริปต์แยกหรือใช้ systemd service แทน

# Slow Cron: รันทุก 30 นาที
*/30 * * * * /usr/bin/php /path/to/project/cron/slow_cron.php
```

### วิธีที่ 3: Windows Task Scheduler

#### Fast Cron (รันต่อเนื่อง)
1. เปิด **Task Scheduler**
2. สร้าง **Basic Task**
3. ตั้งชื่อ: "Media Server Fast Cron"
4. Trigger: **When the computer starts**
5. Action: **Start a program**
   - Program: `C:\path\to\project\cron\start_fast_cron.bat`

#### Slow Cron (ทุก 30 นาที)
1. เปิด **Task Scheduler**
2. สร้าง **Basic Task**
3. ตั้งชื่อ: "Media Server Slow Cron"
4. Trigger: **Daily** → **Repeat every: 30 minutes**
5. Action: **Start a program**
   - Program: `C:\path\to\php.exe`
   - Arguments: `C:\path\to\project\cron\slow_cron.php`
   - Start in: `C:\path\to\project\cron`

## 📊 การทำงาน

### ลำดับการทำงาน:
1. **Fetch Transactions** → ดึงข้อมูลจาก API
2. **Slip Verification** → ตรวจสอบและอนุมัติสลิป
3. **Subscription Check** → เช็ควันหมดอายุ

### Exit Codes:
- **0** = สำเร็จทั้งหมด
- **1** = มีข้อผิดพลาดบางส่วน

## 📝 Log Files

### ตำแหน่ง Log:
```
cron/logs/main_cron.log
```

### ตัวอย่าง Log:
```
[2025-06-23 17:30:00] 🚀 เริ่มต้น Main Cron Job
[2025-06-23 17:30:01] === เริ่มดึงข้อมูล Transaction ===
[2025-06-23 17:30:02] API Fetch Output: Fetched 5 transactions
[2025-06-23 17:30:02] === เสร็จสิ้นการดึงข้อมูล Transaction ===
[2025-06-23 17:30:03] === เริ่มตรวจสอบสลิปอัตโนมัติ ===
[2025-06-23 17:30:04] อนุมัติสลิปอัตโนมัติ: 2 รายการ
[2025-06-23 17:30:04] === เสร็จสิ้นการตรวจสอบสลิป ===
[2025-06-23 17:30:05] === เริ่มตรวจสอบ Subscription ที่หมดอายุ ===
[2025-06-23 17:30:06] อัพเดท subscription ที่หมดอายุ: 1 รายการ
[2025-06-23 17:30:06] === เสร็จสิ้นการตรวจสอบ Subscription ===
[2025-06-23 17:30:06] 📊 สรุปผลการทำงาน:
[2025-06-23 17:30:06] ✅ งานที่สำเร็จ: 3/3
[2025-06-23 17:30:06] 🎉 Main Cron Job เสร็จสิ้นสมบูรณ์
```

## 🔧 การแก้ไขปัญหา

### ปัญหาที่พบบ่อย:

1. **PHP ไม่พบ**
   ```bash
   # ตรวจสอบ PHP path
   which php
   # หรือ
   where php
   ```

2. **Permission Denied (Linux)**
   ```bash
   chmod +x run_main_cron.sh
   chmod 755 main_cron.php
   ```

3. **Log ไม่สร้าง**
   ```bash
   # สร้าง directory logs
   mkdir -p cron/logs
   chmod 755 cron/logs
   ```

## 📈 ข้อดี

### เปรียบเทียบกับการรันแยก:
- ✅ **รันครั้งเดียว** แทนที่จะรัน 3 ครั้ง
- ✅ **Log รวม** ง่ายต่อการติดตาม
- ✅ **Error handling** ที่ดีกว่า
- ✅ **Performance** ดีกว่า (เชื่อมต่อ DB ครั้งเดียว)
- ✅ **ง่ายต่อการจัดการ** Cron Job

## ⚠️ ข้อควรระวัง

1. **ไม่ควรรันพร้อมกัน** กับสคริปต์เก่า
2. **ตรวจสอบ timezone** ให้ถูกต้อง
3. **ตรวจสอบ log** เป็นประจำ
4. **Backup ข้อมูล** ก่อนใช้งานครั้งแรก
