<?php
require_once __DIR__ . '/../config/db_config.php';

class SlipVerificationManager {
    private $conn;
    private $api_config;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        
        // API Configuration
        $this->api_config = [
            'api_key' => '6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446',
            'record_key' => '100568',
            'api_url' => 'https://paynoi.com/api_line',
            'data_file' => __DIR__ . '/../api/data/transactions.json'
        ];
    }
    
    /**
     * ดึงข้อมูลธุรกรรมจาก API
     */
    public function fetchTransactionsFromAPI() {
        try {
            $url = $this->api_config['api_url'] . '?' . http_build_query([
                'api_key' => $this->api_config['api_key'],
                'record_key' => $this->api_config['record_key']
            ]);
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_FOLLOWLOCATION => true
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($response === false) {
                throw new Exception("cURL Error: $error");
            }
            
            if ($httpCode !== 200) {
                throw new Exception("HTTP Error: Status code $httpCode");
            }
            
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("JSON Parse Error: " . json_last_error_msg());
            }
            
            if (!isset($data['status']) || $data['status'] !== 'success') {
                throw new Exception("API Error: " . ($data['status'] ?? 'unknown'));
            }
            
            return [
                'success' => true,
                'data' => $data['data']
            ];
            
        } catch (Exception $e) {
            error_log('SlipVerification: API fetch failed - ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * ดึงข้อมูลธุรกรรมจากไฟล์ JSON
     */
    public function getTransactionsFromFile() {
        try {
            if (!file_exists($this->api_config['data_file'])) {
                return [
                    'success' => false,
                    'message' => 'Transaction data file not found'
                ];
            }
            
            $jsonData = file_get_contents($this->api_config['data_file']);
            $data = json_decode($jsonData, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("JSON Parse Error: " . json_last_error_msg());
            }
            
            return [
                'success' => true,
                'data' => $data['transactions'] ?? [],
                'last_updated' => $data['last_updated'] ?? null
            ];
            
        } catch (Exception $e) {
            error_log('SlipVerification: File read failed - ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * ตรวจสอบและอนุมัติการเติมเงินอัตโนมัติ
     */
    public function processAutoApproval() {
        try {
            error_log('SlipVerification: Starting auto approval process');
            
            // ดึงข้อมูลธุรกรรมจาก API หรือไฟล์
            $transactionResult = $this->fetchTransactionsFromAPI();
            if (!$transactionResult['success']) {
                // ถ้า API ล้มเหลว ลองอ่านจากไฟล์
                $transactionResult = $this->getTransactionsFromFile();
            }
            
            if (!$transactionResult['success']) {
                throw new Exception('Failed to get transaction data: ' . $transactionResult['message']);
            }
            
            $transactions = $transactionResult['data'];
            $processed = 0;
            $approved = 0;
            $results = [];
            
            foreach ($transactions as $transaction) {
                $processed++;
                $result = $this->processTransaction($transaction);
                $results[] = $result;
                
                if ($result['approved']) {
                    $approved++;
                }
            }
            
            error_log("SlipVerification: Processed $processed transactions, approved $approved");
            
            return [
                'success' => true,
                'processed' => $processed,
                'approved' => $approved,
                'results' => $results
            ];
            
        } catch (Exception $e) {
            error_log('SlipVerification: Auto approval failed - ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * ประมวลผลธุรกรรมแต่ละรายการ
     */
    private function processTransaction($transaction) {
        try {
            $trans_id = $transaction['trans_id'] ?? null;
            $amount = floatval($transaction['amount'] ?? 0);
            $date = $transaction['date'] ?? '';
            $type = $transaction['type'] ?? '';
            
            // ตรวจสอบว่าเป็นเงินเข้าหรือไม่
            if ($type !== 'เงินเข้า') {
                return [
                    'trans_id' => $trans_id,
                    'approved' => false,
                    'reason' => 'Not incoming transaction'
                ];
            }
            
            // ตรวจสอบว่าธุรกรรมนี้ถูกประมวลผลแล้วหรือไม่
            if ($this->isTransactionProcessed($trans_id)) {
                return [
                    'trans_id' => $trans_id,
                    'approved' => false,
                    'reason' => 'Already processed'
                ];
            }
            
            // หาการเติมเงินที่ตรงกับจำนวนเงิน
            $matchingTopup = $this->findMatchingTopup($amount, $date);
            
            if ($matchingTopup) {
                // อนุมัติการเติมเงิน
                $approvalResult = $this->approveTopup($matchingTopup['id'], $trans_id, $transaction);
                
                if ($approvalResult['success']) {
                    return [
                        'trans_id' => $trans_id,
                        'approved' => true,
                        'topup_id' => $matchingTopup['id'],
                        'user_id' => $matchingTopup['user_id'],
                        'amount' => $amount
                    ];
                } else {
                    return [
                        'trans_id' => $trans_id,
                        'approved' => false,
                        'reason' => 'Approval failed: ' . $approvalResult['message']
                    ];
                }
            } else {
                return [
                    'trans_id' => $trans_id,
                    'approved' => false,
                    'reason' => 'No matching topup request found'
                ];
            }
            
        } catch (Exception $e) {
            error_log('SlipVerification: Transaction processing failed - ' . $e->getMessage());
            return [
                'trans_id' => $trans_id ?? 'unknown',
                'approved' => false,
                'reason' => 'Processing error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * ตรวจสอบว่าธุรกรรมถูกประมวลผลแล้วหรือไม่
     */
    private function isTransactionProcessed($trans_id) {
        if (!$trans_id) return false;
        
        $query = "SELECT id FROM processed_transactions WHERE trans_id = :trans_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':trans_id', $trans_id);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * หาการเติมเงินที่ตรงกับจำนวนเงิน
     */
    private function findMatchingTopup($amount, $transaction_date) {


        // ค้นหาการเติมเงินที่รอการอนุมัติและมีจำนวนเงินตรงกัน (แบบทศนิยม 2 ตำแหน่ง)
        $query = "SELECT * FROM top_ups
                  WHERE status = 'pending'
                  AND ROUND(amount, 2) = ROUND(:amount, 2)
                  AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                  ORDER BY created_at DESC
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':amount', $amount);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }

        // หากไม่พบ ลองหาแบบประมาณ (สำหรับกรณีที่มีการปัดเศษ)
        $query = "SELECT * FROM top_ups
                  WHERE status = 'pending'
                  AND ABS(amount - :amount) <= 0.02
                  AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                  ORDER BY ABS(amount - :amount) ASC, created_at DESC
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':amount', $amount);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }

        return null;
    }
    
    /**
     * อนุมัติการเติมเงิน
     */
    private function approveTopup($topup_id, $trans_id, $transaction_data) {
        try {
            $this->conn->beginTransaction();
            
            // อัปเดตสถานะการเติมเงิน
            $query = "UPDATE top_ups SET
                      status = 'approved',
                      approved_at = NOW(),
                      slip_data = :slip_data
                      WHERE id = :topup_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':topup_id', $topup_id);
            $stmt->bindParam(':slip_data', json_encode($transaction_data));
            $stmt->execute();

            // ดึงข้อมูลการเติมเงิน
            $query = "SELECT * FROM top_ups WHERE id = :topup_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':topup_id', $topup_id);
            $stmt->execute();
            $topup = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // เพิ่มเงินให้ผู้ใช้
            $query = "UPDATE users SET balance = balance + :amount WHERE id = :user_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':amount', $topup['amount']);
            $stmt->bindParam(':user_id', $topup['user_id']);
            $stmt->execute();
            
            // บันทึกธุรกรรมที่ประมวลผลแล้ว
            $query = "INSERT INTO processed_transactions (trans_id, topup_id, processed_at) 
                      VALUES (:trans_id, :topup_id, NOW())";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':trans_id', $trans_id);
            $stmt->bindParam(':topup_id', $topup_id);
            $stmt->execute();
            
            // บันทึก log
            $query = "INSERT INTO usage_logs (user_id, action, description) 
                      VALUES (:user_id, 'auto_topup_approved', :description)";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':user_id', $topup['user_id']);
            $description = "Auto-approved topup of {$topup['amount']} THB via slip verification (Trans ID: $trans_id)";
            $stmt->bindParam(':description', $description);
            $stmt->execute();
            
            $this->conn->commit();
            
            error_log("SlipVerification: Auto-approved topup ID {$topup_id} for user {$topup['user_id']} amount {$topup['amount']} THB");
            
            return [
                'success' => true,
                'message' => 'Topup approved successfully'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollback();
            error_log('SlipVerification: Approval failed - ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
?>
