<?php
/**
 * Slow Cron Script - รันทุก 30 นาที
 * 
 * งานที่ทำ:
 * 1. ตรวจสอบ subscription ที่หมดอายุ
 * 2. อัพเดทสถานะเป็น expired
 * 3. ปิด remote access สำหรับ user ที่หมดอายุ
 * 
 * วิธีใช้:
 * - Windows: php slow_cron.php
 * - Linux: php slow_cron.php
 * - Cron: */30 * * * * php slow_cron.php
 */

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once __DIR__ . '/../config/db_config.php';

// Check if required classes exist
$required_classes = [
    __DIR__ . '/../classes/SubscriptionManager.php'
];

foreach ($required_classes as $class_file) {
    if (file_exists($class_file)) {
        require_once $class_file;
    }
}

// Log file
$log_file = __DIR__ . '/logs/slow_cron.log';

// Ensure log directory exists
if (!file_exists(dirname($log_file))) {
    mkdir(dirname($log_file), 0755, true);
}

/**
 * Log function
 */
function logMessage($message, $log_file) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry; // Also output to console
}

/**
 * ตรวจสอบ Subscription ที่หมดอายุ
 */
function checkExpiredSubscriptions($log_file) {
    logMessage("=== เริ่มตรวจสอบ Subscription ที่หมดอายุ ===", $log_file);
    
    try {
        // Check if SubscriptionManager class exists
        if (!class_exists('SubscriptionManager')) {
            logMessage("WARNING: SubscriptionManager class ไม่พบ - ข้ามขั้นตอนนี้", $log_file);
            logMessage("=== ข้ามการตรวจสอบ Subscription ===", $log_file);
            return true;
        }
        
        $database = new Database();
        $conn = $database->getConnection();
        
        if (!$conn) {
            logMessage("ERROR: ไม่สามารถเชื่อมต่อฐานข้อมูลได้", $log_file);
            return false;
        }
        
        $subscriptionManager = new SubscriptionManager();
        $result = $subscriptionManager->checkAndUpdateExpiredSubscriptions();
        
        if ($result['success']) {
            logMessage("Subscription Check: " . $result['message'], $log_file);
            
            if (isset($result['expired_count']) && $result['expired_count'] > 0) {
                logMessage("✅ อัพเดท subscription ที่หมดอายุ: {$result['expired_count']} รายการ", $log_file);
            } else {
                logMessage("ℹ️ ไม่มี subscription ที่หมดอายุ", $log_file);
            }
            
            // Additional info if available
            if (isset($result['details'])) {
                foreach ($result['details'] as $detail) {
                    logMessage("   - " . $detail, $log_file);
                }
            }
        } else {
            logMessage("ERROR: " . $result['message'], $log_file);
        }
        
        logMessage("=== เสร็จสิ้นการตรวจสอบ Subscription ===", $log_file);
        return $result['success'];
        
    } catch (Exception $e) {
        logMessage("ERROR: ข้อผิดพลาดในการตรวจสอบ Subscription: " . $e->getMessage(), $log_file);
        return false;
    }
}

// ===== MAIN EXECUTION =====

logMessage("🕐 เริ่มต้น Slow Cron Job (30 นาที)", $log_file);
logMessage("เวลาปัจจุบัน: " . date('Y-m-d H:i:s'), $log_file);

$success = checkExpiredSubscriptions($log_file);

// Summary
logMessage("", $log_file);
if ($success) {
    logMessage("🎉 Slow Cron Job เสร็จสิ้นสมบูรณ์", $log_file);
    exit(0); // Success
} else {
    logMessage("❌ Slow Cron Job เสร็จสิ้นแต่มีข้อผิดพลาด", $log_file);
    exit(1); // Failure
}
?>
