@echo off
echo ========================================
echo Laragon PHP Finder - Detailed Search
echo ========================================

echo Searching for Laragon installation...
echo.

REM Check if Laragon directory exists
if not exist "C:\laragon" (
    echo ERROR: C:\laragon directory not found!
    echo.
    echo Please check if Laragon is installed in the default location
    echo or if it's installed in a different drive/path
    echo.
    pause
    exit /b 1
)

echo [FOUND] Laragon directory: C:\laragon
echo.

REM Check bin directory
if not exist "C:\laragon\bin" (
    echo ERROR: C:\laragon\bin directory not found!
    pause
    exit /b 1
)

echo [FOUND] Bin directory: C:\laragon\bin
echo.

REM Check php directory
if not exist "C:\laragon\bin\php" (
    echo ERROR: C:\laragon\bin\php directory not found!
    echo.
    echo Available directories in C:\laragon\bin:
    dir "C:\laragon\bin" /AD /B
    pause
    exit /b 1
)

echo [FOUND] PHP directory: C:\laragon\bin\php
echo.

echo Available PHP versions in Laragon:
echo ========================================
dir "C:\laragon\bin\php" /AD /B

echo.
echo Detailed search for PHP executables:
echo ========================================

REM Search for all php.exe files
for /d %%d in ("C:\laragon\bin\php\*") do (
    if exist "%%d\php.exe" (
        echo [FOUND] %%d\php.exe
        "%%d\php.exe" -v | findstr "PHP"
        echo.
    ) else (
        echo [NO EXE] %%d (directory exists but no php.exe)
    )
)

REM Check direct php.exe
if exist "C:\laragon\bin\php\php.exe" (
    echo [FOUND] C:\laragon\bin\php\php.exe (direct)
    "C:\laragon\bin\php\php.exe" -v | findstr "PHP"
    echo.
)

echo ========================================
echo Search completed!
echo.
echo If you found working PHP paths above, you can:
echo 1. Copy the path (e.g., C:\laragon\bin\php\php-8.2.12\php.exe)
echo 2. Edit the batch files to use that exact path
echo 3. Or add that directory to your system PATH
echo.

pause
