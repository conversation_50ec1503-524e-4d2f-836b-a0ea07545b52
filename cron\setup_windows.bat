@echo off
REM Windows Setup Script for Cron Jobs

echo ========================================
echo Media Server Cron Jobs - Windows Setup
echo ========================================

cd /d "%~dp0"

echo Step 1: Checking PHP installation...
echo.

REM Try to find PHP
set PHP_PATH=
if exist "C:\xampp\php\php.exe" (
    set PHP_PATH=C:\xampp\php\php.exe
    echo [FOUND] XAMPP PHP: %PHP_PATH%
) else if exist "C:\wamp64\bin\php\php8.2.12\php.exe" (
    set PHP_PATH=C:\wamp64\bin\php\php8.2.12\php.exe
    echo [FOUND] WAMP PHP: %PHP_PATH%
) else if exist "C:\laragon\bin\php\php8.2.12\php.exe" (
    set PHP_PATH=C:\laragon\bin\php\php8.2.12\php.exe
    echo [FOUND] Laragon PHP: %PHP_PATH%
) else (
    php -v >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        set PHP_PATH=php
        echo [FOUND] PHP in system PATH
    ) else (
        echo [ERROR] PHP not found!
        echo.
        echo Please run find_php.bat to locate PHP installations
        echo or install PHP using XAMPP, WAMP, or Laragon
        pause
        exit /b 1
    )
)

echo.
echo Step 2: Testing PHP...
"%PHP_PATH%" -v
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] PHP test failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Creating log directories...
if not exist "logs" mkdir logs
echo [OK] Log directory created

echo.
echo Step 4: Testing Fast Cron...
"%PHP_PATH%" fast_cron.php
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Fast Cron test failed!
    pause
    exit /b 1
)
echo [OK] Fast Cron test passed

echo.
echo Step 5: Testing Slow Cron...
"%PHP_PATH%" slow_cron.php
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Slow Cron test failed!
    pause
    exit /b 1
)
echo [OK] Slow Cron test passed

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Next steps:
echo.
echo 1. For Fast Cron (every 5 seconds):
echo    Double-click: start_fast_cron.bat
echo.
echo 2. For Slow Cron (every 30 minutes):
echo    Set up Windows Task Scheduler to run: run_slow_cron.bat
echo.
echo 3. Check logs in: logs\ directory
echo.
echo ========================================

pause
