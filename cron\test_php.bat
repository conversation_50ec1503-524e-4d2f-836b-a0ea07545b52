@echo off
echo ========================================
echo PHP Test - Laragon PHP 8.3.16
echo ========================================

cd /d "%~dp0"

REM Set the PHP path
set PHP_PATH=C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe

echo Testing PHP at: %PHP_PATH%
echo.

REM Check if file exists
if not exist "%PHP_PATH%" (
    echo [ERROR] PHP executable not found!
    echo.
    echo Please check if:
    echo 1. Laragon is installed
    echo 2. PHP 8.3.16 is installed in Laragon
    echo 3. The path is correct
    echo.
    echo Run find_laragon_php.bat to search for PHP installations
    pause
    exit /b 1
)

echo [FOUND] PHP executable exists
echo.

REM Test PHP version
echo Testing PHP version...
"%PHP_PATH%" -v
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] PHP failed to run!
    pause
    exit /b 1
)

echo.
echo [OK] PHP is working correctly!
echo.

REM Test database connection
echo Testing database connection...
"%PHP_PATH%" -r "
try {
    require_once '../config/db_config.php';
    \$database = new Database();
    \$conn = \$database->getConnection();
    if (\$conn) {
        echo 'Database connection: OK' . PHP_EOL;
    } else {
        echo 'Database connection: FAILED' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Database test error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo Testing Fast Cron script...
"%PHP_PATH%" fast_cron.php
echo Fast Cron Exit Code: %ERRORLEVEL%

echo.
echo Testing Slow Cron script...
"%PHP_PATH%" slow_cron.php
echo Slow Cron Exit Code: %ERRORLEVEL%

echo.
echo ========================================
echo All tests completed!
echo.
echo If all tests passed, you can now run:
echo - start_fast_cron_laragon.bat (for continuous fast cron)
echo - run_slow_cron_laragon.bat (for one-time slow cron)
echo - start_slip_verification_5sec.bat (for slip verification only)
echo.

pause
