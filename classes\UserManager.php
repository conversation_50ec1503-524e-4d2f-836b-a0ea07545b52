<?php
require_once 'config/db_config.php';
require_once 'classes/EmbyAPI.php';
require_once 'classes/JellyfinAPI.php';

class UserManager {
    private $conn;
    private $settings;
    private $emby_api;
    private $jellyfin_api;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->settings = new SystemSettings($this->conn);
        
        // Initialize APIs
        $emby_url = $this->settings->get('emby_server_url');
        $emby_key = $this->settings->get('emby_api_key');
        $jellyfin_url = $this->settings->get('jellyfin_server_url');
        $jellyfin_key = $this->settings->get('jellyfin_api_key');
        
        if($emby_url && $emby_key) {
            $this->emby_api = new EmbyAPI($emby_url, $emby_key);
        }
        
        if($jellyfin_url && $jellyfin_key) {
            $this->jellyfin_api = new JellyfinAPI($jellyfin_url, $jellyfin_key);
        }
    }
    
    public function register($username, $email, $password, $full_name, $phone = '') {
        try {
            // Check if user already exists
            if($this->userExists($username, $email)) {
                return [
                    'success' => false,
                    'message' => 'ชื่อผู้ใช้หรืออีเมลนี้มีอยู่ในระบบแล้ว'
                ];
            }
            
            $this->conn->beginTransaction();
            
            // Create user in main system
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
            
            $query = "INSERT INTO users (username, email, password_hash, full_name, phone) 
                      VALUES (:username, :email, :password_hash, :full_name, :phone)";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':password_hash', $password_hash);
            $stmt->bindParam(':full_name', $full_name);
            $stmt->bindParam(':phone', $phone);
            
            if(!$stmt->execute()) {
                throw new Exception('ไม่สามารถสร้างบัญชีผู้ใช้ได้');
            }
            
            $user_id = $this->conn->lastInsertId();
            
            // Create Emby account (optional - will continue even if fails)
            if($this->emby_api) {
                try {
                    error_log('UserManager: Starting Emby account creation for user ' . $username . ' (ID: ' . $user_id . ')');
                    $emby_result = $this->createEmbyAccount($user_id, $username, $password);
                    if($emby_result['success']) {
                        error_log('UserManager: Emby account creation successful for user ' . $username);
                    } else {
                        error_log('UserManager: Emby account creation failed for user ' . $username . ': ' . ($emby_result['message'] ?? 'Unknown error'));
                    }
                } catch(Exception $e) {
                    error_log('UserManager: Emby account creation exception for user ' . $username . ': ' . $e->getMessage());
                }
            } else {
                error_log('UserManager: Emby API not available');
            }

            // Create Jellyfin account (optional - will continue even if fails)
            if($this->jellyfin_api) {
                try {
                    $jellyfin_result = $this->createJellyfinAccount($user_id, $username, $password);
                    if(!$jellyfin_result['success']) {
                        error_log('Jellyfin account creation failed: ' . ($jellyfin_result['message'] ?? 'Unknown error'));
                    }
                } catch(Exception $e) {
                    error_log('Jellyfin account creation error: ' . $e->getMessage());
                }
            }
            
            $this->conn->commit();
            
            // Log the registration
            $this->logUserAction($user_id, 'register', 'User registered successfully');
            
            return [
                'success' => true,
                'message' => 'สมัครสมาชิกสำเร็จ',
                'user_id' => $user_id
            ];
            
        } catch(Exception $e) {
            $this->conn->rollback();
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
            ];
        }
    }
    
    private function createEmbyAccount($user_id, $username, $password) {
        try {
            error_log('UserManager::createEmbyAccount: Starting for user ' . $username . ' with password length ' . strlen($password));

            $result = $this->emby_api->createUser($username, $password);
            error_log('UserManager::createEmbyAccount: API result: ' . print_r($result, true));

            if($result['success']) {
                $emby_user_id = $result['data']['Id'] ?? '';
                error_log('UserManager::createEmbyAccount: Got Emby user ID: ' . $emby_user_id);

                $query = "INSERT INTO emby_accounts (user_id, emby_user_id, emby_username, emby_password, server_url, api_key)
                          VALUES (:user_id, :emby_user_id, :username, :password, :server_url, :api_key)";

                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':emby_user_id', $emby_user_id);
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':password', $password);
                $stmt->bindValue(':server_url', $this->settings->get('emby_server_url'));
                $stmt->bindValue(':api_key', $this->settings->get('emby_api_key'));

                $execute_result = $stmt->execute();
                error_log('UserManager::createEmbyAccount: Database insert result: ' . ($execute_result ? 'success' : 'failed'));

                if($execute_result) {
                    error_log('UserManager::createEmbyAccount: Successfully saved to database');
                    return ['success' => true];
                } else {
                    error_log('UserManager::createEmbyAccount: Database insert failed');
                    return [
                        'success' => false,
                        'message' => 'Failed to save Emby account to database'
                    ];
                }
            }

            error_log('UserManager::createEmbyAccount: API call failed');
            return [
                'success' => false,
                'message' => 'Failed to create Emby user: ' . ($result['message'] ?? 'Unknown error')
            ];

        } catch(Exception $e) {
            error_log('UserManager::createEmbyAccount: Exception: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    private function createJellyfinAccount($user_id, $username, $password) {
        try {
            $result = $this->jellyfin_api->createUser($username, $password);
            
            if($result['success']) {
                $jellyfin_user_id = $result['data']['Id'] ?? '';
                
                $query = "INSERT INTO jellyfin_accounts (user_id, jellyfin_user_id, jellyfin_username, jellyfin_password, server_url, api_key) 
                          VALUES (:user_id, :jellyfin_user_id, :username, :password, :server_url, :api_key)";
                
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':jellyfin_user_id', $jellyfin_user_id);
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':password', $password);
                $stmt->bindValue(':server_url', $this->settings->get('jellyfin_server_url'));
                $stmt->bindValue(':api_key', $this->settings->get('jellyfin_api_key'));
                
                $stmt->execute();
                
                return ['success' => true];
            }
            
            return [
                'success' => false,
                'message' => 'Failed to create Jellyfin user'
            ];
            
        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    public function login($username, $password) {
        $query = "SELECT * FROM users WHERE (username = :username OR (email IS NOT NULL AND email != '' AND email = :username)) AND status = 'active'";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        if($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if(password_verify($password, $user['password_hash'])) {
                // Log the login
                $this->logUserAction($user['id'], 'login', 'User logged in');
                
                // Remove sensitive data
                unset($user['password_hash']);
                
                return [
                    'success' => true,
                    'user' => $user
                ];
            }
        }
        
        return [
            'success' => false,
            'message' => 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง'
        ];
    }
    
    private function userExists($username, $email) {
        if(empty($email)) {
            // If email is empty, only check username
            $query = "SELECT id FROM users WHERE username = :username";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':username', $username);
        } else {
            // If email is provided, check both username and email
            $query = "SELECT id FROM users WHERE username = :username OR email = :email";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':email', $email);
        }

        $stmt->execute();
        return $stmt->rowCount() > 0;
    }
    
    public function logUserAction($user_id, $action, $description, $ip = null, $user_agent = null) {
        $query = "INSERT INTO usage_logs (user_id, action, description, ip_address, user_agent) 
                  VALUES (:user_id, :action, :description, :ip, :user_agent)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':ip', $ip);
        $stmt->bindParam(':user_agent', $user_agent);
        
        return $stmt->execute();
    }
    
    public function getUserById($user_id) {
        $query = "SELECT * FROM users WHERE id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        if($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            unset($user['password_hash']);
            return $user;
        }
        
        return null;
    }
}
?>
