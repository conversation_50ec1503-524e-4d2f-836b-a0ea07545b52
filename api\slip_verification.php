<?php
/**
 * API Endpoint สำหรับระบบตรวจสอบสลิปอัตโนมัติ
 * 
 * Endpoints:
 * - GET /api/slip_verification.php?action=status - ดูสถานะระบบ
 * - POST /api/slip_verification.php?action=verify - ตรวจสอบสลิปทันที
 * - GET /api/slip_verification.php?action=transactions - ดูธุรกรรมล่าสุด
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../classes/SlipVerificationManager.php';

// ตรวจสอบ API key (optional)
$api_key = $_GET['api_key'] ?? $_POST['api_key'] ?? null;
$valid_api_keys = [
    'admin_key_123',  // สำหรับ admin
    'cron_key_456'    // สำหรับ cron job
];

// สำหรับ demo ไม่ต้องตรวจสอบ API key
// if (!in_array($api_key, $valid_api_keys)) {
//     http_response_code(401);
//     echo json_encode(['error' => 'Invalid API key']);
//     exit;
// }

$action = $_GET['action'] ?? $_POST['action'] ?? 'status';

try {
    $slipManager = new SlipVerificationManager();
    
    switch ($action) {
        case 'status':
            // ดูสถานะระบบ
            $database = new Database();
            $conn = $database->getConnection();
            
            // นับการเติมเงินที่รอการอนุมัติ
            $query = "SELECT COUNT(*) as pending_count FROM top_ups WHERE status = 'pending'";
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $pending = $stmt->fetch(PDO::FETCH_ASSOC);

            // นับการเติมเงินที่อนุมัติแล้ววันนี้
            $query = "SELECT COUNT(*) as approved_today FROM top_ups
                      WHERE status = 'approved'
                      AND DATE(approved_at) = CURDATE()";
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $approved_today = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // ดูข้อมูลธุรกรรมล่าสุด
            $transactionResult = $slipManager->getTransactionsFromFile();
            
            echo json_encode([
                'success' => true,
                'status' => [
                    'pending_topups' => $pending['pending_count'],
                    'approved_today' => $approved_today['approved_today'],
                    'transactions_available' => $transactionResult['success'],
                    'transaction_count' => $transactionResult['success'] ? count($transactionResult['data']) : 0,
                    'last_updated' => $transactionResult['last_updated'] ?? null,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
            break;
            
        case 'verify':
            // ตรวจสอบสลิปทันที
            $result = $slipManager->processAutoApproval();
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Verification completed successfully',
                    'data' => [
                        'processed' => $result['processed'],
                        'approved' => $result['approved'],
                        'results' => $result['results']
                    ]
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => $result['message']
                ]);
            }
            break;
            
        case 'transactions':
            // ดูธุรกรรมล่าสุด
            $transactionResult = $slipManager->getTransactionsFromFile();
            
            if ($transactionResult['success']) {
                echo json_encode([
                    'success' => true,
                    'data' => $transactionResult['data'],
                    'last_updated' => $transactionResult['last_updated']
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => $transactionResult['message']
                ]);
            }
            break;
            
        case 'fetch':
            // ดึงข้อมูลจาก API ใหม่
            $result = $slipManager->fetchTransactionsFromAPI();
            
            if ($result['success']) {
                // บันทึกลงไฟล์
                $data = [
                    'transactions' => $result['data'],
                    'last_updated' => date('Y-m-d H:i:s')
                ];
                
                $dataFile = __DIR__ . '/data/transactions.json';
                $dataDir = dirname($dataFile);
                
                if (!is_dir($dataDir)) {
                    mkdir($dataDir, 0755, true);
                }
                
                file_put_contents($dataFile, json_encode($data, JSON_PRETTY_PRINT));
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Transactions fetched and saved successfully',
                    'count' => count($result['data'])
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => $result['message']
                ]);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action. Available actions: status, verify, transactions, fetch'
            ]);
            break;
    }
    
} catch (Exception $e) {
    error_log('Slip Verification API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage()
    ]);
}
?>
