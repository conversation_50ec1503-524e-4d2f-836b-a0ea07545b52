<?php
class EmbyAPI {
    private $server_url;
    private $api_key;
    
    public function __construct($server_url, $api_key) {
        $this->server_url = rtrim($server_url, '/');
        $this->api_key = $api_key;
    }
    
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->server_url . '/emby' . $endpoint;

        $headers = [
            'X-Emby-Token: ' . $this->api_key,
            'Content-Type: application/json'
        ];

        // Check if cURL is available
        if(function_exists('curl_init')) {
            return $this->makeRequestWithCurl($url, $method, $data, $headers, $endpoint);
        } else {
            return $this->makeRequestWithFileGetContents($url, $method, $data, $headers, $endpoint);
        }
    }

    private function makeRequestWithCurl($url, $method, $data, $headers, $endpoint = '') {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        switch($method) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if($response === false) {
            throw new Exception('cURL Error: ' . curl_error($ch));
        }

        $decoded = json_decode($response, true);

        // Log API calls for debugging (only for policy-related endpoints)
        if(!empty($endpoint) && strpos($endpoint, '/Policy') !== false) {
            error_log('Emby API Debug: ' . $method . ' ' . $endpoint .
                     ' - HTTP ' . $http_code .
                     ' - Response: ' . substr($response, 0, 200));
        }

        return [
            'success' => $http_code >= 200 && $http_code < 300,
            'http_code' => $http_code,
            'data' => $decoded,
            'raw' => $response,
            'message' => $http_code >= 400 ? 'HTTP Error ' . $http_code : null
        ];
    }

    private function makeRequestWithFileGetContents($url, $method, $data, $headers, $endpoint = '') {
        // For now, return a mock response when cURL is not available
        // In production, you would implement file_get_contents with stream context
        return [
            'success' => false,
            'http_code' => 500,
            'data' => null,
            'raw' => '',
            'error' => 'cURL extension not available. Please enable cURL in PHP.'
        ];
    }
    
    public function createUser($username, $password, $name = '') {
        error_log('EmbyAPI: Starting user creation for ' . $username);

        // Step 1: Create user without password first (most reliable method)
        $userData = [
            'Name' => $username
        ];

        if($name) {
            $userData['Name'] = $name;
        }

        error_log('EmbyAPI: Creating user without password first');
        $createResult = $this->makeRequest('/Users/<USER>', 'POST', $userData);

        if(!$createResult['success']) {
            error_log('EmbyAPI: User creation failed: ' . print_r($createResult, true));
            return $createResult;
        }

        if(!isset($createResult['data']['Id'])) {
            error_log('EmbyAPI: User creation succeeded but no ID returned');
            return [
                'success' => false,
                'message' => 'User created but no ID returned'
            ];
        }

        $userId = $createResult['data']['Id'];
        error_log('EmbyAPI: User created successfully with ID: ' . $userId);

        // Step 2: Set password using the most reliable method
        $passwordSet = false;
        if(!empty($password)) {
            error_log('EmbyAPI: Setting password for user ' . $username);
            $passwordSet = $this->setUserPasswordReliable($userId, $username, $password);
        }

        // Step 3: Set user policy to enable remote access (Allow remote connections)
        error_log('EmbyAPI: Setting user policy with remote access enabled');
        $policyResult = $this->setUserPolicyWithRemoteAccess($userId);
        if(!$policyResult['success']) {
            error_log('EmbyAPI: Failed to set user policy: ' . $policyResult['message']);
        }

        // Step 4: Final verification
        $finalStatus = [
            'user_created' => true,
            'password_set' => $passwordSet,
            'policy_set' => $policyResult['success'] ?? false
        ];

        error_log('EmbyAPI: User creation completed for ' . $username . ': ' . print_r($finalStatus, true));

        return [
            'success' => true,
            'data' => $createResult['data'],
            'message' => 'User created successfully',
            'password_set' => $passwordSet,
            'user_id' => $userId,
            'status' => $finalStatus
        ];
    }

    /**
     * Reliable method to set user password using multiple approaches
     */
    private function setUserPasswordReliable($userId, $username, $password) {
        error_log('EmbyAPI: setUserPasswordReliable starting for user ' . $username . ' with password length ' . strlen($password));

        // Wait a moment for user creation to complete
        sleep(1);

        // Method 1: Standard password reset with ResetPassword flag
        error_log('EmbyAPI: Method 1 - Standard password reset');
        $resetData = [
            'Id' => $userId,
            'CurrentPw' => '',
            'NewPw' => $password,
            'ResetPassword' => true
        ];

        $resetResult = $this->makeRequest('/Users/' . $userId . '/Password', 'POST', $resetData);
        error_log('EmbyAPI: Method 1 result: ' . print_r($resetResult, true));

        if($resetResult['success']) {
            sleep(1); // Wait for password to be set
            $verifyResult = $this->verifyUserPassword($userId, $username, $password);
            if($verifyResult['success']) {
                error_log('EmbyAPI: Method 1 succeeded and verified');
                return true;
            } else {
                error_log('EmbyAPI: Method 1 API success but verification failed');
            }
        }

        // Method 2: Alternative password format
        error_log('EmbyAPI: Method 2 - Alternative password format');
        $altData = [
            'CurrentPassword' => '',
            'NewPassword' => $password
        ];

        $altResult = $this->makeRequest('/Users/' . $userId . '/Password', 'POST', $altData);
        error_log('EmbyAPI: Method 2 result: ' . print_r($altResult, true));

        if($altResult['success']) {
            sleep(1);
            $verifyResult = $this->verifyUserPassword($userId, $username, $password);
            if($verifyResult['success']) {
                error_log('EmbyAPI: Method 2 succeeded and verified');
                return true;
            }
        }

        // Method 3: Direct user update with password
        error_log('EmbyAPI: Method 3 - Direct user update');
        $userResult = $this->makeRequest('/Users/' . $userId);
        if($userResult['success']) {
            $userData = $userResult['data'];

            // Set password fields
            $userData['Password'] = $password;
            $userData['HasPassword'] = true;
            $userData['HasConfiguredPassword'] = true;

            // Ensure policy exists and has password settings
            if(!isset($userData['Policy'])) {
                $userData['Policy'] = [];
            }
            $userData['Policy']['EnableLocalPassword'] = true;

            $updateResult = $this->makeRequest('/Users/' . $userId, 'POST', $userData);
            error_log('EmbyAPI: Method 3 result: ' . print_r($updateResult, true));

            if($updateResult['success']) {
                sleep(1);
                $verifyResult = $this->verifyUserPassword($userId, $username, $password);
                if($verifyResult['success']) {
                    error_log('EmbyAPI: Method 3 succeeded and verified');
                    return true;
                }
            }
        }

        // Method 4: Minimal user data approach
        error_log('EmbyAPI: Method 4 - Minimal user data');
        $minimalData = [
            'Id' => $userId,
            'Name' => $username,
            'Password' => $password,
            'HasPassword' => true,
            'HasConfiguredPassword' => true,
            'Policy' => [
                'EnableLocalPassword' => true,
                'IsDisabled' => false
            ]
        ];

        $minimalResult = $this->makeRequest('/Users/' . $userId, 'POST', $minimalData);
        error_log('EmbyAPI: Method 4 result: ' . print_r($minimalResult, true));

        if($minimalResult['success']) {
            sleep(1);
            $verifyResult = $this->verifyUserPassword($userId, $username, $password);
            if($verifyResult['success']) {
                error_log('EmbyAPI: Method 4 succeeded and verified');
                return true;
            }
        }

        // Method 5: Force password without current password
        error_log('EmbyAPI: Method 5 - Force password without current');
        $forceData = [
            'CurrentPw' => '',
            'NewPw' => $password
        ];

        $forceResult = $this->makeRequest('/Users/' . $userId . '/Password', 'POST', $forceData);
        error_log('EmbyAPI: Method 5 result: ' . print_r($forceResult, true));

        if($forceResult['success']) {
            sleep(1);
            $verifyResult = $this->verifyUserPassword($userId, $username, $password);
            if($verifyResult['success']) {
                error_log('EmbyAPI: Method 5 succeeded and verified');
                return true;
            }
        }

        error_log('EmbyAPI: All 5 password setting methods failed for user ' . $username);

        // Final check - see what the user data looks like now
        $finalUserResult = $this->makeRequest('/Users/' . $userId);
        if($finalUserResult['success']) {
            $finalUserData = $finalUserResult['data'];
            error_log('EmbyAPI: Final user data: HasPassword=' . ($finalUserData['HasPassword'] ?? 'null') .
                     ', HasConfiguredPassword=' . ($finalUserData['HasConfiguredPassword'] ?? 'null'));
        }

        return false;
    }

    /**
     * Secure user policy setup with hidden from login screens
     */
    private function setSecureUserPolicy($userId) {
        error_log('EmbyAPI: Setting secure user policy for user ' . $userId);

        try {
            // Get current policy
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];
            } else {
                // Create default policy if none exists
                $policy = [];
            }

            // Set secure policy settings
            $policy['IsAdministrator'] = false;
            $policy['IsDisabled'] = true;  // Disable this user initially
            $policy['IsHidden'] = true;  // Hide this user from login screens on the local network
            $policy['IsHiddenRemotely'] = true;  // Hide from remote login
            $policy['IsHiddenFromUnusedDevices'] = true;  // Hide from unused devices
            $policy['EnableRemoteAccess'] = false;  // Disable remote access initially
            $policy['EnableRemoteControlOfOtherUsers'] = false;
            $policy['EnableLocalNetworkAccess'] = true;
            $policy['EnableMediaPlayback'] = true;
            $policy['EnableAudioPlaybackTranscoding'] = true;
            $policy['EnableVideoPlaybackTranscoding'] = true;
            $policy['EnablePlaybackRemuxing'] = true;
            $policy['EnableContentDeletion'] = false;
            $policy['EnableContentDownloading'] = false;
            $policy['EnableUserPreferenceAccess'] = true;
            $policy['LoginAttemptsBeforeLockout'] = -1;
            $policy['EnableLocalPassword'] = true;  // Enable local password

            error_log('EmbyAPI: Setting policy with IsDisabled=true, IsHidden=true, IsHiddenRemotely=true, IsHiddenFromUnusedDevices=true');

            $updateResult = $this->updateUserPolicy($userId, $policy);

            if($updateResult['success']) {
                error_log('EmbyAPI: Secure user policy set successfully');

                // Also update user configuration to ensure hiding works
                $this->setUserHiddenConfiguration($userId);

                return ['success' => true, 'message' => 'Secure policy set with hidden flags'];
            } else {
                error_log('EmbyAPI: Failed to set secure user policy: ' . ($updateResult['message'] ?? 'Unknown error'));
                return $updateResult;
            }

        } catch(Exception $e) {
            error_log('EmbyAPI: Exception setting secure user policy: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Set user configuration to hide from login screens
     */
    private function setUserHiddenConfiguration($userId) {
        try {
            error_log('EmbyAPI: Setting user hidden configuration for user ' . $userId);

            $userResult = $this->makeRequest('/Users/' . $userId);
            if($userResult['success']) {
                $userData = $userResult['data'];

                // Ensure Configuration object exists
                if(!isset($userData['Configuration'])) {
                    $userData['Configuration'] = [];
                }

                // Set configuration to hide user
                $userData['Configuration']['IsHidden'] = true;
                $userData['Configuration']['HidePlayedInLatest'] = true;
                $userData['Configuration']['DisplayMissingEpisodes'] = false;
                $userData['Configuration']['EnableLocalPassword'] = true;

                // Update user with new configuration
                $updateResult = $this->makeRequest('/Users/' . $userId, 'POST', $userData);

                if($updateResult['success']) {
                    error_log('EmbyAPI: User hidden configuration set successfully');
                } else {
                    error_log('EmbyAPI: Failed to set user hidden configuration');
                }

                return $updateResult;
            }

            return $userResult;

        } catch(Exception $e) {
            error_log('EmbyAPI: Exception setting user hidden configuration: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Enable user for active subscription
     */
    public function enableUserForSubscription($userId) {
        try {
            error_log('EmbyAPI: Enabling user for subscription: ' . $userId);

            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Enable user when package is assigned (Unchecked "Disable This user")
                $policy['IsDisabled'] = false;  // Unchecked "Disable This user" when package assigned
                $policy['IsHidden'] = true;     // ซ่อนจากหน้า login เพื่อความปลอดภัย
                $policy['IsHiddenRemotely'] = true;  // ซ่อนจาก remote login
                $policy['IsHiddenFromUnusedDevices'] = true;  // ซ่อนจาก unused devices
                $policy['EnableRemoteAccess'] = true;  // Enable remote access (Allow remote connections)
                $policy['EnableLocalNetworkAccess'] = true;  // Allow local network access

                error_log('EmbyAPI: Enabling user with package assigned (Unchecked Disable This user)');

                $updateResult = $this->updateUserPolicy($userId, $policy);

                if($updateResult['success']) {
                    error_log('EmbyAPI: User enabled for subscription successfully');
                    return ['success' => true, 'message' => 'User enabled for subscription'];
                } else {
                    error_log('EmbyAPI: Failed to enable user for subscription: ' . ($updateResult['message'] ?? 'Unknown error'));
                    return $updateResult;
                }
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('EmbyAPI: Exception enabling user for subscription: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Disable user when subscription expires
     */
    public function disableUserForExpiredSubscription($userId) {
        try {
            error_log('EmbyAPI: Disabling user for expired subscription: ' . $userId);

            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Disable user (Checked "Disable This user") and hide for security
                $policy['IsDisabled'] = true;   // This is the main setting - Disable this user

                // Hide user from login screens for security
                $policy['IsHidden'] = true;     // ซ่อนจากหน้า login เพื่อความปลอดภัย
                $policy['IsHiddenRemotely'] = true;  // ซ่อนจาก remote login
                $policy['IsHiddenFromUnusedDevices'] = true;  // ซ่อนจาก unused devices

                // Disable access (Unchecked "Allow remote connections to this Emby Server")
                $policy['EnableRemoteAccess'] = false;  // Unchecked "Allow remote connections"
                $policy['EnableLocalNetworkAccess'] = false;  // Disable local network access

                error_log('EmbyAPI: Setting policy with IsDisabled=true, hidden, and remote access disabled for expired subscription');

                $updateResult = $this->updateUserPolicy($userId, $policy);

                if($updateResult['success']) {
                    error_log('EmbyAPI: User disabled for expired subscription successfully');
                    return ['success' => true, 'message' => 'User disabled for expired subscription'];
                } else {
                    error_log('EmbyAPI: Failed to disable user for expired subscription: ' . ($updateResult['message'] ?? 'Unknown error'));
                    return $updateResult;
                }
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('EmbyAPI: Exception disabling user for expired subscription: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ตั้งค่า policy เริ่มต้นสำหรับ user ใหม่
     * - Hide from login screens
     * - Disable remote access
     */
    public function setInitialUserPolicy($userId) {
        try {
            // Step 1: Set user configuration to hide from login (based on Emby community)
            $configuration = [
                'IsHidden' => false,  // Keep false for main IsHidden
                'IsHiddenRemotely' => true,  // Hide from remote login
                'IsHiddenFromUnusedDevices' => false,  // Don't hide from unused devices
                'HidePlayedInLatest' => true,
                'DisplayMissingEpisodes' => false,
                'EnableLocalPassword' => false,
                'GroupedFolders' => [],
                'SubtitleMode' => 'Default',
                'DisplayCollectionsView' => false,
                'EnableNextEpisodeAutoPlay' => true,
                'RememberAudioSelections' => true,
                'RememberSubtitleSelections' => true
            ];

            $configResult = $this->updateUserConfiguration($userId, $configuration);

            // Step 2: Set user policy
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Hide user from login screens (based on Emby community examples)
                $policy['IsHidden'] = false;  // Keep false for main IsHidden
                $policy['IsHiddenRemotely'] = true;  // Hide from remote login
                $policy['IsHiddenFromUnusedDevices'] = false;  // Don't hide from unused devices
                $policy['LoginAttemptsBeforeLockout'] = -1;
                $policy['EnablePublicSharing'] = true;  // Allow public sharing
                $policy['EnableSharedDeviceControl'] = true;  // Allow shared device control

                // Disable remote access initially (Unchecked "Allow remote connections to this Emby Server")
                $policy['EnableRemoteAccess'] = false;
                $policy['EnableRemoteControlOfOtherUsers'] = false;
                $policy['ForceRemoteSourceTranscoding'] = false;
                $policy['EnableLiveTvAccess'] = true;  // Allow Live TV access
                $policy['EnableLiveTvManagement'] = true;  // Allow Live TV management

                // Allow local network access only
                $policy['EnableLocalNetworkAccess'] = true;

                // Basic permissions
                $policy['IsAdministrator'] = false;
                $policy['IsDisabled'] = false;

                // Media permissions
                $policy['EnableMediaPlayback'] = true;
                $policy['EnableAudioPlaybackTranscoding'] = true;
                $policy['EnableVideoPlaybackTranscoding'] = true;
                $policy['EnablePlaybackRemuxing'] = true;
                $policy['EnableContentDeletion'] = false;
                $policy['EnableContentDownloading'] = false;
                $policy['EnableSyncTranscoding'] = false;
                $policy['EnableMediaConversion'] = false;
                $policy['EnableUserPreferenceAccess'] = true;

                $policyUpdateResult = $this->updateUserPolicy($userId, $policy);

                // Log for debugging
                error_log('Emby: Set initial policy for user ' . $userId .
                         ' - Config: ' . ($configResult['success'] ? 'success' : 'failed') .
                         ', Policy: ' . ($policyUpdateResult['success'] ? 'success' : 'failed'));

                return $policyUpdateResult;
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('Emby: Failed to set initial user policy: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ตั้งค่า user policy สำหรับผู้ใช้ใหม่ พร้อมเปิด remote access
     * (Checked "Allow remote connections to this Emby Server")
     */
    public function setUserPolicyWithRemoteAccess($userId) {
        try {
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Disable user initially (Checked "Disable This user") until package is assigned
                $policy['IsDisabled'] = true;  // Checked "Disable This user" for new users
                $policy['IsHidden'] = true;  // ซ่อนจากหน้า login เพื่อความปลอดภัย
                $policy['IsHiddenRemotely'] = true;  // ซ่อนจาก remote login
                $policy['IsHiddenFromUnusedDevices'] = true;  // ซ่อนจาก unused devices

                // Enable remote access (Check "Allow remote connections to this Emby Server")
                $policy['EnableRemoteAccess'] = true;
                $policy['EnableLocalNetworkAccess'] = true;
                $policy['EnableRemoteControlOfOtherUsers'] = false; // Keep false for security
                $policy['ForceRemoteSourceTranscoding'] = false; // Keep false for performance
                $policy['EnableLiveTvAccess'] = true;
                $policy['EnableLiveTvManagement'] = false; // Keep false for security

                // Basic permissions
                $policy['EnableUserPreferenceAccess'] = true;
                $policy['EnablePlaybackRemuxing'] = true;
                $policy['EnableVideoPlaybackTranscoding'] = true;
                $policy['EnableAudioPlaybackTranscoding'] = true;
                $policy['EnableContentDeletion'] = false;
                $policy['EnableContentDownloading'] = true;
                $policy['EnableSyncTranscoding'] = true;
                $policy['EnableMediaConversion'] = false;
                $policy['EnableAllDevices'] = true;
                $policy['EnableAllChannels'] = true;
                $policy['EnableAllFolders'] = true;
                $policy['EnablePublicSharing'] = false;
                $policy['EnableRemoteAccess'] = true; // This is the key setting!

                error_log('EmbyAPI: Setting policy with user disabled initially until package is assigned');
                return $this->updateUserPolicy($userId, $policy);
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('EmbyAPI: Exception in setUserPolicyWithRemoteAccess: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * ปิดการใช้งาน remote access สำหรับ user ที่ subscription inactive
     * (Unchecked "Allow remote connections to this Emby Server")
     */
    public function disableRemoteAccessForInactiveSubscription($userId) {
        try {
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Keep user enabled but disable remote access
                $policy['IsDisabled'] = false;  // Keep user enabled
                $policy['IsHidden'] = true;     // ซ่อนจากหน้า login เพื่อความปลอดภัย
                $policy['IsHiddenRemotely'] = true;  // ซ่อนจาก remote login
                $policy['IsHiddenFromUnusedDevices'] = true;  // ซ่อนจาก unused devices

                // Disable remote access (Unchecked "Allow remote connections to this Emby Server")
                $policy['EnableRemoteAccess'] = false;  // Unchecked "Allow remote connections"
                $policy['EnableLocalNetworkAccess'] = true;  // Keep local network access

                error_log('EmbyAPI: Disabling remote access for inactive subscription');
                return $this->updateUserPolicy($userId, $policy);
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('EmbyAPI: Exception in disableRemoteAccessForInactiveSubscription: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * เปิดใช้งาน remote access สำหรับ user
     * (Checked "Allow remote connections to this Emby Server")
     */
    public function enableRemoteAccess($userId) {
        try {
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Enable remote access (Check "Allow remote connections to this Emby Server")
                $policy['EnableRemoteAccess'] = true;
                $policy['EnableRemoteControlOfOtherUsers'] = true;
                $policy['ForceRemoteSourceTranscoding'] = false; // Keep false for performance
                $policy['EnableLiveTvAccess'] = true;
                $policy['EnableLiveTvManagement'] = false; // Keep false for security

                return $this->updateUserPolicy($userId, $policy);
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('Emby: Failed to enable remote access: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ปิดใช้งาน remote access สำหรับ user
     * (Unchecked "Allow remote connections to this Emby Server")
     */
    public function disableRemoteAccess($userId) {
        try {
            // Try multiple approaches to disable remote access

            // Approach 1: Get current policy and update
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Disable remote access (Uncheck "Allow remote connections to this Emby Server")
                $policy['EnableRemoteAccess'] = false;
                $policy['EnableRemoteControlOfOtherUsers'] = false;
                $policy['ForceRemoteSourceTranscoding'] = false;
                $policy['EnableLiveTvAccess'] = false;
                $policy['EnableLiveTvManagement'] = false;

                $updateResult = $this->updateUserPolicy($userId, $policy);

                if($updateResult['success']) {
                    // Verify the change
                    $verifyResult = $this->getUserPolicy($userId);
                    if($verifyResult['success']) {
                        $isDisabled = !isset($verifyResult['data']['EnableRemoteAccess']) || !$verifyResult['data']['EnableRemoteAccess'];

                        if($isDisabled) {
                            return ['success' => true, 'message' => 'Remote access disabled successfully'];
                        } else {
                            // Try approach 2: Direct policy update
                            $directResult = $this->forceDisableRemoteAccessDirect($userId);

                            if($directResult['success']) {
                                return $directResult;
                            } else {
                                // Try approach 3: Alternative method with multiple variations
                                return $this->disableRemoteAccessAlternative($userId);
                            }
                        }
                    }
                }

                return $updateResult;
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('Emby: Failed to disable remote access: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Force disable remote access using direct API calls based on Emby community examples
     */
    private function forceDisableRemoteAccessDirect($userId) {
        try {
            // Method 1: Get complete user data and update (based on community examples)
            $userResult = $this->makeRequest('/Users/' . $userId);
            if($userResult['success']) {
                $userData = $userResult['data'];

                // Based on Emby community examples, we need to send the complete policy
                if(isset($userData['Policy'])) {
                    $policy = $userData['Policy'];

                    // Set remote access to false (key finding from community)
                    $policy['EnableRemoteAccess'] = false;
                    $policy['EnableRemoteControlOfOtherUsers'] = false;
                    $policy['ForceRemoteSourceTranscoding'] = false;

                    // Try updating just the policy with complete data
                    $policyResult = $this->makeRequest('/Users/' . $userId . '/Policy', 'POST', $policy);

                    if($policyResult['success']) {
                        // Verify the change took effect
                        $verifyResult = $this->makeRequest('/Users/' . $userId);
                        if($verifyResult['success'] && isset($verifyResult['data']['Policy'])) {
                            $newPolicy = $verifyResult['data']['Policy'];
                            $isDisabled = !isset($newPolicy['EnableRemoteAccess']) || !$newPolicy['EnableRemoteAccess'];

                            if($isDisabled) {
                                return ['success' => true, 'message' => 'Remote access disabled via complete policy update'];
                            }
                        }
                    }

                    // Method 2: Update entire user object (fallback)
                    $userData['Policy'] = $policy;
                    $userUpdateResult = $this->makeRequest('/Users/' . $userId, 'POST', $userData);

                    if($userUpdateResult['success']) {
                        return ['success' => true, 'message' => 'Remote access disabled via complete user update'];
                    }
                }
            }

            // Method 3: Try with minimal policy structure (last resort)
            $minimalPolicy = [
                'EnableRemoteAccess' => false,
                'EnableRemoteControlOfOtherUsers' => false,
                'EnableLocalNetworkAccess' => true,
                'IsAdministrator' => false,
                'IsHidden' => false,
                'IsDisabled' => false
            ];

            $minimalResult = $this->makeRequest('/Users/' . $userId . '/Policy', 'POST', $minimalPolicy);

            if($minimalResult['success']) {
                return ['success' => true, 'message' => 'Remote access disabled via minimal policy'];
            }

            return [
                'success' => false,
                'message' => 'All direct methods failed',
                'details' => [
                    'user_fetch' => isset($userResult) ? $userResult['success'] : false,
                    'policy_update' => isset($policyResult) ? $policyResult['success'] : false,
                    'user_update' => isset($userUpdateResult) ? $userUpdateResult['success'] : false,
                    'minimal_update' => $minimalResult['success']
                ]
            ];

        } catch(Exception $e) {
            error_log('Emby: Failed to force disable remote access directly: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * ซ่อน user จาก login screens
     */
    public function hideUserFromLogin($userId) {
        try {
            // Get current user data
            $userResult = $this->makeRequest('/Users/' . $userId);

            if($userResult['success']) {
                $userData = $userResult['data'];

                // Ensure Configuration object exists
                if(!isset($userData['Configuration'])) {
                    $userData['Configuration'] = [];
                }

                // Set hide from login screens
                $userData['Configuration']['IsHidden'] = true;
                $userData['Configuration']['HidePlayedInLatest'] = true;
                $userData['Configuration']['DisplayMissingEpisodes'] = false;
                $userData['Configuration']['EnableLocalPassword'] = false;

                // Update user with new configuration
                $updateResult = $this->makeRequest('/Users/' . $userId, 'POST', $userData);

                if($updateResult['success']) {
                    // Also update policy separately
                    $policyResult = $this->getUserPolicy($userId);

                    if($policyResult['success']) {
                        $policy = $policyResult['data'];
                        $policy['IsHidden'] = true;
                        $policy['LoginAttemptsBeforeLockout'] = -1;

                        $policyUpdateResult = $this->updateUserPolicy($userId, $policy);

                        error_log('Emby: Hide user - Config: ' . ($updateResult['success'] ? 'success' : 'failed') .
                                 ', Policy: ' . ($policyUpdateResult['success'] ? 'success' : 'failed'));

                        return $policyUpdateResult;
                    }
                }

                return $updateResult;
            }

            return $userResult;

        } catch(Exception $e) {
            error_log('Emby: Failed to hide user from login: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * อัปเดต user configuration โดยตรง
     */
    public function updateUserConfiguration($userId, $configuration) {
        try {
            // Get current user data
            $userResult = $this->makeRequest('/Users/' . $userId);

            if($userResult['success']) {
                $userData = $userResult['data'];

                // Merge configuration
                if(!isset($userData['Configuration'])) {
                    $userData['Configuration'] = [];
                }

                $userData['Configuration'] = array_merge($userData['Configuration'], $configuration);

                // Update user
                return $this->makeRequest('/Users/' . $userId, 'POST', $userData);
            }

            return $userResult;

        } catch(Exception $e) {
            error_log('Emby: Failed to update user configuration: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function getUserByName($username) {
        $result = $this->makeRequest('/Users');
        
        if($result['success'] && isset($result['data']['Items'])) {
            foreach($result['data']['Items'] as $user) {
                if($user['Name'] === $username) {
                    return [
                        'success' => true,
                        'data' => $user
                    ];
                }
            }
        }
        
        return [
            'success' => false,
            'message' => 'User not found'
        ];
    }
    
    public function updateUserPassword($userId, $newPassword) {
        error_log('EmbyAPI: updateUserPassword called for user ' . $userId . ' with password length ' . strlen($newPassword));

        // Method 1: Standard Emby password reset (most reliable)
        $data1 = [
            'Id' => $userId,
            'CurrentPw' => '',
            'NewPw' => $newPassword,
            'ResetPassword' => true
        ];

        error_log('EmbyAPI: Trying method 1 - password reset');
        $result1 = $this->makeRequest('/Users/' . $userId . '/Password', 'POST', $data1);
        error_log('EmbyAPI: Method 1 result: ' . print_r($result1, true));

        if($result1['success']) {
            error_log('EmbyAPI: Method 1 succeeded');
            return $result1;
        }

        // Method 2: Official format from Emby community
        $data2 = [
            'CurrentPassword' => '',
            'NewPassword' => $newPassword
        ];

        error_log('EmbyAPI: Trying method 2 - official format');
        $result2 = $this->makeRequest('/Users/' . $userId . '/Password', 'POST', $data2);
        error_log('EmbyAPI: Method 2 result: ' . print_r($result2, true));

        if($result2['success']) {
            error_log('EmbyAPI: Method 2 succeeded');
            return $result2;
        }

        // Method 3: Alternative format
        $data3 = [
            'Id' => $userId,
            'CurrentPw' => '',
            'NewPw' => $newPassword
        ];

        error_log('EmbyAPI: Trying method 3 - alternative format');
        $result3 = $this->makeRequest('/Users/' . $userId . '/Password', 'POST', $data3);
        error_log('EmbyAPI: Method 3 result: ' . print_r($result3, true));

        if($result3['success']) {
            error_log('EmbyAPI: Method 3 succeeded');
            return $result3;
        }

        // Method 4: Direct user update with password
        error_log('EmbyAPI: Trying method 4 - direct user update');
        $userResult = $this->makeRequest('/Users/' . $userId);
        if($userResult['success']) {
            $userData = $userResult['data'];
            $userData['Password'] = $newPassword;
            $userData['HasPassword'] = true;
            $userData['HasConfiguredPassword'] = true;

            $result4 = $this->makeRequest('/Users/' . $userId, 'POST', $userData);
            error_log('EmbyAPI: Method 4 result: ' . print_r($result4, true));

            if($result4['success']) {
                error_log('EmbyAPI: Method 4 succeeded');
                return $result4;
            }
        }

        error_log('EmbyAPI: All password update methods failed');
        // Return the first error if all methods fail
        return $result1;
    }

    /**
     * Alternative method to set password by updating user data directly
     */
    public function setPasswordAlternative($userId, $password) {
        try {
            // Get current user data
            $userResult = $this->makeRequest('/Users/' . $userId);
            if(!$userResult['success']) {
                return $userResult;
            }

            $userData = $userResult['data'];

            // Try setting password in user data
            $userData['Password'] = $password;
            $userData['HasPassword'] = true;
            $userData['HasConfiguredPassword'] = true;

            // Update user
            $updateResult = $this->makeRequest('/Users/' . $userId, 'POST', $userData);

            if($updateResult['success']) {
                error_log('Emby: Alternative password method succeeded');
                return $updateResult;
            }

            // Try another approach - minimal user update with just password
            $minimalData = [
                'Id' => $userId,
                'Password' => $password,
                'HasPassword' => true
            ];

            $minimalResult = $this->makeRequest('/Users/' . $userId, 'POST', $minimalData);
            return $minimalResult;

        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => 'Alternative password method failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Force set password using direct user data update
     */
    public function forceSetPassword($userId, $password) {
        try {
            // Get current user data
            $userResult = $this->makeRequest('/Users/' . $userId);
            if(!$userResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Could not get user data: ' . ($userResult['message'] ?? 'Unknown error')
                ];
            }

            $userData = $userResult['data'];

            // Method 1: Try updating with password in user data
            $updateData = $userData;
            $updateData['Password'] = $password;
            $updateData['HasPassword'] = true;
            $updateData['HasConfiguredPassword'] = true;

            $updateResult = $this->makeRequest('/Users/' . $userId, 'POST', $updateData);
            if($updateResult['success']) {
                error_log('Emby: Force password method 1 succeeded');
                return $updateResult;
            }

            // Method 2: Try with minimal data
            $minimalData = [
                'Id' => $userId,
                'Name' => $userData['Name'],
                'Password' => $password,
                'HasPassword' => true,
                'HasConfiguredPassword' => true
            ];

            $minimalResult = $this->makeRequest('/Users/' . $userId, 'POST', $minimalData);
            if($minimalResult['success']) {
                error_log('Emby: Force password method 2 succeeded');
                return $minimalResult;
            }

            // Method 3: Try password change endpoint with empty current password
            $passwordData = [
                'Id' => $userId,
                'CurrentPw' => '',
                'NewPw' => $password,
                'ResetPassword' => true
            ];

            $passwordResult = $this->makeRequest('/Users/' . $userId . '/Password', 'POST', $passwordData);
            if($passwordResult['success']) {
                error_log('Emby: Force password method 3 succeeded');
                return $passwordResult;
            }

            return [
                'success' => false,
                'message' => 'All force password methods failed'
            ];

        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => 'Force password exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Verify that user password is set correctly
     */
    public function verifyUserPassword($userId, $username, $password) {
        try {
            error_log('EmbyAPI: Verifying password for user ' . $username . ' (ID: ' . $userId . ')');

            // First check if user has password set in user data
            $userResult = $this->makeRequest('/Users/' . $userId);
            if($userResult['success']) {
                $userData = $userResult['data'];
                $hasPassword = $userData['HasPassword'] ?? false;
                $hasConfiguredPassword = $userData['HasConfiguredPassword'] ?? false;

                error_log('EmbyAPI: User password flags - HasPassword: ' . ($hasPassword ? 'true' : 'false') .
                         ', HasConfiguredPassword: ' . ($hasConfiguredPassword ? 'true' : 'false'));

                // If no password is set according to flags, return false immediately
                if(!$hasPassword && !$hasConfiguredPassword) {
                    error_log('EmbyAPI: User has no password set according to flags');
                    return [
                        'success' => false,
                        'message' => 'User has no password set',
                        'has_password' => false,
                        'has_configured_password' => false
                    ];
                }
            }

            // Method 1: Try to authenticate with the password using Pw field
            $authData = [
                'Username' => $username,
                'Pw' => $password
            ];

            error_log('EmbyAPI: Trying authentication method 1 (Pw field)');
            $authResult = $this->makeRequest('/Users/<USER>', 'POST', $authData);
            error_log('EmbyAPI: Authentication result 1: ' . print_r($authResult, true));

            if($authResult['success']) {
                error_log('EmbyAPI: Authentication method 1 successful');
                return [
                    'success' => true,
                    'message' => 'Password verification successful'
                ];
            }

            // Method 2: Try alternative authentication format with Password field
            $authData2 = [
                'Username' => $username,
                'Password' => $password
            ];

            error_log('EmbyAPI: Trying authentication method 2 (Password field)');
            $authResult2 = $this->makeRequest('/Users/<USER>', 'POST', $authData2);
            error_log('EmbyAPI: Authentication result 2: ' . print_r($authResult2, true));

            if($authResult2['success']) {
                error_log('EmbyAPI: Authentication method 2 successful');
                return [
                    'success' => true,
                    'message' => 'Password verification successful (Password field)'
                ];
            }

            // Method 3: Try with both fields
            $authData3 = [
                'Username' => $username,
                'Pw' => $password,
                'Password' => $password
            ];

            error_log('EmbyAPI: Trying authentication method 3 (both fields)');
            $authResult3 = $this->makeRequest('/Users/<USER>', 'POST', $authData3);
            error_log('EmbyAPI: Authentication result 3: ' . print_r($authResult3, true));

            if($authResult3['success']) {
                error_log('EmbyAPI: Authentication method 3 successful');
                return [
                    'success' => true,
                    'message' => 'Password verification successful (both fields)'
                ];
            }

            // All authentication methods failed
            error_log('EmbyAPI: All authentication methods failed');
            return [
                'success' => false,
                'message' => 'Password verification failed - all authentication methods failed',
                'has_password' => $hasPassword ?? false,
                'has_configured_password' => $hasConfiguredPassword ?? false,
                'auth_errors' => [
                    'method1' => $authResult['message'] ?? 'Unknown error',
                    'method2' => $authResult2['message'] ?? 'Unknown error',
                    'method3' => $authResult3['message'] ?? 'Unknown error'
                ]
            ];

        } catch(Exception $e) {
            error_log('EmbyAPI: Password verification exception: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Password verification exception: ' . $e->getMessage()
            ];
        }
    }

    public function deleteUser($userId) {
        return $this->makeRequest('/Users/' . $userId, 'DELETE');
    }
    
    public function getUserPolicy($userId) {
        try {
            // Try multiple endpoints for getting user policy

            // Method 1: Standard policy endpoint
            $result = $this->makeRequest('/Users/' . $userId . '/Policy');
            if($result['success']) {
                return $result;
            }

            // Method 2: Get user data and extract policy
            $userResult = $this->makeRequest('/Users/' . $userId);
            if($userResult['success'] && isset($userResult['data']['Policy'])) {
                return [
                    'success' => true,
                    'data' => $userResult['data']['Policy']
                ];
            }

            // Method 3: Try alternative endpoint
            $altResult = $this->makeRequest('/Users/' . $userId . '/Configuration/Policy');
            if($altResult['success']) {
                return $altResult;
            }

            // Return error with details if all methods fail
            return [
                'success' => false,
                'message' => 'Failed to get user policy - HTTP ' . $result['http_code'],
                'http_code' => $result['http_code'],
                'details' => 'All policy endpoints failed'
            ];

        } catch(Exception $e) {
            error_log('Emby: Exception in getUserPolicy: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }
    
    public function updateUserPolicy($userId, $policy) {
        try {
            // Try multiple methods for updating user policy

            // Method 1: Standard policy endpoint
            $result = $this->makeRequest('/Users/' . $userId . '/Policy', 'POST', $policy);
            if($result['success']) {
                return $result;
            }

            // Method 2: Try PUT method
            $putResult = $this->makeRequest('/Users/' . $userId . '/Policy', 'PUT', $policy);
            if($putResult['success']) {
                return $putResult;
            }

            // Method 3: Update through user endpoint
            $userResult = $this->makeRequest('/Users/' . $userId);
            if($userResult['success']) {
                $userData = $userResult['data'];
                $userData['Policy'] = $policy;

                $updateResult = $this->makeRequest('/Users/' . $userId, 'POST', $userData);
                if($updateResult['success']) {
                    return $updateResult;
                }
            }

            // Return error with details if all methods fail
            return [
                'success' => false,
                'message' => 'Failed to update user policy - HTTP ' . $result['http_code'],
                'http_code' => $result['http_code'],
                'details' => 'All policy update methods failed'
            ];

        } catch(Exception $e) {
            error_log('Emby: Exception in updateUserPolicy: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }
    
    public function disableUser($userId) {
        $policy = $this->getUserPolicy($userId);
        
        if($policy['success']) {
            $policyData = $policy['data'];
            $policyData['IsDisabled'] = true;
            
            return $this->updateUserPolicy($userId, $policyData);
        }
        
        return $policy;
    }
    
    public function enableUser($userId) {
        $policy = $this->getUserPolicy($userId);
        
        if($policy['success']) {
            $policyData = $policy['data'];
            $policyData['IsDisabled'] = false;
            
            return $this->updateUserPolicy($userId, $policyData);
        }
        
        return $policy;
    }
    
    public function testConnection() {
        try {
            $result = $this->makeRequest('/System/Info');
            return $result['success'];
        } catch(Exception $e) {
            return false;
        }
    }
    
    public function getServerInfo() {
        return $this->makeRequest('/System/Info');
    }

    /**
     * บังคับปิด remote access และตรวจสอบให้แน่ใจ
     */
    public function forceDisableRemoteAccess($userId) {
        try {
            // Method 1: Disable through policy
            $disableResult = $this->disableRemoteAccess($userId);

            // Method 2: Get and verify current settings
            $policyResult = $this->getUserPolicy($userId);

            if($policyResult['success']) {
                $policy = $policyResult['data'];

                // Force all remote access flags to false
                $policy['EnableRemoteAccess'] = false;
                $policy['EnableRemoteControlOfOtherUsers'] = false;
                $policy['ForceRemoteSourceTranscoding'] = false;
                $policy['EnableLiveTvAccess'] = false;
                $policy['EnableLiveTvManagement'] = false;
                $policy['EnablePublicSharing'] = false;
                $policy['EnableSharedDeviceControl'] = false;

                // Keep local access enabled
                $policy['EnableLocalNetworkAccess'] = true;

                $updateResult = $this->updateUserPolicy($userId, $policy);

                // Verify the settings were applied
                $verifyResult = $this->getUserPolicy($userId);

                if($verifyResult['success']) {
                    $verifyPolicy = $verifyResult['data'];
                    $remoteDisabled = !isset($verifyPolicy['EnableRemoteAccess']) || !$verifyPolicy['EnableRemoteAccess'];

                    error_log('Emby: Force disable remote access for user ' . $userId .
                             ' - Update: ' . ($updateResult['success'] ? 'success' : 'failed') .
                             ', Verified disabled: ' . ($remoteDisabled ? 'yes' : 'no'));

                    return [
                        'success' => $updateResult['success'] && $remoteDisabled,
                        'message' => $remoteDisabled ? 'Remote access successfully disabled' : 'Failed to disable remote access',
                        'remote_access_disabled' => $remoteDisabled
                    ];
                }

                return $updateResult;
            }

            return $policyResult;

        } catch(Exception $e) {
            error_log('Emby: Failed to force disable remote access: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Get user by ID with full details
     */
    public function getUserById($userId) {
        return $this->makeRequest('/Users/' . $userId);
    }

    /**
     * Public method to make API requests (for testing purposes)
     */
    public function makeApiRequest($endpoint, $method = 'GET', $data = null) {
        return $this->makeRequest($endpoint, $method, $data);
    }

    /**
     * Get all users with multiple fallback methods
     */
    public function getAllUsers() {
        // Try multiple endpoints to get users
        $endpoints = [
            '/Users',
            '/Users/<USER>',
            '/Users?EnableImages=false',
            '/Users/<USER>'
        ];

        foreach($endpoints as $endpoint) {
            $result = $this->makeRequest($endpoint);

            if($result['success']) {
                // Handle different response formats
                if(isset($result['data']['Items']) && is_array($result['data']['Items'])) {
                    return [
                        'success' => true,
                        'data' => $result['data']['Items'],
                        'endpoint' => $endpoint,
                        'format' => 'Items'
                    ];
                } elseif(is_array($result['data'])) {
                    return [
                        'success' => true,
                        'data' => $result['data'],
                        'endpoint' => $endpoint,
                        'format' => 'Array'
                    ];
                }
            }
        }

        // If all endpoints fail, return the last error
        return [
            'success' => false,
            'message' => 'All user endpoints failed',
            'last_error' => isset($result) ? $result : ['message' => 'No endpoints tried']
        ];
    }

    /**
     * Alternative method to disable remote access using server configuration approach
     */
    public function disableRemoteAccessAlternative($userId) {
        try {
            // This method tries to disable remote access by checking if it's a server-level setting

            // First, check current server configuration
            $serverInfo = $this->getServerInfo();
            if($serverInfo['success']) {
                error_log('Emby: Server allows remote connections: ' .
                         (isset($serverInfo['data']['SupportsRemoteAccess']) ? 'true' : 'false'));
            }

            // Get user's current configuration
            $userResult = $this->makeRequest('/Users/' . $userId);
            if(!$userResult['success']) {
                return ['success' => false, 'message' => 'Cannot get user data'];
            }

            $userData = $userResult['data'];

            // Try different policy structures based on Emby documentation
            $policyVariations = [
                // Variation 1: Simple boolean
                ['EnableRemoteAccess' => false],

                // Variation 2: With related settings
                [
                    'EnableRemoteAccess' => false,
                    'EnableRemoteControlOfOtherUsers' => false,
                    'ForceRemoteSourceTranscoding' => false
                ],

                // Variation 3: Complete policy with defaults
                [
                    'EnableRemoteAccess' => false,
                    'EnableRemoteControlOfOtherUsers' => false,
                    'ForceRemoteSourceTranscoding' => false,
                    'EnableLocalNetworkAccess' => true,
                    'EnableLiveTvAccess' => true,
                    'EnableLiveTvManagement' => false,
                    'EnablePublicSharing' => true,
                    'EnableSharedDeviceControl' => true,
                    'IsAdministrator' => isset($userData['Policy']['IsAdministrator']) ? $userData['Policy']['IsAdministrator'] : false,
                    'IsHidden' => isset($userData['Policy']['IsHidden']) ? $userData['Policy']['IsHidden'] : false,
                    'IsDisabled' => isset($userData['Policy']['IsDisabled']) ? $userData['Policy']['IsDisabled'] : false
                ]
            ];

            foreach($policyVariations as $index => $policyData) {
                error_log('Emby: Trying policy variation ' . ($index + 1) . ' for user ' . $userId);

                // Try POST method
                $result = $this->makeRequest('/Users/' . $userId . '/Policy', 'POST', $policyData);

                if($result['success']) {
                    // Verify the change
                    $verifyResult = $this->makeRequest('/Users/' . $userId);
                    if($verifyResult['success'] && isset($verifyResult['data']['Policy'])) {
                        $newPolicy = $verifyResult['data']['Policy'];
                        $isDisabled = !isset($newPolicy['EnableRemoteAccess']) || !$newPolicy['EnableRemoteAccess'];

                        if($isDisabled) {
                            error_log('Emby: Successfully disabled remote access using variation ' . ($index + 1));
                            return [
                                'success' => true,
                                'message' => 'Remote access disabled using policy variation ' . ($index + 1),
                                'method' => 'policy_variation_' . ($index + 1)
                            ];
                        }
                    }
                }

                // Try PUT method as alternative
                $putResult = $this->makeRequest('/Users/' . $userId . '/Policy', 'PUT', $policyData);

                if($putResult['success']) {
                    // Verify the change
                    $verifyResult = $this->makeRequest('/Users/' . $userId);
                    if($verifyResult['success'] && isset($verifyResult['data']['Policy'])) {
                        $newPolicy = $verifyResult['data']['Policy'];
                        $isDisabled = !isset($newPolicy['EnableRemoteAccess']) || !$newPolicy['EnableRemoteAccess'];

                        if($isDisabled) {
                            error_log('Emby: Successfully disabled remote access using PUT variation ' . ($index + 1));
                            return [
                                'success' => true,
                                'message' => 'Remote access disabled using PUT policy variation ' . ($index + 1),
                                'method' => 'put_policy_variation_' . ($index + 1)
                            ];
                        }
                    }
                }
            }

            return [
                'success' => false,
                'message' => 'All policy variations failed to disable remote access',
                'note' => 'Remote access might be enforced at server level'
            ];

        } catch(Exception $e) {
            error_log('Emby: Exception in disableRemoteAccessAlternative: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Force hide user from login screens using direct API call
     * Based on Emby API documentation and community examples
     */
    public function forceHideUser($userId) {
        try {
            // Get current user data
            $userResult = $this->getUserById($userId);

            if($userResult['success']) {
                $userData = $userResult['data'];

                // Force hide in multiple ways
                if(!isset($userData['Configuration'])) {
                    $userData['Configuration'] = [];
                }

                // Set all possible hide flags based on Emby API
                $userData['Configuration']['IsHidden'] = true;
                $userData['Configuration']['IsHiddenRemotely'] = true;
                $userData['Configuration']['IsHiddenFromUnusedDevices'] = true;
                $userData['Configuration']['HidePlayedInLatest'] = true;
                $userData['Configuration']['DisplayMissingEpisodes'] = false;
                $userData['Configuration']['EnableLocalPassword'] = false;
                $userData['Configuration']['GroupedFolders'] = [];

                // Update user data
                $updateResult = $this->makeRequest('/Users/' . $userId, 'POST', $userData);

                if($updateResult['success']) {
                    // Also update policy with all hide flags
                    $policyResult = $this->getUserPolicy($userId);

                    if($policyResult['success']) {
                        $policy = $policyResult['data'];

                        // Set all hide-related policy flags
                        $policy['IsHidden'] = true;
                        $policy['IsHiddenRemotely'] = true;
                        $policy['IsHiddenFromUnusedDevices'] = true;
                        $policy['LoginAttemptsBeforeLockout'] = -1;
                        $policy['EnablePublicSharing'] = false;
                        $policy['EnableSharedDeviceControl'] = false;

                        // Disable remote access (Unchecked "Allow remote connections to this Emby Server")
                        $policy['EnableRemoteAccess'] = false;
                        $policy['EnableRemoteControlOfOtherUsers'] = false;
                        $policy['ForceRemoteSourceTranscoding'] = false;
                        $policy['EnableLiveTvAccess'] = false;
                        $policy['EnableLiveTvManagement'] = false;

                        $policyUpdateResult = $this->updateUserPolicy($userId, $policy);

                        error_log('Emby: Force hide user ' . $userId .
                                 ' - User update: ' . ($updateResult['success'] ? 'success' : 'failed') .
                                 ', Policy update: ' . ($policyUpdateResult['success'] ? 'success' : 'failed'));

                        return [
                            'success' => true,
                            'message' => 'User hidden successfully with all flags',
                            'user_update' => $updateResult['success'],
                            'policy_update' => $policyUpdateResult['success']
                        ];
                    }
                }

                return $updateResult;
            }

            return $userResult;

        } catch(Exception $e) {
            error_log('Emby: Failed to force hide user: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
?>
