@echo off
echo Testing PHP paths...

echo.
echo 1. Testing: php (system PATH)
php -v
echo Exit code: %ERRORLEVEL%

echo.
echo 2. Testing: C:\xampp\php\php.exe
if exist "C:\xampp\php\php.exe" (
    "C:\xampp\php\php.exe" -v
    echo Exit code: %ERRORLEVEL%
) else (
    echo File not found
)

echo.
echo 3. Testing: C:\wamp64\bin\php\php8.2.12\php.exe
if exist "C:\wamp64\bin\php\php8.2.12\php.exe" (
    "C:\wamp64\bin\php\php8.2.12\php.exe" -v
    echo Exit code: %ERRORLEVEL%
) else (
    echo File not found
)

echo.
echo 4. Current directory:
cd
echo.

echo 5. Directory contents:
dir

pause
