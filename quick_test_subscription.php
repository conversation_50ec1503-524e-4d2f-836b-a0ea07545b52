<?php
/**
 * ทดสอบการอัปเดตสถานะ subscription แบบง่าย
 */

session_start();

// ตรวจสอบสิทธิ์แอดมิน
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    die("❌ ไม่มีสิทธิ์เข้าถึง");
}

require_once 'config/db_config.php';

echo "<h1>🧪 ทดสอบการอัปเดตสถานะ Subscription</h1>";

$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
}

// ทดสอบการอัปเดต
if (isset($_POST['test_update'])) {
    $test_id = $_POST['test_id'];
    $test_status = $_POST['test_status'];
    
    echo "<h2>🔄 กำลังทดสอบการอัปเดต...</h2>";
    
    try {
        $conn->beginTransaction();
        
        $update_query = "UPDATE user_subscriptions SET status = :status, updated_at = NOW() WHERE id = :id";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindParam(':status', $test_status);
        $update_stmt->bindParam(':id', $test_id);
        $result = $update_stmt->execute();
        
        if ($result) {
            $affected = $update_stmt->rowCount();
            $conn->commit();
            echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ อัปเดตสำเร็จ: {$affected} รายการ";
            echo "</div>";
        } else {
            $conn->rollback();
            echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>";
            echo "❌ อัปเดตล้มเหลว";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        $conn->rollback();
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>";
        echo "❌ ข้อผิดพลาด: " . $e->getMessage();
        echo "</div>";
    }
}

// ดึงข้อมูล subscriptions
try {
    $query = "SELECT us.*, u.username 
              FROM user_subscriptions us 
              LEFT JOIN users u ON us.user_id = u.id 
              ORDER BY us.id DESC 
              LIMIT 10";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($subscriptions)) {
        echo "<p>❌ ไม่มีข้อมูล subscriptions</p>";
        echo "<p>กรุณาสร้างข้อมูลทดสอบก่อน</p>";
    } else {
        echo "<h2>📋 ข้อมูล Subscriptions</h2>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Package ID</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Start Date</th>";
        echo "<th style='padding: 8px;'>End Date</th>";
        echo "</tr>";
        
        foreach ($subscriptions as $sub) {
            $status_color = '';
            switch($sub['status']) {
                case 'active': $status_color = 'background: #d4edda; color: #155724;'; break;
                case 'inactive': $status_color = 'background: #fff3cd; color: #856404;'; break;
                case 'expired': $status_color = 'background: #f8d7da; color: #721c24;'; break;
                case 'cancelled': $status_color = 'background: #e2e3e5; color: #383d41;'; break;
            }
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$sub['id']}</td>";
            echo "<td style='padding: 8px;'>" . ($sub['username'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>{$sub['package_id']}</td>";
            echo "<td style='padding: 8px; {$status_color}'>{$sub['status']}</td>";
            echo "<td style='padding: 8px;'>{$sub['start_date']}</td>";
            echo "<td style='padding: 8px;'>{$sub['end_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // ฟอร์มทดสอบ
        echo "<h2>🧪 ทดสอบการอัปเดต</h2>";
        echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>เลือก Subscription:</label>";
        echo "<select name='test_id' style='width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;'>";
        foreach ($subscriptions as $sub) {
            echo "<option value='{$sub['id']}'>";
            echo "ID: {$sub['id']} - {$sub['username']} - {$sub['status']}";
            echo "</option>";
        }
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 15px;'>";
        echo "<label style='display: block; margin-bottom: 5px; font-weight: bold;'>สถานะใหม่:</label>";
        echo "<select name='test_status' style='width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;'>";
        echo "<option value='active'>Active (ใช้งานอยู่)</option>";
        echo "<option value='inactive'>Inactive (ไม่ใช้งาน)</option>";
        echo "<option value='expired'>Expired (หมดอายุ)</option>";
        echo "<option value='cancelled'>Cancelled (ยกเลิก)</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<button type='submit' name='test_update' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>";
        echo "ทดสอบอัปเดต";
        echo "</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาดในการดึงข้อมูล: " . $e->getMessage() . "</p>";
}

echo "<h2>🔗 ลิงก์</h2>";
echo "<ul>";
echo "<li><a href='?page=admin_subscriptions'>หน้า Admin Subscriptions</a></li>";
echo "<li><a href='?page=admin_users'>หน้า Admin Users</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>⚠️ ไฟล์นี้เป็นไฟล์ทดสอบ ให้ลบออกหลังใช้เสร็จ</small></p>";
?>
