/* Font Override for Different Pages */

/* TH Sarabun Font Face Declarations */
@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

/* Default font for all pages (TH Sarabun) */
body:not(.homepage) {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) h1,
body:not(.homepage) h2,
body:not(.homepage) h3,
body:not(.homepage) h4,
body:not(.homepage) h5,
body:not(.homepage) h6 {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .navbar-brand {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .nav-link {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .btn {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .card-title {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .card-text {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .form-label {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .form-control {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .table th,
body:not(.homepage) .table td {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .badge {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .alert {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .dropdown-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .modal-title,
body:not(.homepage) .modal-body {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .breadcrumb-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .page-link {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .list-group-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .progress-bar {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .tooltip-inner,
body:not(.homepage) .popover-body {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .lead {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) small,
body:not(.homepage) .small {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Homepage keeps MN Bak kut teh font (already defined in fonts.css) */
body.homepage {
    font-family: 'MN Bak kut teh', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Ensure all text elements on homepage use MN Bak kut teh */
body.homepage * {
    font-family: inherit !important;
}

/* Special adjustments for TH Sarabun to match MN Bak kut teh size */
body:not(.homepage) {
    font-weight: 400;
    line-height: 1.6;
    font-size: 1.1em; /* เพิ่มขนาดตัวอักษรพื้นฐาน */
}

body:not(.homepage) h1 {
    font-weight: 600;
    font-size: 2.8rem; /* เพิ่มขนาดให้เท่า MN Bak kut teh */
}

body:not(.homepage) h2 {
    font-weight: 600;
    font-size: 2.3rem;
}

body:not(.homepage) h3 {
    font-weight: 600;
    font-size: 1.9rem;
}

body:not(.homepage) h4 {
    font-weight: 600;
    font-size: 1.6rem;
}

body:not(.homepage) h5 {
    font-weight: 600;
    font-size: 1.35rem;
}

body:not(.homepage) h6 {
    font-weight: 600;
    font-size: 1.15rem;
}

body:not(.homepage) .navbar-brand {
    font-weight: 700;
    font-size: 2rem; /* เพิ่มขนาดให้เท่า MN Bak kut teh */
}

body:not(.homepage) .btn {
    font-weight: 500;
    font-size: 1.05em; /* เพิ่มขนาดปุ่ม */
}

body:not(.homepage) .nav-link {
    font-size: 1.05em; /* เพิ่มขนาด navigation */
}

body:not(.homepage) .card-title {
    font-size: 1.3rem; /* เพิ่มขนาด card title */
}

body:not(.homepage) .form-label {
    font-size: 1.05em; /* เพิ่มขนาด form label */
}

body:not(.homepage) .form-control {
    font-size: 1.05em; /* เพิ่มขนาด form input */
}

body:not(.homepage) .table th,
body:not(.homepage) .table td {
    font-size: 1.05em; /* เพิ่มขนาดตาราง */
}

body:not(.homepage) .badge {
    font-size: 0.9em; /* ปรับขนาด badge */
}

body:not(.homepage) .alert {
    font-size: 1.05em; /* เพิ่มขนาด alert */
}

body:not(.homepage) .dropdown-item {
    font-size: 1.02em; /* เพิ่มขนาด dropdown */
}

body:not(.homepage) .modal-title {
    font-size: 1.4rem; /* เพิ่มขนาด modal title */
}

body:not(.homepage) .modal-body {
    font-size: 1.05em; /* เพิ่มขนาด modal body */
}

body:not(.homepage) .breadcrumb-item {
    font-size: 1.02em; /* เพิ่มขนาด breadcrumb */
}

body:not(.homepage) .page-link {
    font-size: 1.02em; /* เพิ่มขนาด pagination */
}

body:not(.homepage) .list-group-item {
    font-size: 1.05em; /* เพิ่มขนาด list group */
}

body:not(.homepage) .lead {
    font-size: 1.35rem; /* เพิ่มขนาด lead text */
}

body:not(.homepage) small,
body:not(.homepage) .small {
    font-size: 0.9em; /* ปรับขนาด small text */
}

/* ปรับขนาด display text */
body:not(.homepage) .display-1 {
    font-size: 5.5rem;
}

body:not(.homepage) .display-2 {
    font-size: 4.5rem;
}

body:not(.homepage) .display-3 {
    font-size: 3.5rem;
}

body:not(.homepage) .display-4 {
    font-size: 2.8rem;
}

body:not(.homepage) .display-5 {
    font-size: 2.3rem;
}

body:not(.homepage) .display-6 {
    font-size: 1.9rem;
}

/* Responsive adjustments for TH Sarabun to match MN Bak kut teh */
@media (max-width: 768px) {
    body:not(.homepage) {
        font-size: 1.05em; /* รักษาขนาดพื้นฐาน */
    }

    body:not(.homepage) h1 {
        font-size: 2.2rem; /* เท่ากับ MN Bak kut teh responsive */
    }

    body:not(.homepage) h2 {
        font-size: 1.9rem;
    }

    body:not(.homepage) .navbar-brand {
        font-size: 1.7rem; /* เพิ่มขนาดให้เท่า MN Bak kut teh */
    }

    body:not(.homepage) .btn {
        font-size: 1.02em;
    }
}

@media (max-width: 576px) {
    body:not(.homepage) {
        font-size: 1.02em;
    }

    body:not(.homepage) h1 {
        font-size: 2rem; /* เท่ากับ MN Bak kut teh responsive */
    }

    body:not(.homepage) h2 {
        font-size: 1.7rem;
    }

    body:not(.homepage) .navbar-brand {
        font-size: 1.5rem; /* เพิ่มขนาดให้เท่า MN Bak kut teh */
    }

    body:not(.homepage) .btn {
        font-size: 1em;
    }
}
