<?php
/**
 * Helper functions for the application
 */

/**
 * Format currency amount for display
 * @param float|string $amount The amount to format
 * @param int $decimals Number of decimal places (default: 2)
 * @return string Formatted currency string
 */
function formatCurrency($amount, $decimals = 2) {
    // Convert to float to ensure proper formatting
    $amount = floatval($amount);
    
    // Format with Thai number format (comma as thousands separator, dot as decimal)
    return number_format($amount, $decimals, '.', ',');
}

/**
 * Format currency with Thai Baht symbol
 * @param float|string $amount The amount to format
 * @param int $decimals Number of decimal places (default: 2)
 * @return string Formatted currency string with ฿ symbol
 */
function formatCurrencyTHB($amount, $decimals = 2) {
    return formatCurrency($amount, $decimals) . ' ฿';
}

/**
 * Sanitize and validate amount input
 * @param mixed $amount Input amount
 * @return float Sanitized amount
 */
function sanitizeAmount($amount) {
    // Remove any non-numeric characters except decimal point
    $amount = preg_replace('/[^0-9.]/', '', $amount);
    
    // Convert to float
    $amount = floatval($amount);
    
    // Ensure minimum value
    return max(0, $amount);
}

/**
 * Check if amount is valid for payment
 * @param float $amount Amount to check
 * @param float $min_amount Minimum allowed amount (default: 1)
 * @param float $max_amount Maximum allowed amount (default: 100000)
 * @return array Result with success status and message
 */
function validateAmount($amount, $min_amount = 1, $max_amount = 100000) {
    $amount = sanitizeAmount($amount);
    
    if ($amount < $min_amount) {
        return [
            'success' => false,
            'message' => "จำนวนเงินต้องไม่น้อยกว่า " . formatCurrencyTHB($min_amount)
        ];
    }
    
    if ($amount > $max_amount) {
        return [
            'success' => false,
            'message' => "จำนวนเงินต้องไม่เกิน " . formatCurrencyTHB($max_amount)
        ];
    }
    
    return [
        'success' => true,
        'amount' => $amount
    ];
}

/**
 * Format date for Thai display
 * @param string $date Date string
 * @param string $format Output format (default: 'd/m/Y H:i')
 * @return string Formatted date
 */
function formatDateThai($date, $format = 'd/m/Y H:i') {
    if (empty($date) || $date === '0000-00-00 00:00:00') {
        return '-';
    }
    
    return date($format, strtotime($date));
}

/**
 * Generate secure random string
 * @param int $length Length of string (default: 32)
 * @return string Random string
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Sanitize HTML output
 * @param string $string Input string
 * @return string Sanitized string
 */
function sanitizeOutput($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Check if user has sufficient balance
 * @param float $user_balance User's current balance
 * @param float $required_amount Required amount
 * @return array Result with success status and message
 */
function checkSufficientBalance($user_balance, $required_amount) {
    $user_balance = floatval($user_balance);
    $required_amount = floatval($required_amount);
    
    if ($user_balance >= $required_amount) {
        return [
            'success' => true,
            'message' => 'ยอดเงินเพียงพอ'
        ];
    }
    
    $shortage = $required_amount - $user_balance;
    
    return [
        'success' => false,
        'message' => 'ยอดเงินไม่เพียงพอ ต้องการเพิ่มอีก ' . formatCurrencyTHB($shortage),
        'shortage' => $shortage
    ];
}
?>
