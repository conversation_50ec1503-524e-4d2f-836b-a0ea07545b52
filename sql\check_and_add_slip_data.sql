-- Check if slip_data column exists and add if not
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'top_ups' 
AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;

-- Add slip_data column only (approved_at already exists)
ALTER TABLE top_ups 
ADD COLUMN slip_data TEXT COMMENT 'JSON data from slip verification API';

-- Create processed_transactions table
CREATE TABLE IF NOT EXISTS processed_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trans_id VARCHAR(255) NOT NULL UNIQUE,
    topup_id INT,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_trans_id (trans_id),
    INDEX idx_topup_id (topup_id),
    INDEX idx_processed_at (processed_at),
    
    FOREIGN KEY (topup_id) REFERENCES top_ups(id) ON DELETE SET NULL
);
