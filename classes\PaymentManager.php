<?php
require_once 'config/db_config.php';

class PaymentManager {
    private $conn;
    private $settings;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->settings = new SystemSettings($this->conn);
    }
    
    public function generatePromptPayQR($amount, $user_id) {
        try {
            $phone = $this->settings->get('promptpay_phone');

            if(!$phone) {
                throw new Exception('กรุณาตั้งค่าเบอร์โทรศัพท์ PromptPay ในระบบก่อน');
            }

            // Add random decimal (0.01-0.99) for unique matching
            $random_decimal = mt_rand(1, 99) / 100;
            $unique_amount = $amount + $random_decimal;

            // Format amount to 2 decimal places (for QR URL - no thousands separator)
            $qr_amount = number_format($unique_amount, 2, '.', '');

            // Format amount for display (with thousands separator)
            $formatted_amount = number_format($unique_amount, 2, '.', ',');

            // Generate QR Code URL (use promptpay.io as primary)
            $qr_url = "https://promptpay.io/{$phone}/{$qr_amount}.png";
            $service_used = 'promptpay.io';

            // Return QR data without saving to database yet
            // Will save when slip is uploaded
            return [
                'success' => true,
                'qr_url' => $qr_url,
                'amount' => $formatted_amount,
                'qr_amount' => $qr_amount,
                'unique_amount' => $unique_amount,
                'original_amount' => $amount,
                'phone' => $phone,
                'service' => $service_used,
                'user_id' => $user_id
            ];

        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * บันทึกข้อมูลการเติมเงินเมื่ออัปโหลดสลิป
     */
    public function createTopUpWithSlip($user_id, $amount, $qr_url, $slip_image_path) {
        try {
            $query = "INSERT INTO top_ups (user_id, amount, payment_method, qr_code_url, slip_image, status, notes, created_at)
                      VALUES (:user_id, :amount, 'promptpay', :qr_url, :slip_image, 'pending', 'promptpay.io', NOW())";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':qr_url', $qr_url);
            $stmt->bindParam(':slip_image', $slip_image_path);

            if($stmt->execute()) {
                $topup_id = $this->conn->lastInsertId();

                // Log the action
                $this->logPaymentAction($user_id, 'topup_created',
                    "Top-up request created with amount {$amount} THB and slip uploaded");

                return [
                    'success' => true,
                    'topup_id' => $topup_id,
                    'message' => 'บันทึกข้อมูลการเติมเงินสำเร็จ'
                ];
            }

            throw new Exception('ไม่สามารถบันทึกข้อมูลการเติมเงินได้');

        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private function testQRService($url) {
        // Skip testing for now - just return true to use the first service
        return true;

        // Alternative: Simple test - check if URL is accessible
        // $headers = @get_headers($url, 1);
        // return $headers && strpos($headers[0], '200') !== false;
    }

    private function generateFallbackQR($phone, $amount) {
        // Generate a simple QR code with basic PromptPay data
        $promptpay_data = "00020101021129370016A000000677010111011300{$phone}5204000053037645802TH630{$amount}6304";
        return "https://api.qrserver.com/v1/create-qr-code/?size=300x300&format=png&data=" . urlencode($promptpay_data);
    }
    
    public function approveTopUp($topup_id, $admin_id) {
        try {
            $this->conn->beginTransaction();

            // Get top-up details
            $query = "SELECT * FROM top_ups WHERE id = :topup_id AND status = 'pending'";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':topup_id', $topup_id);
            $stmt->execute();

            if($stmt->rowCount() == 0) {
                throw new Exception('Top-up not found or already processed');
            }

            $topup = $stmt->fetch(PDO::FETCH_ASSOC);

            // Update top-up status
            $query = "UPDATE top_ups SET status = 'approved', approved_by = :admin_id, approved_at = NOW()
                      WHERE id = :topup_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':admin_id', $admin_id);
            $stmt->bindParam(':topup_id', $topup_id);
            $stmt->execute();

            // Update user balance
            $query = "UPDATE users SET balance = balance + :amount WHERE id = :user_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':amount', $topup['amount']);
            $stmt->bindParam(':user_id', $topup['user_id']);
            $stmt->execute();

            $this->conn->commit();

            // Log the action
            $this->logPaymentAction($topup['user_id'], 'topup_approved',
                "Top-up of {$topup['amount']} approved by admin {$admin_id}");

            return [
                'success' => true,
                'message' => 'อนุมัติการเติมเงินสำเร็จ'
            ];

        } catch(Exception $e) {
            $this->conn->rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    public function rejectTopUp($topup_id, $admin_id, $reason = '') {
        try {
            $query = "UPDATE top_ups SET status = 'rejected', approved_by = :admin_id, approved_at = NOW() 
                      WHERE id = :topup_id AND status = 'pending'";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':admin_id', $admin_id);
            $stmt->bindParam(':topup_id', $topup_id);
            
            if($stmt->execute() && $stmt->rowCount() > 0) {
                // Get user_id for logging
                $query = "SELECT user_id FROM top_ups WHERE id = :topup_id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':topup_id', $topup_id);
                $stmt->execute();
                $topup = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Log the action
                $this->logPaymentAction($topup['user_id'], 'topup_rejected', 
                    "Top-up rejected by admin {$admin_id}. Reason: {$reason}");
                
                return [
                    'success' => true,
                    'message' => 'ปฏิเสธการเติมเงินสำเร็จ'
                ];
            }
            
            return [
                'success' => false,
                'message' => 'ไม่พบรายการเติมเงินหรือได้ดำเนินการแล้ว'
            ];
            
        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    public function purchasePackage($user_id, $package_id) {
        try {
            $this->conn->beginTransaction();
            
            // Get user balance
            $query = "SELECT balance FROM users WHERE id = :user_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if(!$user) {
                throw new Exception('User not found');
            }
            
            // Get package details
            $query = "SELECT * FROM packages WHERE id = :package_id AND status = 'active'";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':package_id', $package_id);
            $stmt->execute();
            $package = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if(!$package) {
                throw new Exception('Package not found or inactive');
            }
            
            // Check if user has enough balance
            if($user['balance'] < $package['price']) {
                throw new Exception('ยอดเงินไม่เพียงพอ');
            }
            
            // Calculate subscription dates (Thailand timezone)
            date_default_timezone_set('Asia/Bangkok');
            $start_date = date('Y-m-d');
            $end_date = date('Y-m-d', strtotime("+{$package['duration_days']} days"));
            
            // Check for existing active subscription
            $query = "SELECT id FROM user_subscriptions 
                      WHERE user_id = :user_id AND status = 'active' AND end_date >= CURDATE()";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
            if($stmt->rowCount() > 0) {
                // Extend existing subscription
                $query = "UPDATE user_subscriptions 
                          SET end_date = DATE_ADD(end_date, INTERVAL :duration_days DAY),
                              updated_at = NOW()
                          WHERE user_id = :user_id AND status = 'active'";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':duration_days', $package['duration_days']);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
            } else {
                // Create new subscription
                $query = "INSERT INTO user_subscriptions (user_id, package_id, start_date, end_date, status) 
                          VALUES (:user_id, :package_id, :start_date, :end_date, 'active')";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':package_id', $package_id);
                $stmt->bindParam(':start_date', $start_date);
                $stmt->bindParam(':end_date', $end_date);
                $stmt->execute();
            }
            
            // Deduct balance
            $query = "UPDATE users SET balance = balance - :price WHERE id = :user_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':price', $package['price']);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
            $this->conn->commit();

            // Enable users for subscription after successful purchase
            $this->enableUsersForSubscription($user_id);

            // Log the purchase
            $this->logPaymentAction($user_id, 'package_purchase',
                "Purchased package: {$package['name']} for {$package['price']} THB");

            return [
                'success' => true,
                'message' => 'ซื้อแพ็คเกจสำเร็จ',
                'end_date' => $end_date
            ];
            
        } catch(Exception $e) {
            $this->conn->rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    public function getPendingTopUps() {
        $query = "SELECT t.*, u.username, u.full_name 
                  FROM top_ups t 
                  JOIN users u ON t.user_id = u.id 
                  WHERE t.status = 'pending' 
                  ORDER BY t.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getUserTopUps($user_id) {
        $query = "SELECT * FROM top_ups WHERE user_id = :user_id ORDER BY created_at DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * เปิดใช้งาน users สำหรับ subscription หลังจากซื้อ package สำเร็จ
     */
    private function enableUsersForSubscription($user_id) {
        try {
            error_log('PaymentManager: Enabling users for subscription - User ID: ' . $user_id);

            $results = [];

            // Enable Emby user
            $emby_query = "SELECT emby_user_id FROM emby_accounts WHERE user_id = :user_id AND status = 'active'";
            $emby_stmt = $this->conn->prepare($emby_query);
            $emby_stmt->bindParam(':user_id', $user_id);
            $emby_stmt->execute();

            if($emby_stmt->rowCount() > 0) {
                $emby_account = $emby_stmt->fetch(PDO::FETCH_ASSOC);
                error_log('PaymentManager: Found Emby account - User ID: ' . $emby_account['emby_user_id']);

                require_once 'classes/EmbyAPI.php';
                $database = new Database();
                $conn = $database->getConnection();
                $settings = new SystemSettings($conn);

                $emby_api = new EmbyAPI(
                    $settings->get('emby_server_url'),
                    $settings->get('emby_api_key')
                );

                $emby_result = $emby_api->enableUserForSubscription($emby_account['emby_user_id']);
                $results['emby'] = $emby_result['success'];
                error_log('PaymentManager: Emby enable result: ' . ($emby_result['success'] ? 'success' : 'failed - ' . ($emby_result['message'] ?? 'unknown')));
            } else {
                error_log('PaymentManager: No active Emby account found for user ' . $user_id);
                $results['emby'] = false;
            }

            // Enable Jellyfin user
            $jellyfin_query = "SELECT jellyfin_user_id FROM jellyfin_accounts WHERE user_id = :user_id AND status = 'active'";
            $jellyfin_stmt = $this->conn->prepare($jellyfin_query);
            $jellyfin_stmt->bindParam(':user_id', $user_id);
            $jellyfin_stmt->execute();

            if($jellyfin_stmt->rowCount() > 0) {
                $jellyfin_account = $jellyfin_stmt->fetch(PDO::FETCH_ASSOC);
                error_log('PaymentManager: Found Jellyfin account - User ID: ' . $jellyfin_account['jellyfin_user_id']);

                require_once 'classes/JellyfinAPI.php';
                $database = new Database();
                $conn = $database->getConnection();
                $settings = new SystemSettings($conn);

                $jellyfin_api = new JellyfinAPI(
                    $settings->get('jellyfin_server_url'),
                    $settings->get('jellyfin_api_key')
                );

                $jellyfin_result = $jellyfin_api->enableUserForSubscription($jellyfin_account['jellyfin_user_id']);
                $results['jellyfin'] = $jellyfin_result['success'];
                error_log('PaymentManager: Jellyfin enable result: ' . ($jellyfin_result['success'] ? 'success' : 'failed - ' . ($jellyfin_result['message'] ?? 'unknown')));
            } else {
                error_log('PaymentManager: No active Jellyfin account found for user ' . $user_id);
                $results['jellyfin'] = false;
            }

            // Log the action with detailed results
            $log_message = 'Users enabled for subscription - ';
            $log_message .= 'Emby: ' . ($results['emby'] ? 'success' : 'failed') . ', ';
            $log_message .= 'Jellyfin: ' . ($results['jellyfin'] ? 'success' : 'failed');

            $this->logPaymentAction($user_id, 'users_enabled_for_subscription', $log_message);

            error_log('PaymentManager: Enable users completed - ' . $log_message);

            return $results;

        } catch(Exception $e) {
            error_log('PaymentManager: Failed to enable users for subscription: ' . $e->getMessage());
            $this->logPaymentAction($user_id, 'enable_users_failed',
                'Failed to enable users for subscription: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    private function logPaymentAction($user_id, $action, $description) {
        $query = "INSERT INTO usage_logs (user_id, action, description)
                  VALUES (:user_id, :action, :description)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':action', $action);
        $stmt->bindParam(':description', $description);

        return $stmt->execute();
    }
}
?>
