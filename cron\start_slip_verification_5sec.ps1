# PowerShell Script สำหรับรันการตรวจสอบสลิปอัตโนมัติทุก 5 วินาที

# ตั้งค่าหน้าต่าง
$Host.UI.RawUI.WindowTitle = "Slip Verification Auto Service - Every 5 Seconds"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Slip Verification Auto Service" -ForegroundColor Cyan
Write-Host "  ตรวจสอบสลิปอัตโนมัติทุก 5 วินาที" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host

# เปลี่ยนไปยัง directory ของโปรเจค
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
Set-Location $ProjectRoot

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host

# ค้นหา PHP
$PhpPath = $null

# ลองหาใน PATH ก่อน
try {
    $null = php --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        $PhpPath = "php"
    }
} catch {}

# ลองหาใน Laragon
if (-not $PhpPath) {
    $LaraganPaths = Get-ChildItem "C:\laragon\bin\php\php-*\php.exe" -ErrorAction SilentlyContinue
    if ($LaraganPaths) {
        $PhpPath = $LaraganPaths[0].FullName
    }
}

# ลองหาใน XAMPP
if (-not $PhpPath -and (Test-Path "C:\xampp\php\php.exe")) {
    $PhpPath = "C:\xampp\php\php.exe"
}

# ลองหาใน WAMP
if (-not $PhpPath) {
    $WampPaths = Get-ChildItem "C:\wamp64\bin\php\php*\php.exe" -ErrorAction SilentlyContinue
    if ($WampPaths) {
        $PhpPath = $WampPaths[0].FullName
    }
}

if (-not $PhpPath) {
    Write-Host "❌ ไม่พบ PHP ในระบบ" -ForegroundColor Red
    Write-Host "กรุณาติดตั้ง PHP หรือเพิ่ม PHP ใน PATH" -ForegroundColor Yellow
    Write-Host "ตำแหน่งที่ค้นหา:" -ForegroundColor Yellow
    Write-Host "- C:\laragon\bin\php\" -ForegroundColor Yellow
    Write-Host "- C:\xampp\php\" -ForegroundColor Yellow
    Write-Host "- C:\wamp64\bin\php\" -ForegroundColor Yellow
    Write-Host "- PATH environment" -ForegroundColor Yellow
    Read-Host "กด Enter เพื่อออก"
    exit 1
}

Write-Host "✅ พบ PHP แล้ว: $PhpPath" -ForegroundColor Green

# แสดงเวอร์ชัน PHP
try {
    & $PhpPath --version | Select-Object -First 1 | Write-Host -ForegroundColor Green
} catch {
    Write-Host "⚠️ ไม่สามารถตรวจสอบเวอร์ชัน PHP ได้" -ForegroundColor Yellow
}

Write-Host
Write-Host "🚀 เริ่มการตรวจสอบสลิปอัตโนมัติ..." -ForegroundColor Green
Write-Host "📝 Log จะถูกบันทึกใน cron/logs/slip_verification_5sec.log" -ForegroundColor Yellow
Write-Host "🔄 ระบบจะตรวจสอบทุก 5 วินาที" -ForegroundColor Yellow
Write-Host
Write-Host "กด Ctrl+C เพื่อหยุดการทำงาน" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Cyan
Write-Host

# ตัวแปรสำหรับสถิติ
$Counter = 0
$TotalApproved = 0
$StartTime = Get-Date

# จัดการ Ctrl+C
[Console]::TreatControlCAsInput = $true

try {
    while ($true) {
        $Counter++
        $CurrentTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        
        Write-Host "[$CurrentTime] รอบที่ $Counter - กำลังตรวจสอบสลิป..." -ForegroundColor White
        
        try {
            # รัน PHP script
            $Output = & $PhpPath "cron/slip_verification_cron_5sec.php" 2>&1
            
            # กรองและแสดงข้อมูลสำคัญ
            $ImportantLines = $Output | Where-Object { 
                $_ -match "อนุมัติ|ERROR|WARNING|สถิติ|เริ่มการตรวจสอบ|สิ้นสุดการตรวจสอบ" 
            }
            
            foreach ($Line in $ImportantLines) {
                if ($Line -match "ERROR") {
                    Write-Host "  $Line" -ForegroundColor Red
                } elseif ($Line -match "WARNING") {
                    Write-Host "  $Line" -ForegroundColor Yellow
                } elseif ($Line -match "อนุมัติ.*สำเร็จ") {
                    Write-Host "  $Line" -ForegroundColor Green
                    # นับจำนวนการอนุมัติ
                    if ($Line -match "อนุมัติ (\d+) รายการ") {
                        $TotalApproved += [int]$Matches[1]
                    }
                } elseif ($Line -match "สถิติ") {
                    Write-Host "  $Line" -ForegroundColor Cyan
                } else {
                    Write-Host "  $Line" -ForegroundColor Gray
                }
            }
            
        } catch {
            Write-Host "  ⚠️ เกิดข้อผิดพลาดในการรัน PHP script: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # แสดงสถิติทุก 12 รอบ (1 นาที)
        if ($Counter % 12 -eq 0) {
            $ElapsedTime = (Get-Date) - $StartTime
            $Minutes = [math]::Round($ElapsedTime.TotalMinutes, 1)
            
            Write-Host
            Write-Host "📊 สถิติ: ตรวจสอบแล้ว $Counter รอบ ($Minutes นาที) | อนุมัติรวม: $TotalApproved รายการ" -ForegroundColor Cyan
            Write-Host "========================================" -ForegroundColor Cyan
        }
        
        # ตรวจสอบว่าผู้ใช้กด Ctrl+C หรือไม่
        if ([Console]::KeyAvailable) {
            $Key = [Console]::ReadKey($true)
            if ($Key.Key -eq "C" -and $Key.Modifiers -eq "Control") {
                break
            }
        }
        
        # รอ 5 วินาที
        Start-Sleep -Seconds 5
    }
} catch {
    Write-Host "เกิดข้อผิดพลาด: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    $ElapsedTime = (Get-Date) - $StartTime
    $Minutes = [math]::Round($ElapsedTime.TotalMinutes, 1)
    
    Write-Host
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "🛑 หยุดการทำงานแล้ว" -ForegroundColor Yellow
    Write-Host "📊 รวมตรวจสอบ: $Counter รอบ ($Minutes นาที)" -ForegroundColor Cyan
    Write-Host "💰 อนุมัติรวม: $TotalApproved รายการ" -ForegroundColor Green
    Write-Host "📝 Log file: cron\logs\slip_verification_5sec.log" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Read-Host "กด Enter เพื่อออก"
}
