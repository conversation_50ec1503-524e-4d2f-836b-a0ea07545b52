@echo off
REM Setup Windows Task Scheduler for PayNoi API Cron Job

echo ========================================
echo PayNoi API Windows Task Scheduler Setup
echo ========================================
echo.

REM Get current directory
set CURRENT_DIR=%~dp0
set SCRIPT_PATH=%CURRENT_DIR%run_cron.bat

echo Current directory: %CURRENT_DIR%
echo Script path: %SCRIPT_PATH%
echo.

REM Check if script exists
if not exist "%SCRIPT_PATH%" (
    echo Error: run_cron.bat not found in current directory
    pause
    exit /b 1
)

echo Available scheduling options:
echo.
echo 1. Every 5 minutes
echo 2. Every 15 minutes  
echo 3. Every 30 minutes
echo 4. Every hour
echo 5. Every 6 hours
echo 6. Daily at 2:00 AM
echo 7. Custom schedule
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" (
    set SCHEDULE=/sc minute /mo 5
    set TASK_NAME=PayNoi_API_Every5Min
) else if "%choice%"=="2" (
    set SCHEDULE=/sc minute /mo 15
    set TASK_NAME=PayNoi_API_Every15Min
) else if "%choice%"=="3" (
    set SCHEDULE=/sc minute /mo 30
    set TASK_NAME=PayNoi_API_Every30Min
) else if "%choice%"=="4" (
    set SCHEDULE=/sc hourly
    set TASK_NAME=PayNoi_API_Hourly
) else if "%choice%"=="5" (
    set SCHEDULE=/sc hourly /mo 6
    set TASK_NAME=PayNoi_API_Every6Hours
) else if "%choice%"=="6" (
    set SCHEDULE=/sc daily /st 02:00
    set TASK_NAME=PayNoi_API_Daily
) else if "%choice%"=="7" (
    echo.
    echo For custom schedule, please use Task Scheduler GUI:
    echo 1. Open Task Scheduler (taskschd.msc)
    echo 2. Create Basic Task
    echo 3. Set trigger as needed
    echo 4. Set action to start program: %SCRIPT_PATH%
    echo.
    pause
    exit /b 0
) else (
    echo Invalid choice. Exiting.
    pause
    exit /b 1
)

echo.
echo Creating scheduled task: %TASK_NAME%
echo Schedule: %SCHEDULE%
echo.

REM Create the scheduled task
schtasks /create /tn "%TASK_NAME%" /tr "%SCRIPT_PATH%" %SCHEDULE% /f

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo SUCCESS: Scheduled task created!
    echo ========================================
    echo.
    echo Task Name: %TASK_NAME%
    echo Script: %SCRIPT_PATH%
    echo.
    echo To manage the task:
    echo - View: schtasks /query /tn "%TASK_NAME%"
    echo - Run now: schtasks /run /tn "%TASK_NAME%"
    echo - Delete: schtasks /delete /tn "%TASK_NAME%" /f
    echo.
    echo Or use Task Scheduler GUI (taskschd.msc)
) else (
    echo.
    echo ========================================
    echo ERROR: Failed to create scheduled task
    echo ========================================
    echo You may need to run this as Administrator
)

echo.
echo Press any key to exit...
pause >nul
