# ระบบตรวจสอบสลิปอัตโนมัติ (Automatic Slip Verification System)

## ภาพรวมระบบ

ระบบตรวจสอบสลิปอัตโนมัติจะทำการเชื่อมต่อกับ API ของธนาคารเพื่อดึงข้อมูลธุรกรรมและจับคู่กับการเติมเงินที่รอการอนุมัติ เมื่อพบการจับคู่ที่ถูกต้อง ระบบจะอนุมัติการเติมเงินอัตโนมัติ

## ไฟล์ที่เกี่ยวข้อง

### Core Classes
- `classes/SlipVerificationManager.php` - คลาสหลักสำหรับจัดการการตรวจสอบสลิป
- `api/slip_verification.php` - API endpoint สำหรับการตรวจสอบ
- `pages/slip_verification.php` - หน้าสำหรับ admin ดูสถานะระบบ

### Cron Jobs
- `cron/slip_verification_cron.php` - Cron job สำหรับตรวจสอบอัตโนมัติ
- `api/cron_api_fetch.php` - ดึงข้อมูลจาก API (ใช้ร่วมกับระบบเดิม)

### Database
- `sql/create_processed_transactions_table.sql` - สคริปต์สร้างตาราง

### Data Storage
- `api/data/transactions.json` - ไฟล์เก็บข้อมูลธุรกรรมล่าสุด

## การติดตั้งและตั้งค่า

### 1. สร้างตารางฐานข้อมูล
```sql
-- รันสคริปต์ SQL
source sql/create_processed_transactions_table.sql;
```

### 2. ตั้งค่า Cron Job
```bash
# เพิ่มใน crontab เพื่อรันทุก 5 นาที
*/5 * * * * /usr/bin/php /path/to/your/project/cron/slip_verification_cron.php

# หรือรันทุก 10 นาที
*/10 * * * * /usr/bin/php /path/to/your/project/cron/slip_verification_cron.php
```

### 3. ตั้งค่า API Configuration
แก้ไขใน `classes/SlipVerificationManager.php`:
```php
$this->api_config = [
    'api_key' => 'YOUR_API_KEY',
    'record_key' => 'YOUR_RECORD_KEY',
    'api_url' => 'https://paynoi.com/api_line'
];
```

## การใช้งาน

### สำหรับ Admin
1. เข้าไปที่เมนู "จัดการระบบ" → "ตรวจสอบสลิป"
2. ดูสถานะการทำงานของระบบ
3. กดปุ่ม "ตรวจสอบทันที" เพื่อรันการตรวจสอบแบบ manual

### API Endpoints

#### 1. ดูสถานะระบบ
```
GET /api/slip_verification.php?action=status
```

#### 2. ตรวจสอบสลิปทันที
```
POST /api/slip_verification.php?action=verify
```

#### 3. ดูธุรกรรมล่าสุด
```
GET /api/slip_verification.php?action=transactions
```

#### 4. ดึงข้อมูลจาก API ใหม่
```
POST /api/slip_verification.php?action=fetch
```

## การทำงานของระบบ

### 1. ดึงข้อมูลธุรกรรม
- ดึงข้อมูลจาก API ของธนาคาร
- หากล้มเหลว จะอ่านจากไฟล์ backup

### 2. การจับคู่ธุรกรรม
- ตรวจสอบเฉพาะธุรกรรม "เงินเข้า"
- จับคู่จำนวนเงินกับการเติมเงินที่รอการอนุมัติ
- ตรวจสอบว่าธุรกรรมยังไม่ถูกประมวลผล

### 3. การอนุมัติ
- อัปเดตสถานะการเติมเงินเป็น "approved"
- เพิ่มเงินให้ผู้ใช้
- บันทึก transaction ID
- สร้าง usage log

### 4. ป้องกันการประมวลผลซ้ำ
- บันทึก transaction ID ที่ประมวลผลแล้วในตาราง `processed_transactions`
- ตรวจสอบก่อนประมวลผลทุกครั้ง

## ตัวอย่างการใช้งาน

### Manual Verification
```php
$slipManager = new SlipVerificationManager();
$result = $slipManager->processAutoApproval();

if ($result['success']) {
    echo "ประมวลผล: {$result['processed']} รายการ";
    echo "อนุมัติ: {$result['approved']} รายการ";
}
```

### API Call
```javascript
// ตรวจสอบสลิปทันที
fetch('/api/slip_verification.php?action=verify', {
    method: 'POST'
})
.then(response => response.json())
.then(data => {
    console.log('ผลลัพธ์:', data);
});
```

## การ Monitor และ Debug

### Log Files
- `cron/logs/slip_verification.log` - Log จาก cron job
- PHP error log - Log จาก SlipVerificationManager

### การตรวจสอบสถานะ
1. ดูจำนวนการเติมเงินที่รอการอนุมัติ
2. ตรวจสอบ timestamp ของข้อมูลธุรกรรมล่าสุด
3. ดู log การทำงานของ cron job

### การแก้ไขปัญหา
1. **API ไม่ตอบสนอง**: ระบบจะใช้ข้อมูลจากไฟล์ backup
2. **การจับคู่ผิดพลาด**: ตรวจสอบจำนวนเงินและเวลา
3. **ประมวลผลซ้ำ**: ระบบป้องกันด้วยตาราง processed_transactions

## ความปลอดภัย

### การป้องกัน
- ตรวจสอบ transaction ID ซ้ำ
- จำกัดการจับคู่ภายใน 24 ชั่วโมง
- บันทึก log ทุกการทำงาน
- API key authentication (optional)

### ข้อควรระวัง
- ตรวจสอบจำนวนเงินให้ถูกต้อง
- ระวังการจับคู่ผิดพลาด
- สำรองข้อมูลก่อนรันระบบ

## การบำรุงรักษา

### รายวัน
- ตรวจสอบ log การทำงาน
- ดูจำนวนการอนุมัติ

### รายสัปดาห์
- ทำความสะอาด log เก่า
- ตรวจสอบข้อมูลในตาราง processed_transactions

### รายเดือน
- สำรองข้อมูลธุรกรรม
- ตรวจสอบประสิทธิภาพระบบ
