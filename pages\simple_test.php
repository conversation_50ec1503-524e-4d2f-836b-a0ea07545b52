<?php
// Session already started in index.php

// ตรวจสอบสิทธิ์แอดมิน
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo "<h1>❌ Access Denied</h1>";
    echo "<p>คุณต้องเป็น admin เพื่อเข้าหน้านี้</p>";
    echo "<p><a href='?page=login'>เข้าสู่ระบบ</a></p>";
    exit();
}

echo "<h1>🧪 ทดสอบแบบง่าย</h1>";

// ทดสอบการโหลดไฟล์
echo "<h2>📁 ตรวจสอบไฟล์</h2>";
$files = [
    'config/db_config.php',
    'classes/EmbyAPI.php',
    'classes/JellyfinAPI.php',
    'classes/SubscriptionManager.php'
];

foreach ($files as $file) {
    echo "<p><strong>$file:</strong> " . (file_exists($file) ? '✅ มีอยู่' : '❌ ไม่มี') . "</p>";
}

// ทดสอบการเชื่อมต่อฐานข้อมูล
echo "<h2>🔗 ทดสอบฐานข้อมูล</h2>";
try {
    require_once 'config/db_config.php';
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "<p>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
        
        // ทดสอบ query
        $query = "SELECT COUNT(*) as count FROM users";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ พบ users จำนวน: " . $result['count'] . " คน</p>";
        
    } else {
        echo "<p>❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาดฐานข้อมูล: " . $e->getMessage() . "</p>";
}

// ทดสอบการโหลด API classes
echo "<h2>🔧 ทดสอบ API Classes</h2>";
try {
    require_once 'classes/EmbyAPI.php';
    echo "<p>✅ โหลด EmbyAPI สำเร็จ</p>";
    
    require_once 'classes/JellyfinAPI.php';
    echo "<p>✅ โหลด JellyfinAPI สำเร็จ</p>";
    
    require_once 'classes/SubscriptionManager.php';
    echo "<p>✅ โหลด SubscriptionManager สำเร็จ</p>";
    
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาดในการโหลด classes: " . $e->getMessage() . "</p>";
}

// ทดสอบการสร้าง API instances
echo "<h2>🚀 ทดสอบการสร้าง API Instances</h2>";

// Emby API
try {
    $emby_url = 'https://emby.embyjames.xyz';
    $emby_key = 'd2499d0eacfe4ccbba940836be91a9f1';
    
    $emby_api = new EmbyAPI($emby_url, $emby_key);
    echo "<p>✅ สร้าง EmbyAPI instance สำเร็จ</p>";
    
    // ทดสอบ method ที่มีอยู่
    $methods = get_class_methods($emby_api);
    echo "<p>📋 EmbyAPI methods: " . count($methods) . " methods</p>";
    
    if (in_array('getAllUsers', $methods)) {
        echo "<p>✅ มี method getAllUsers</p>";
        
        // ทดสอบเรียก API
        $result = $emby_api->getAllUsers();
        if ($result['success']) {
            $count = is_array($result['data']) ? count($result['data']) : 0;
            echo "<p>✅ เชื่อมต่อ Emby สำเร็จ - พบ $count users</p>";
        } else {
            echo "<p>❌ เชื่อมต่อ Emby ล้มเหลว: " . ($result['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p>❌ ไม่มี method getAllUsers</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาด EmbyAPI: " . $e->getMessage() . "</p>";
}

// Jellyfin API
try {
    $jellyfin_url = 'https://jellyfin.embyjames.xyz';
    $jellyfin_key = 'e30753ff625847e0bf7163df609ebaf6';
    
    $jellyfin_api = new JellyfinAPI($jellyfin_url, $jellyfin_key);
    echo "<p>✅ สร้าง JellyfinAPI instance สำเร็จ</p>";
    
    // ทดสอบ method ที่มีอยู่
    $methods = get_class_methods($jellyfin_api);
    echo "<p>📋 JellyfinAPI methods: " . count($methods) . " methods</p>";
    
    if (in_array('getAllUsers', $methods)) {
        echo "<p>✅ มี method getAllUsers</p>";
        
        // ทดสอบเรียก API
        $result = $jellyfin_api->getAllUsers();
        if ($result['success']) {
            $count = is_array($result['data']) ? count($result['data']) : 0;
            echo "<p>✅ เชื่อมต่อ Jellyfin สำเร็จ - พบ $count users</p>";
        } else {
            echo "<p>❌ เชื่อมต่อ Jellyfin ล้มเหลว: " . ($result['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p>❌ ไม่มี method getAllUsers</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาด JellyfinAPI: " . $e->getMessage() . "</p>";
}

// ทดสอบ SubscriptionManager
echo "<h2>🔧 ทดสอบ SubscriptionManager</h2>";
try {
    $subscription_manager = new SubscriptionManager();
    echo "<p>✅ สร้าง SubscriptionManager สำเร็จ</p>";
    
    // ทดสอบ methods
    $methods = get_class_methods($subscription_manager);
    echo "<p>📋 SubscriptionManager methods: " . count($methods) . " methods</p>";
    
    $required_methods = ['disableUsersForExpiredSubscription', 'enableUsersForRenewedSubscription'];
    foreach ($required_methods as $method) {
        if (in_array($method, $methods)) {
            echo "<p>✅ มี method $method</p>";
        } else {
            echo "<p>❌ ไม่มี method $method</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาด SubscriptionManager: " . $e->getMessage() . "</p>";
}

// ทดสอบการ disable users
if (isset($_POST['test_user_id'])) {
    $user_id = intval($_POST['test_user_id']);
    echo "<h2>🧪 ทดสอบการ Disable User ID: $user_id</h2>";
    
    try {
        $subscription_manager = new SubscriptionManager();
        $result = $subscription_manager->disableUsersForExpiredSubscription($user_id);
        
        echo "<h3>📊 ผลลัพธ์:</h3>";
        echo "<pre>";
        print_r($result);
        echo "</pre>";
        
    } catch (Exception $e) {
        echo "<p>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
    }
}

// ดึงข้อมูล users
echo "<h2>👥 Users ที่มี Media Server Accounts</h2>";
try {
    if (isset($conn)) {
        $query = "SELECT DISTINCT u.id, u.username, u.full_name,
                         ea.emby_user_id, ja.jellyfin_user_id,
                         us.status as subscription_status
                  FROM users u
                  LEFT JOIN emby_accounts ea ON u.id = ea.user_id
                  LEFT JOIN jellyfin_accounts ja ON u.id = ja.user_id
                  LEFT JOIN user_subscriptions us ON u.id = us.user_id
                  WHERE (ea.emby_user_id IS NOT NULL OR ja.jellyfin_user_id IS NOT NULL)
                  ORDER BY u.id LIMIT 5";
        
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($users)) {
            echo "<p>❌ ไม่มี users ที่มี media server accounts</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Username</th>";
            echo "<th style='padding: 8px;'>Emby ID</th>";
            echo "<th style='padding: 8px;'>Jellyfin ID</th>";
            echo "<th style='padding: 8px;'>Subscription</th>";
            echo "<th style='padding: 8px;'>Test</th>";
            echo "</tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$user['id']}</td>";
                echo "<td style='padding: 8px;'>{$user['username']}</td>";
                echo "<td style='padding: 8px;'>" . ($user['emby_user_id'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 8px;'>" . ($user['jellyfin_user_id'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 8px;'>" . ($user['subscription_status'] ?? 'N/A') . "</td>";
                echo "<td style='padding: 8px;'>";
                
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='test_user_id' value='{$user['id']}'>";
                echo "<button type='submit' style='background: #dc3545; color: white; padding: 5px 10px; border: none; border-radius: 4px; cursor: pointer;' onclick='return confirm(\"ทดสอบ disable user {$user['username']}?\")'>Test Disable</button>";
                echo "</form>";
                
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาดในการดึงข้อมูล users: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🔗 ลิงก์</h3>";
echo "<ul>";
echo "<li><a href='?page=admin_subscriptions'>กลับหน้า Admin Subscriptions</a></li>";
echo "<li><a href='?page=simple_test'>รีเฟรชหน้านี้</a></li>";
echo "</ul>";
?>
