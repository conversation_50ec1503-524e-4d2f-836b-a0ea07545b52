<?php
/**
 * ServerStatus Class
 * 
 * ใช้สำหรับตรวจสอบสถานะของ Media Server
 */
class ServerStatus {
    private $conn;
    private $settings;
    private $cache_duration = 60; // Cache for 60 seconds

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->settings = new SystemSettings($this->conn);
    }

    /**
     * ดึงข้อมูลจาก cache หรือสร้างใหม่
     */
    private function getCachedStatus($key, $callback) {
        $cache_file = sys_get_temp_dir() . '/media_server_' . $key . '.cache';

        // ตรวจสอบว่า cache ยังใช้ได้หรือไม่
        if(file_exists($cache_file) && (time() - filemtime($cache_file)) < $this->cache_duration) {
            $cached_data = file_get_contents($cache_file);
            if($cached_data) {
                return json_decode($cached_data, true);
            }
        }

        // สร้างข้อมูลใหม่
        $data = $callback();

        // บันทึก cache
        file_put_contents($cache_file, json_encode($data));

        return $data;
    }
    
    /**
     * ตรวจสอบสถานะของ Emby Server
     *
     * @return array สถานะของ Emby Server
     */
    public function checkEmbyStatus() {
        return $this->getCachedStatus('emby', function() {
            $emby_url = $this->settings->get('emby_server_url');
            $emby_key = $this->settings->get('emby_api_key');

            if(!$emby_url || !$emby_key) {
                return [
                    'status' => 'offline',
                    'message' => 'ไม่ได้ตั้งค่า Emby Server',
                    'response_time' => 0
                ];
            }

            $start_time = microtime(true);
            $status = $this->checkEmbyAPI($emby_url, $emby_key);
            $end_time = microtime(true);
            $response_time = round(($end_time - $start_time) * 1000); // ms

            return [
                'status' => $status ? 'online' : 'offline',
                'message' => $status ? 'Emby Server พร้อมใช้งาน' : 'Emby Server ไม่ตอบสนอง',
                'response_time' => $status ? $response_time : 0
            ];
        });
    }
    
    /**
     * ตรวจสอบสถานะของ Jellyfin Server
     *
     * @return array สถานะของ Jellyfin Server
     */
    public function checkJellyfinStatus() {
        return $this->getCachedStatus('jellyfin', function() {
            $jellyfin_url = $this->settings->get('jellyfin_server_url');
            $jellyfin_key = $this->settings->get('jellyfin_api_key');

            if(!$jellyfin_url || !$jellyfin_key) {
                return [
                    'status' => 'offline',
                    'message' => 'ไม่ได้ตั้งค่า Jellyfin Server',
                    'response_time' => 0
                ];
            }

            $start_time = microtime(true);
            $status = $this->checkJellyfinAPI($jellyfin_url, $jellyfin_key);
            $end_time = microtime(true);
            $response_time = round(($end_time - $start_time) * 1000); // ms

            return [
                'status' => $status ? 'online' : 'offline',
                'message' => $status ? 'Jellyfin Server พร้อมใช้งาน' : 'Jellyfin Server ไม่ตอบสนอง',
                'response_time' => $status ? $response_time : 0
            ];
        });
    }
    
    /**
     * ตรวจสอบสถานะของ Web Server
     * 
     * @return array สถานะของ Web Server
     */
    public function checkWebStatus() {
        $start_time = microtime(true);
        $end_time = microtime(true);
        $response_time = round(($end_time - $start_time) * 1000); // ms
        
        return [
            'status' => 'online',
            'message' => 'Web Server พร้อมใช้งาน',
            'response_time' => $response_time
        ];
    }
    
    /**
     * ดึงข้อมูลหนังและซีรีส์ล่าสุด
     *
     * @param int $limit จำนวนรายการที่ต้องการ
     * @return array รายการหนังและซีรีส์ล่าสุด
     */
    public function getLatestMedia($limit = 5) {
        try {
            require_once 'classes/MediaManager.php';
            $mediaManager = new MediaManager();

            // ดึงข้อมูลจากฐานข้อมูล
            $media_list = $mediaManager->getActiveMedia($limit);

            // ถ้าไม่มีข้อมูลในฐานข้อมูล ใช้ข้อมูลเริ่มต้น
            if(empty($media_list)) {
                $default_media = [
                    [
                        'title' => 'Venom: The Last Dance',
                        'year' => '2024',
                        'poster' => 'https://image.tmdb.org/t/p/original/hw1gSt2KbFTFtJK9IjEPyYCq25i.jpg',
                        'type' => 'movie'
                    ],
                    [
                        'title' => 'Terrifier 3',
                        'year' => '2024',
                        'poster' => 'https://image.tmdb.org/t/p/original/s9SmRfDH0hikqjVpCWy5qvi70ZM.jpg',
                        'type' => 'movie'
                    ],
                    [
                        'title' => 'Smile 2',
                        'year' => '2024',
                        'poster' => 'https://image.tmdb.org/t/p/original/jNctLh193rNy8yeo8T05X5FXDD8.jpg',
                        'type' => 'movie'
                    ],
                    [
                        'title' => 'The Wild Robot',
                        'year' => '2024',
                        'poster' => 'https://image.tmdb.org/t/p/original/7QeX24363a9AeaZ7UnIpFHDlH1F.jpg',
                        'type' => 'movie'
                    ],
                    [
                        'title' => 'Beetlejuice Beetlejuice',
                        'year' => '2024',
                        'poster' => 'https://image.tmdb.org/t/p/original/cjrSkULmG2btwLOEvWZCeO5KRY2.jpg',
                        'type' => 'movie'
                    ]
                ];

                return array_slice($default_media, 0, $limit);
            }

            return $media_list;

        } catch(Exception $e) {
            // ถ้าเกิดข้อผิดพลาด ใช้ข้อมูลเริ่มต้น
            $default_media = [
                [
                    'title' => 'Venom: The Last Dance',
                    'year' => '2024',
                    'poster' => 'https://image.tmdb.org/t/p/original/hw1gSt2KbFTFtJK9IjEPyYCq25i.jpg',
                    'type' => 'movie'
                ]
            ];

            return array_slice($default_media, 0, $limit);
        }
    }
    
    /**
     * ตรวจสอบ Emby API
     *
     * @param string $url URL ของ Emby Server
     * @param string $api_key API Key
     * @return bool สถานะการเชื่อมต่อ
     */
    private function checkEmbyAPI($url, $api_key) {
        // ลบ trailing slash
        $url = rtrim($url, '/');

        // ลอง endpoint หลายตัว (เรียงตามความน่าจะเป็น)
        $endpoints = [
            '/System/Info/Public',              // มาตรฐาน Emby/Jellyfin
            '/emby/System/Info/Public',         // Emby specific
            '/System/Info?api_key=' . urlencode($api_key),
            '/emby/System/Info?api_key=' . urlencode($api_key)
        ];

        foreach($endpoints as $endpoint) {
            $full_url = $url . $endpoint;

            $ch = curl_init($full_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'MediaServerManager/1.0');
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // ถ้าไม่มี error และได้ response
            if($response !== false && empty($error) && $http_code == 200) {
                $data = json_decode($response, true);
                if($data && is_array($data)) {
                    // ตรวจสอบว่ามีข้อมูลที่บ่งบอกว่าเป็น media server
                    if(isset($data['ServerName']) ||
                       isset($data['Version']) ||
                       isset($data['ProductName']) ||
                       isset($data['Id'])) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * ตรวจสอบ Jellyfin API
     *
     * @param string $url URL ของ Jellyfin Server
     * @param string $api_key API Key
     * @return bool สถานะการเชื่อมต่อ
     */
    private function checkJellyfinAPI($url, $api_key) {
        // ลบ trailing slash
        $url = rtrim($url, '/');

        // ลอง endpoint หลายตัว
        $endpoints = [
            '/System/Info/Public',
            '/System/Info?api_key=' . urlencode($api_key)
        ];

        foreach($endpoints as $endpoint) {
            $full_url = $url . $endpoint;

            $ch = curl_init($full_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'MediaServerManager/1.0');
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // ถ้าไม่มี error และได้ response
            if($response !== false && empty($error) && $http_code == 200) {
                $data = json_decode($response, true);
                if($data && is_array($data)) {
                    // ตรวจสอบว่ามีข้อมูลที่บ่งบอกว่าเป็น media server
                    if(isset($data['ServerName']) ||
                       isset($data['Version']) ||
                       isset($data['ProductName']) ||
                       isset($data['Id'])) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * ทดสอบการเชื่อมต่อกับเซิร์ฟเวอร์ (เก่า - สำหรับ fallback)
     *
     * @param string $url URL ของเซิร์ฟเวอร์
     * @return bool สถานะการเชื่อมต่อ
     */
    private function pingServer($url) {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 3);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $response !== false && ($http_code >= 200 && $http_code < 400);
    }
}
?>
