# การตรวจสอบสลิปอัตโนมัติทุก 5 วินาที

## ภาพรวม
ระบบตรวจสอบสลิปอัตโนมัติที่ทำงานเหมือนหน้า slip verification แต่รันอัตโนมัติทุก 5 วินาที เพื่อให้การอนุมัติเติมเงินเร็วขึ้น

## ไฟล์ที่เกี่ยวข้อง

### สคริปหลัก
- `slip_verification_cron_5sec.php` - สคริป PHP หลักสำหรับตรวจสอบสลิป
- `start_slip_verification_5sec.bat` - สคริป Batch สำหรับ Windows
- `start_slip_verification_5sec.ps1` - สคริป PowerShell สำหรับ Windows (แนะนำ)
- `test_slip_verification.bat` - สคริปทดสอบ (รันครั้งเดียว)

### Log Files
- `logs/slip_verification_5sec.log` - Log การทำงานของระบบ

## วิธีการใช้งาน

### 1. ทดสอบก่อนใช้งาน
```cmd
# รันครั้งเดียวเพื่อทดสอบ
cron\test_slip_verification.bat
```

### 2. รันแบบต่อเนื่อง

#### วิธีที่ 1: PowerShell (แนะนำ)
```powershell
# เปิด PowerShell และไปยัง directory ของโปรเจค
cd C:\laragon\www\your-project

# อนุญาตให้รัน PowerShell script (ครั้งแรกเท่านั้น)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# รันสคริป
.\cron\start_slip_verification_5sec.ps1
```

#### วิธีที่ 2: Command Prompt
```cmd
# เปิด Command Prompt และไปยัง directory ของโปรเจค
cd C:\laragon\www\your-project

# รันสคริป
cron\start_slip_verification_5sec.bat
```

## การทำงานของระบบ

### ขั้นตอนการทำงาน (ทุก 5 วินาที)
1. **ตรวจสอบ Lock File** - ป้องกันการรันซ้ำ
2. **เชื่อมต่อฐานข้อมูล** - ตรวจสอบการเชื่อมต่อ
3. **ดึงสถิติก่อนตรวจสอบ** - นับการเติมเงินที่รอการอนุมัติ
4. **ตรวจสอบธุรกรรม** - ดึงข้อมูลจาก API หรือไฟล์
5. **จับคู่และอนุมัติ** - หาการเติมเงินที่ตรงกับธุรกรรม
6. **บันทึก Log** - เก็บประวัติการทำงาน
7. **แสดงสถิติ** - สรุปผลการทำงาน

### การแสดงผล
- ✅ **สำเร็จ**: การอนุมัติที่สำเร็จ (สีเขียว)
- ⚠️ **คำเตือน**: ข้อผิดพลาดที่ไม่ร้ายแรง (สีเหลือง)
- ❌ **ข้อผิดพลาด**: ข้อผิดพลาดร้ายแรง (สีแดง)
- 📊 **สถิติ**: ข้อมูลสถิติการทำงาน (สีฟ้า)

### การจัดการ Log
- เก็บ Log ล่าสุด 2000 บรรทัด
- ทำความสะอาดอัตโนมัติ
- แสดงเฉพาะข้อมูลสำคัญใน console

## ข้อดีของระบบ 5 วินาที

### เปรียบเทียบกับระบบเดิม
| ระบบ | ความถี่ | ความเร็วอนุมัติ | การใช้ทรัพยากร |
|------|---------|----------------|----------------|
| เดิม | Manual | ต้องกดปุ่ม | ต่ำ |
| 5 วินาที | อัตโนมัติ | เร็วมาก | ปานกลาง |
| 1 นาที | อัตโนมัติ | เร็ว | ต่ำ |

### ข้อดี
- ✅ **อนุมัติเร็ว**: ลูกค้าไม่ต้องรอนาน
- ✅ **ลดงาน Manual**: ไม่ต้องกดปุ่มตรวจสอบ
- ✅ **Real-time**: ใกล้เคียงกับการทำงานแบบ real-time
- ✅ **สถิติครบถ้วน**: แสดงข้อมูลการทำงานละเอียด

### ข้อควรระวัง
- ⚠️ **ใช้ทรัพยากรมากขึ้น**: CPU และ Database
- ⚠️ **Log เยอะ**: ต้องจัดการ log file
- ⚠️ **ต้องเฝาดู**: ควรมีคนดูแลระบบ

## การหยุดการทำงาน

### Windows
- กด `Ctrl + C` ใน Command Prompt/PowerShell
- ปิดหน้าต่าง Command Prompt

### การตรวจสอบสถานะ
```cmd
# ดู log แบบ real-time (PowerShell)
Get-Content cron\logs\slip_verification_5sec.log -Wait -Tail 20

# ดู log ล่าสุด
type cron\logs\slip_verification_5sec.log | findstr /C:"สถิติ" /C:"อนุมัติ"
```

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

#### 1. Script รันซ้ำ
```
Script is already running, skipping...
```
**วิธีแก้**: รอ 10 วินาที หรือลบไฟล์ `cron/slip_verification.lock`

#### 2. ไม่พบ PHP
```
❌ ไม่พบ PHP ในระบบ
```
**วิธีแก้**: ติดตั้ง PHP หรือเพิ่ม PHP ใน PATH

#### 3. Database Connection Error
```
❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้
```
**วิธีแก้**: ตรวจสอบการตั้งค่าใน `config/db_config.php`

#### 4. Foreign Key Error
```
Foreign key constraint fails
```
**วิธีแก้**: รัน `sql/fix_foreign_key.sql`

## การปรับแต่ง

### เปลี่ยนความถี่การตรวจสอบ
แก้ไขใน script:
```batch
REM เปลี่ยนจาก 5 วินาที เป็น 10 วินาที
timeout /t 10 /nobreak >nul
```

```powershell
# เปลี่ยนจาก 5 วินาที เป็น 10 วินาที
Start-Sleep -Seconds 10
```

### เปลี่ยนจำนวน Log ที่เก็บ
แก้ไขใน `slip_verification_cron_5sec.php`:
```php
// เก็บเฉพาะ log ล่าสุด 5000 บรรทัด
if (count($lines) > 5000) {
    $recent_lines = array_slice($lines, -5000);
```

### เพิ่มการแจ้งเตือน
เพิ่มใน `slip_verification_cron_5sec.php`:
```php
// ส่งอีเมลเมื่อมีการอนุมัติ
if ($approved > 0) {
    mail('<EMAIL>', 'Slip Approved', "อนุมัติ {$approved} รายการ");
}
```

## การ Monitor

### ดูสถิติการทำงาน
```cmd
# ดูจำนวนการอนุมัติวันนี้
findstr /C:"อนุมัติ.*สำเร็จ" cron\logs\slip_verification_5sec.log

# ดูข้อผิดพลาด
findstr /C:"ERROR" cron\logs\slip_verification_5sec.log

# ดูสถิติล่าสุด
findstr /C:"สถิติวันนี้" cron\logs\slip_verification_5sec.log | tail -1
```

### การตั้งค่า Windows Service (ขั้นสูง)
สำหรับการรันในพื้นหลังแบบ service:
1. ใช้ NSSM (Non-Sucking Service Manager)
2. สร้าง service ที่เรียก PowerShell script
3. ตั้งให้เริ่มอัตโนมัติเมื่อเปิดเครื่อง

## ความปลอดภัย

### การป้องกัน
- ✅ ป้องกันการรันซ้ำด้วย lock file
- ✅ ตรวจสอบ CLI mode
- ✅ จำกัดขนาดไฟล์ log
- ✅ บันทึก log ทุกการทำงาน

### Best Practices
- 📊 ตรวจสอบ log เป็นประจำ
- 💾 สำรองข้อมูลก่อนใช้งาน
- 🔧 ทดสอบในสภาพแวดล้อม development ก่อน
- 📈 Monitor การใช้ทรัพยากรระบบ
