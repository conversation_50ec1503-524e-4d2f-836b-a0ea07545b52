# Main Cron Job Runner for PowerShell
# รันงานหลักทั้งหมด: ดึง transaction, ตรวจสลิป, เช็ค subscription

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Main Cron Job - Starting" -ForegroundColor Green
Write-Host "Time: $(Get-Date)" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan

# Change to script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

try {
    # Run the main cron script
    $process = Start-Process -FilePath "php" -ArgumentList "main_cron.php" -Wait -PassThru -NoNewWindow
    $exitCode = $process.ExitCode
    
    if ($exitCode -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Cyan
        Write-Host "Main Cron Job - COMPLETED SUCCESSFULLY" -ForegroundColor Green
        Write-Host "Time: $(Get-Date)" -ForegroundColor Yellow
        Write-Host "========================================" -ForegroundColor Cyan
    } else {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Cyan
        Write-Host "Main Cron Job - COMPLETED WITH ERRORS" -ForegroundColor Red
        Write-Host "Exit Code: $exitCode" -ForegroundColor Red
        Write-Host "Time: $(Get-Date)" -ForegroundColor Yellow
        Write-Host "========================================" -ForegroundColor Cyan
    }
    
    exit $exitCode
    
} catch {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Main Cron Job - FAILED TO START" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Time: $(Get-Date)" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    exit 1
}
