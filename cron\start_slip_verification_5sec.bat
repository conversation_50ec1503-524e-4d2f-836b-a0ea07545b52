@echo off
title Slip Verification Auto Service - Every 5 Seconds
color 0A

echo ========================================
echo   Slip Verification Auto Service
echo   ตรวจสอบสลิปอัตโนมัติทุก 5 วินาที
echo ========================================
echo.

REM เปลี่ยนไปยัง directory ของโปรเจค
cd /d "%~dp0\.."

echo Current directory: %CD%
echo.

REM ค้นหา PHP ในตำแหน่งต่างๆ
set PHP_PATH=""

REM ลองหา PHP ใน PATH ก่อน
php --version >nul 2>&1
if not errorlevel 1 (
    set PHP_PATH=php
    goto php_found
)

REM ลองหาใน Laragon
for /d %%i in ("C:\laragon\bin\php\php-*") do (
    if exist "%%i\php.exe" (
        set PHP_PATH="%%i\php.exe"
        goto php_found
    )
)

REM ลองหาใน XAMPP
if exist "C:\xampp\php\php.exe" (
    set PHP_PATH="C:\xampp\php\php.exe"
    goto php_found
)

REM ลองหาใน WAMP
for /d %%i in ("C:\wamp64\bin\php\php*") do (
    if exist "%%i\php.exe" (
        set PHP_PATH="%%i\php.exe"
        goto php_found
    )
)

echo ❌ ไม่พบ PHP ในระบบ
echo กรุณาติดตั้ง PHP หรือแก้ไข path ในสคริปนี้
echo ตำแหน่งที่ค้นหา:
echo - C:\laragon\bin\php\
echo - C:\xampp\php\
echo - C:\wamp64\bin\php\
echo - PATH environment
pause
exit /b 1

:php_found
echo ✅ พบ PHP แล้ว: %PHP_PATH%
%PHP_PATH% --version
echo.
echo 🚀 เริ่มการตรวจสอบสลิปอัตโนมัติ...
echo 📝 Log จะถูกบันทึกใน cron/logs/slip_verification_5sec.log
echo 🔄 ระบบจะตรวจสอบทุก 5 วินาที
echo.
echo กด Ctrl+C เพื่อหยุดการทำงาน
echo ========================================
echo.

REM สร้าง counter สำหรับแสดงสถิติ
set /a counter=0
set /a total_approved=0

:loop
    set /a counter+=1
    
    REM แสดงสถานะ
    echo [%date% %time%] รอบที่ %counter% - กำลังตรวจสอบสลิป...
    
    REM รัน PHP script และจับผลลัพธ์
    %PHP_PATH% cron/slip_verification_cron_5sec.php 2>&1 | findstr /C:"อนุมัติ" /C:"ERROR" /C:"WARNING" /C:"สถิติ"
    
    REM ตรวจสอบ error level
    if errorlevel 1 (
        echo ⚠️ เกิดข้อผิดพลาดในการรัน PHP script
    )
    
    REM แสดงสถิติทุก 12 รอบ (1 นาที)
    set /a mod=counter%%12
    if %mod%==0 (
        echo.
        echo 📊 สถิติ: ตรวจสอบแล้ว %counter% รอบ (%.1f นาที^)
        echo ========================================
    )
    
    REM รอ 5 วินาที
    timeout /t 5 /nobreak >nul
    
    REM ตรวจสอบว่าผู้ใช้กด Ctrl+C หรือไม่
    if errorlevel 1 goto end
    
goto loop

:end
echo.
echo ========================================
echo 🛑 หยุดการทำงานแล้ว
echo 📊 รวมตรวจสอบ: %counter% รอบ
echo 📝 Log file: cron\logs\slip_verification_5sec.log
echo ========================================
pause
