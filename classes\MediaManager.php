<?php
/**
 * MediaManager Class
 * 
 * จัดการข้อมูลหนังและซีรีส์
 */
class MediaManager {
    private $conn;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->createMediaTable();
    }
    
    /**
     * สร้างตารางสำหรับเก็บข้อมูลหนังและซีรีส์
     */
    private function createMediaTable() {
        $query = "CREATE TABLE IF NOT EXISTS media_content (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            year INT NOT NULL,
            type ENUM('movie', 'series') NOT NULL,
            poster TEXT NOT NULL,
            description TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $this->conn->exec($query);
        
        // เพิ่มข้อมูลเริ่มต้นถ้าตารางว่าง
        $this->insertDefaultData();
    }
    
    /**
     * เพิ่มข้อมูลเริ่มต้น
     */
    private function insertDefaultData() {
        $count_query = "SELECT COUNT(*) FROM media_content";
        $count_stmt = $this->conn->prepare($count_query);
        $count_stmt->execute();
        $count = $count_stmt->fetchColumn();
        
        if($count == 0) {
            $default_media = [
                [
                    'title' => 'Venom: The Last Dance',
                    'year' => 2024,
                    'type' => 'movie',
                    'poster' => 'https://image.tmdb.org/t/p/original/hw1gSt2KbFTFtJK9IjEPyYCq25i.jpg',
                    'description' => 'ภาคสุดท้ายของ Venom ที่เต็มไปด้วยแอคชั่นและความตื่นเต้น'
                ],
                [
                    'title' => 'Terrifier 3',
                    'year' => 2024,
                    'type' => 'movie',
                    'poster' => 'https://image.tmdb.org/t/p/original/s9SmRfDH0hikqjVpCWy5qvi70ZM.jpg',
                    'description' => 'หนังสยองขวัญที่จะทำให้คุณสั่นสะเทือน'
                ],
                [
                    'title' => 'Smile 2',
                    'year' => 2024,
                    'type' => 'movie',
                    'poster' => 'https://image.tmdb.org/t/p/original/jNctLh193rNy8yeo8T05X5FXDD8.jpg',
                    'description' => 'ภาคต่อของหนังสยองขวัญที่ทำให้หลายคนต้องปิดตา'
                ],
                [
                    'title' => 'The Wild Robot',
                    'year' => 2024,
                    'type' => 'movie',
                    'poster' => 'https://image.tmdb.org/t/p/original/7QeX24363a9AeaZ7UnIpFHDlH1F.jpg',
                    'description' => 'เรื่องราวของหุ่นยนต์ที่ต้องเรียนรู้การอยู่รอดในป่า'
                ],
                [
                    'title' => 'Beetlejuice Beetlejuice',
                    'year' => 2024,
                    'type' => 'movie',
                    'poster' => 'https://image.tmdb.org/t/p/original/cjrSkULmG2btwLOEvWZCeO5KRY2.jpg',
                    'description' => 'การกลับมาของ Beetlejuice ในภาคต่อที่รอคอยมานาน'
                ],
                [
                    'title' => 'House of the Dragon',
                    'year' => 2024,
                    'type' => 'series',
                    'poster' => 'https://image.tmdb.org/t/p/w500/xiB0hsxMpgvEWAEPZHmMfLxjkVR.jpg',
                    'description' => 'ซีรีส์แฟนตาซีที่เล่าเรื่องราวของตระกูล Targaryen'
                ],
                [
                    'title' => 'The Penguin',
                    'year' => 2024,
                    'type' => 'series',
                    'poster' => 'https://image.tmdb.org/t/p/w500/qZlAD0HHJQWQvWs5mFRtmvHEVZ.jpg',
                    'description' => 'ซีรีส์ที่เล่าเรื่องราวของ Penguin จากจักรวาล Batman'
                ],
                [
                    'title' => 'The Lord of the Rings: The Rings of Power',
                    'year' => 2024,
                    'type' => 'series',
                    'poster' => 'https://image.tmdb.org/t/p/w500/rqMYRQQEfUFDl1GGgVB2cN7LnbL.jpg',
                    'description' => 'ซีรีส์แฟนตาซีจากจักรวาล Lord of the Rings'
                ]
            ];
            
            foreach($default_media as $media) {
                $this->addMediaDirect($media);
            }
        }
    }
    
    /**
     * เพิ่มข้อมูลหนัง/ซีรีส์
     */
    public function addMedia($data) {
        try {
            $query = "INSERT INTO media_content (title, year, type, poster, description, status)
                      VALUES (:title, :year, :type, :poster, :description, :status)";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':year', $data['year'], PDO::PARAM_INT);
            $stmt->bindParam(':type', $data['type']);
            $stmt->bindParam(':poster', $data['poster']);
            $description = $data['description'] ?? '';
            $stmt->bindParam(':description', $description);
            $status = $data['status'] ?? 'active';
            $stmt->bindParam(':status', $status);

            if($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'เพิ่มข้อมูลสำเร็จ'
                ];
            } else {
                $error_info = $stmt->errorInfo();
                return [
                    'success' => false,
                    'message' => 'ไม่สามารถเพิ่มข้อมูลได้: ' . $error_info[2]
                ];
            }
        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * เพิ่มข้อมูลโดยตรง (สำหรับข้อมูลเริ่มต้น)
     */
    private function addMediaDirect($data) {
        $query = "INSERT INTO media_content (title, year, type, poster, description) 
                  VALUES (:title, :year, :type, :poster, :description)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':year', $data['year']);
        $stmt->bindParam(':type', $data['type']);
        $stmt->bindParam(':poster', $data['poster']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->execute();
    }
    
    /**
     * อัปเดตข้อมูลหนัง/ซีรีส์
     */
    public function updateMedia($id, $data) {
        try {
            // ตรวจสอบว่ามีข้อมูลอยู่จริง
            $check_query = "SELECT id FROM media_content WHERE id = :id";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(':id', $id);
            $check_stmt->execute();

            if($check_stmt->rowCount() == 0) {
                return [
                    'success' => false,
                    'message' => 'ไม่พบข้อมูลที่ต้องการแก้ไข'
                ];
            }

            $query = "UPDATE media_content
                      SET title = :title, year = :year, type = :type, poster = :poster,
                          description = :description, status = :status, updated_at = NOW()
                      WHERE id = :id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':year', $data['year'], PDO::PARAM_INT);
            $stmt->bindParam(':type', $data['type']);
            $stmt->bindParam(':poster', $data['poster']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':status', $data['status']);

            if($stmt->execute()) {
                $affected_rows = $stmt->rowCount();
                if($affected_rows > 0) {
                    return [
                        'success' => true,
                        'message' => 'อัปเดตข้อมูลสำเร็จ'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'ไม่มีการเปลี่ยนแปลงข้อมูล'
                    ];
                }
            } else {
                $error_info = $stmt->errorInfo();
                return [
                    'success' => false,
                    'message' => 'ไม่สามารถอัปเดตข้อมูลได้: ' . $error_info[2]
                ];
            }
        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * ลบข้อมูลหนัง/ซีรีส์
     */
    public function deleteMedia($id) {
        try {
            $query = "DELETE FROM media_content WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            
            if($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'ลบข้อมูลสำเร็จ'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'ไม่สามารถลบข้อมูลได้'
                ];
            }
        } catch(Exception $e) {
            return [
                'success' => false,
                'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * ดึงข้อมูลหนัง/ซีรีส์ทั้งหมด
     */
    public function getAllMedia() {
        $query = "SELECT * FROM media_content ORDER BY created_at DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * ดึงข้อมูลหนัง/ซีรีส์ที่แสดง
     */
    public function getActiveMedia($limit = null) {
        $query = "SELECT * FROM media_content WHERE status = 'active' ORDER BY created_at DESC";
        if($limit) {
            $query .= " LIMIT " . intval($limit);
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * ดึงข้อมูลหนัง/ซีรีส์ตาม ID
     */
    public function getMediaById($id) {
        $query = "SELECT * FROM media_content WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
