#!/bin/bash

echo "========================================"
echo "  Automatic Slip Verification Service"
echo "  รันทุก 5 วินาที"
echo "========================================"
echo

# เปลี่ยนไปยัง directory ของโปรเจค
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

# ตรวจสอบว่ามี PHP หรือไม่
if ! command -v php &> /dev/null; then
    echo "❌ ไม่พบ PHP ในระบบ"
    echo "กรุณาติดตั้ง PHP"
    exit 1
fi

echo "✅ พบ PHP แล้ว"
echo "🚀 เริ่มการตรวจสอบสลิปอัตโนมัติ..."
echo "📝 Log จะถูกบันทึกใน cron/logs/slip_verification_auto.log"
echo
echo "กด Ctrl+C เพื่อหยุดการทำงาน"
echo

# สร้าง trap เพื่อจัดการ Ctrl+C
trap 'echo -e "\n🛑 หยุดการทำงานแล้ว"; exit 0' INT

# วนลูปทุก 5 วินาที
while true; do
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] กำลังตรวจสอบ..."
    php cron/slip_verification_auto.php
    
    # รอ 5 วินาที
    sleep 5
done
