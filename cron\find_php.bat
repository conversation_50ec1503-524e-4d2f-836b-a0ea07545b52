@echo off
REM PHP Path Finder - หา PHP executable ในระบบ

echo ========================================
echo PHP Path Finder
echo ========================================

REM Try common PHP locations
set PHP_FOUND=0

echo Searching for PHP...
echo.

REM Check if php is in PATH
php -v >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [FOUND] PHP in system PATH
    php -v | findstr "PHP"
    set PHP_FOUND=1
    echo.
)

REM Check XAMPP
if exist "C:\xampp\php\php.exe" (
    echo [FOUND] XAMPP PHP: C:\xampp\php\php.exe
    "C:\xampp\php\php.exe" -v | findstr "PHP"
    set PHP_FOUND=1
    echo.
)

REM Check WAMP (common versions)
for %%v in (php8.3.* php8.2.* php8.1.* php8.0.* php7.4.*) do (
    if exist "C:\wamp64\bin\php\%%v\php.exe" (
        echo [FOUND] WAMP PHP: C:\wamp64\bin\php\%%v\php.exe
        "C:\wamp64\bin\php\%%v\php.exe" -v | findstr "PHP"
        set PHP_FOUND=1
        echo.
    )
)

REM Check Laragon (common versions)
for %%v in (php8.3.* php8.2.* php8.1.* php8.0.* php7.4.*) do (
    if exist "C:\laragon\bin\php\%%v\php.exe" (
        echo [FOUND] Laragon PHP: C:\laragon\bin\php\%%v\php.exe
        "C:\laragon\bin\php\%%v\php.exe" -v | findstr "PHP"
        set PHP_FOUND=1
        echo.
    )
)

REM Check Program Files
if exist "C:\Program Files\PHP\php.exe" (
    echo [FOUND] Program Files PHP: C:\Program Files\PHP\php.exe
    "C:\Program Files\PHP\php.exe" -v | findstr "PHP"
    set PHP_FOUND=1
    echo.
)

if exist "C:\Program Files (x86)\PHP\php.exe" (
    echo [FOUND] Program Files x86 PHP: C:\Program Files (x86)\PHP\php.exe
    "C:\Program Files (x86)\PHP\php.exe" -v | findstr "PHP"
    set PHP_FOUND=1
    echo.
)

echo ========================================
if %PHP_FOUND% EQU 1 (
    echo SUCCESS: Found PHP installation(s)
    echo.
    echo To use PHP globally, add one of the above paths to your system PATH:
    echo 1. Press Win+R, type "sysdm.cpl", press Enter
    echo 2. Click "Environment Variables"
    echo 3. Edit "Path" in System Variables
    echo 4. Add the PHP directory (without php.exe)
    echo.
    echo Example: C:\xampp\php
) else (
    echo ERROR: No PHP installation found!
    echo.
    echo Please install PHP using one of these methods:
    echo 1. XAMPP: https://www.apachefriends.org/
    echo 2. WAMP: https://www.wampserver.com/
    echo 3. Laragon: https://laragon.org/
    echo 4. Official PHP: https://windows.php.net/
)
echo ========================================

pause
