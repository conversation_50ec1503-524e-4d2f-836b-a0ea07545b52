<?php
// Check if user is admin
if(!isset($_SESSION['user_id'])) {
    header('Location: index.php?page=login');
    exit;
}

$database = new Database();
$conn = $database->getConnection();

$query = "SELECT role FROM users WHERE id = :user_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$user || $user['role'] != 'admin') {
    header('Location: index.php');
    exit;
}

require_once 'classes/MediaManager.php';
$mediaManager = new MediaManager();

$success_message = '';
$error_message = '';

// Handle form submissions
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        if(isset($_POST['add_media'])) {
            // Validate required fields
            if(empty($_POST['title']) || empty($_POST['year']) || empty($_POST['type']) || empty($_POST['poster'])) {
                $error_message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
            } else {
                $result = $mediaManager->addMedia($_POST);
                if($result['success']) {
                    $success_message = $result['message'];
                    // Redirect to prevent form resubmission
                    header('Location: ?page=admin_media&success=add');
                    exit;
                } else {
                    $error_message = $result['message'];
                }
            }
        } elseif(isset($_POST['update_media'])) {
            // Validate required fields
            if(empty($_POST['id']) || empty($_POST['title']) || empty($_POST['year']) || empty($_POST['type']) || empty($_POST['poster'])) {
                $error_message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
            } else {
                $result = $mediaManager->updateMedia($_POST['id'], $_POST);
                if($result['success']) {
                    $success_message = $result['message'];
                    // Redirect to prevent form resubmission
                    header('Location: ?page=admin_media&success=update');
                    exit;
                } else {
                    $error_message = $result['message'];
                }
            }
        } elseif(isset($_POST['delete_media'])) {
            if(empty($_POST['id'])) {
                $error_message = 'ไม่พบ ID ที่ต้องการลบ';
            } else {
                $result = $mediaManager->deleteMedia($_POST['id']);
                if($result['success']) {
                    $success_message = $result['message'];
                    // Redirect to prevent form resubmission
                    header('Location: ?page=admin_media&success=delete');
                    exit;
                } else {
                    $error_message = $result['message'];
                }
            }
        }
    } catch(Exception $e) {
        $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
    }
}

// Handle success messages from redirects
if(isset($_GET['success'])) {
    switch($_GET['success']) {
        case 'add':
            $success_message = 'เพิ่มข้อมูลสำเร็จ';
            break;
        case 'update':
            $success_message = 'อัปเดตข้อมูลสำเร็จ';
            break;
        case 'delete':
            $success_message = 'ลบข้อมูลสำเร็จ';
            break;
    }
}

// Get all media
$media_list = $mediaManager->getAllMedia();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">จัดการหนังและซีรีส์</h2>
            <p class="text-muted">เพิ่ม แก้ไข และลบข้อมูลหนังและซีรีส์ที่แสดงในหน้าแรก</p>
        </div>
    </div>
    
    <?php if($success_message): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if($error_message): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Add New Media Form -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>เพิ่มหนัง/ซีรีส์ใหม่
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="?page=admin_media" id="addMediaForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="title" class="form-label">ชื่อเรื่อง <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="year" class="form-label">ปี <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="year" name="year" min="1900" max="2030" value="2024" required>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="type" class="form-label">ประเภท <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">เลือกประเภท</option>
                                    <option value="movie">หนัง</option>
                                    <option value="series">ซีรีส์</option>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <label for="poster" class="form-label">URL รูปภาพ <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" id="poster" name="poster" 
                                       placeholder="https://image.tmdb.org/t/p/original/..." required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    แนะนำให้ใช้ภาพจาก TMDB ขนาด original สำหรับคุณภาพที่ดีที่สุด
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <label for="description" class="form-label">คำอธิบาย</label>
                                <textarea class="form-control" id="description" name="description" rows="3" 
                                          placeholder="คำอธิบายสั้นๆ เกี่ยวกับเรื่องนี้"></textarea>
                            </div>
                            
                            <div class="col-12">
                                <input type="hidden" name="add_media" value="เพิ่มข้อมูล">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>เพิ่มข้อมูล
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Media List -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>รายการหนังและซีรีส์ทั้งหมด
                    </h5>
                    <span class="badge bg-primary"><?php echo count($media_list); ?> รายการ</span>
                </div>
                <div class="card-body p-0">
                    <?php if(empty($media_list)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-film fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">ยังไม่มีข้อมูลหนังและซีรีส์</h5>
                        <p class="text-muted">เพิ่มข้อมูลใหม่โดยใช้ฟอร์มด้านบน</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th width="80">รูปภาพ</th>
                                    <th>ชื่อเรื่อง</th>
                                    <th width="80">ปี</th>
                                    <th width="100">ประเภท</th>
                                    <th width="120">สถานะ</th>
                                    <th width="150">การจัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($media_list as $media): ?>
                                <tr>
                                    <td>
                                        <img src="<?php echo htmlspecialchars($media['poster']); ?>" 
                                             class="img-thumbnail" 
                                             style="width: 60px; height: 90px; object-fit: cover;"
                                             alt="<?php echo htmlspecialchars($media['title']); ?>">
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($media['title']); ?></strong>
                                        </div>
                                        <?php if($media['description']): ?>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars(strlen($media['description']) > 100 ? substr($media['description'], 0, 100) . '...' : $media['description']); ?>
                                        </small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $media['year']; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $media['type'] == 'movie' ? 'primary' : 'info'; ?>">
                                            <?php echo $media['type'] == 'movie' ? 'หนัง' : 'ซีรีส์'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $media['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $media['status'] == 'active' ? 'แสดง' : 'ซ่อน'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary"
                                                    data-media-id="<?php echo $media['id']; ?>"
                                                    data-media-title="<?php echo htmlspecialchars($media['title']); ?>"
                                                    data-media-year="<?php echo $media['year']; ?>"
                                                    data-media-type="<?php echo $media['type']; ?>"
                                                    data-media-poster="<?php echo htmlspecialchars($media['poster']); ?>"
                                                    data-media-description="<?php echo htmlspecialchars($media['description']); ?>"
                                                    data-media-status="<?php echo $media['status']; ?>"
                                                    onclick="editMediaData(this)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteMedia(<?php echo $media['id']; ?>, '<?php echo htmlspecialchars($media['title'], ENT_QUOTES); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Media Modal -->
<div class="modal fade" id="editMediaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อมูลหนัง/ซีรีส์
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editMediaForm" action="?page=admin_media">
                <div class="modal-body">
                    <input type="hidden" name="id" id="edit_id">
                    <input type="hidden" name="update_media" value="1">
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="edit_title" class="form-label">ชื่อเรื่อง <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_title" name="title" required>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="edit_year" class="form-label">ปี <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit_year" name="year" min="1900" max="2030" required>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="edit_type" class="form-label">ประเภท <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_type" name="type" required>
                                <option value="movie">หนัง</option>
                                <option value="series">ซีรีส์</option>
                            </select>
                        </div>
                        
                        <div class="col-12">
                            <label for="edit_poster" class="form-label">URL รูปภาพ <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="edit_poster" name="poster" required>
                        </div>
                        
                        <div class="col-12">
                            <label for="edit_description" class="form-label">คำอธิบาย</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="edit_status" class="form-label">สถานะ</label>
                            <select class="form-select" id="edit_status" name="status">
                                <option value="active">แสดง</option>
                                <option value="inactive">ซ่อน</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>บันทึกการเปลี่ยนแปลง
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteMediaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>ยืนยันการลบ
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>คุณต้องการลบ <strong id="deleteMediaTitle"></strong> หรือไม่?</p>
                <p class="text-muted small">การดำเนินการนี้ไม่สามารถยกเลิกได้</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <form method="POST" action="?page=admin_media" style="display: inline;" id="deleteForm">
                    <input type="hidden" name="id" id="delete_id">
                    <input type="hidden" name="delete_media" value="1">
                    <button type="submit" class="btn btn-danger" id="deleteButton">
                        <i class="fas fa-trash me-2"></i>ลบ
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function editMediaData(button) {
    // Get data from button attributes
    const id = button.getAttribute('data-media-id');
    const title = button.getAttribute('data-media-title');
    const year = button.getAttribute('data-media-year');
    const type = button.getAttribute('data-media-type');
    const poster = button.getAttribute('data-media-poster');
    const description = button.getAttribute('data-media-description');
    const status = button.getAttribute('data-media-status');

    console.log('Edit Media Data:', {id, title, year, type, poster, description, status});

    // Set form values
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_title').value = title;
    document.getElementById('edit_year').value = year;
    document.getElementById('edit_type').value = type;
    document.getElementById('edit_poster').value = poster;
    document.getElementById('edit_description').value = description || '';
    document.getElementById('edit_status').value = status;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('editMediaModal'));
    modal.show();
}

function deleteMedia(id, title) {
    console.log('Delete Media Called:', {id, title});

    document.getElementById('delete_id').value = id;
    document.getElementById('deleteMediaTitle').textContent = title;

    new bootstrap.Modal(document.getElementById('deleteMediaModal')).show();
}

// Form submission handler
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin Media page loaded');

    // Handle add form
    const addForm = document.getElementById('addMediaForm');
    if(addForm) {
        console.log('Add form found');

        addForm.addEventListener('submit', function(e) {
            console.log('Add form submitted');

            // Get form data for debugging
            const formData = new FormData(addForm);
            const data = {};
            for(let [key, value] of formData.entries()) {
                data[key] = value;
            }
            console.log('Add form data being submitted:', data);

            // Validate required fields
            const requiredFields = ['title', 'year', 'type', 'poster'];
            let isValid = true;

            for(let field of requiredFields) {
                const element = document.querySelector(`#addMediaForm [name="${field}"]`);
                if(!element || !element.value.trim()) {
                    console.error(`Missing required field: ${field}`);
                    alert(`กรุณากรอก ${field}`);
                    isValid = false;
                    break;
                }
            }

            // Check if type is selected
            const typeElement = document.querySelector('#addMediaForm [name="type"]');
            if(typeElement && typeElement.value === '') {
                console.error('Type not selected');
                alert('กรุณาเลือกประเภท');
                isValid = false;
            }

            if(!isValid) {
                e.preventDefault();
                return false;
            }

            console.log('Add form validation passed');

            // Show loading state
            const submitButton = addForm.querySelector('[type="submit"]');
            if(submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังเพิ่มข้อมูล...';
            }
        });
    } else {
        console.error('Add form not found');
    }

    const editForm = document.getElementById('editMediaForm');
    if(editForm) {
        console.log('Edit form found');

        editForm.addEventListener('submit', function(e) {
            console.log('Edit form submitted');

            // Get form data for debugging
            const formData = new FormData(editForm);
            const data = {};
            for(let [key, value] of formData.entries()) {
                data[key] = value;
            }
            console.log('Form data being submitted:', data);

            // Check if update_media button exists in form data
            if(!data.update_media) {
                console.error('update_media field missing from form data');
                // Add it manually
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'update_media';
                hiddenInput.value = '1';
                editForm.appendChild(hiddenInput);
                console.log('Added update_media field manually');
            }

            // Validate required fields
            const requiredFields = ['id', 'title', 'year', 'type', 'poster'];
            let isValid = true;

            for(let field of requiredFields) {
                const element = document.querySelector(`#editMediaForm [name="${field}"]`);
                if(!element || !element.value.trim()) {
                    console.error(`Missing required field: ${field}`);
                    alert(`กรุณากรอก ${field}`);
                    isValid = false;
                    break;
                }
            }

            if(!isValid) {
                e.preventDefault();
                return false;
            }

            console.log('Form validation passed, submitting to:', editForm.action);
            console.log('Form method:', editForm.method);

            // Show loading state
            const submitButton = editForm.querySelector('[type="submit"]');
            if(submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังบันทึก...';
            }
        });
    } else {
        console.error('Edit form not found');
    }

    // Handle delete form
    const deleteForm = document.getElementById('deleteForm');
    if(deleteForm) {
        console.log('Delete form found');

        deleteForm.addEventListener('submit', function(e) {
            console.log('Delete form submitted');

            const formData = new FormData(deleteForm);
            const data = {};
            for(let [key, value] of formData.entries()) {
                data[key] = value;
            }
            console.log('Delete form data:', data);

            // Validate required fields
            if(!data.id) {
                console.error('Missing ID for delete');
                alert('ไม่พบ ID ที่ต้องการลบ');
                e.preventDefault();
                return false;
            }

            if(!data.delete_media) {
                console.error('Missing delete_media field');
                alert('ไม่พบฟิลด์ delete_media');
                e.preventDefault();
                return false;
            }

            console.log('Delete form validation passed');

            // Show loading state
            const deleteButton = document.getElementById('deleteButton');
            if(deleteButton) {
                deleteButton.disabled = true;
                deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังลบ...';
            }
        });
    } else {
        console.error('Delete form not found');
    }

    // Add success message auto-hide
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if(alert.classList.contains('show')) {
                alert.classList.remove('show');
                alert.classList.add('fade');
            }
        }, 5000);
    });
});
</script>
