<?php
/**
 * <PERSON><PERSON> Job สำหรับตรวจสอบและอนุมัติสลิปอัตโนมัติ
 * ควรรันทุก 5-10 นาที
 * 
 * Cron command: */5 * * * * /usr/bin/php /path/to/your/project/cron/slip_verification_cron.php
 */

// Prevent direct web access
if(isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line.');
}

require_once __DIR__ . '/../config/db_config.php';
require_once __DIR__ . '/../classes/SlipVerificationManager.php';

function writeLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    
    // Write to log file
    $logFile = __DIR__ . '/logs/slip_verification.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    // Also output to console
    echo $logMessage;
}

try {
    writeLog("=== Slip Verification Cron Job Started ===");
    
    $slipManager = new SlipVerificationManager();
    
    // ประมวลผลการอนุมัติอัตโนมัติ
    writeLog("Starting auto approval process...");
    $result = $slipManager->processAutoApproval();
    
    if ($result['success']) {
        writeLog("Auto approval completed successfully");
        writeLog("Processed: {$result['processed']} transactions");
        writeLog("Approved: {$result['approved']} topups");
        
        // แสดงรายละเอียดการอนุมัติ
        foreach ($result['results'] as $transactionResult) {
            if ($transactionResult['approved']) {
                writeLog("✓ Approved: Trans ID {$transactionResult['trans_id']}, " .
                        "Topup ID {$transactionResult['topup_id']}, " .
                        "User ID {$transactionResult['user_id']}, " .
                        "Amount {$transactionResult['amount']} THB");
            } else {
                writeLog("✗ Skipped: Trans ID {$transactionResult['trans_id']}, " .
                        "Reason: {$transactionResult['reason']}");
            }
        }
    } else {
        writeLog("Auto approval failed: " . $result['message']);
    }
    
    writeLog("=== Slip Verification Cron Job Completed ===");
    
} catch (Exception $e) {
    writeLog("ERROR: " . $e->getMessage());
    writeLog("=== Slip Verification Cron Job Failed ===");
    exit(1);
}

exit(0);
?>
