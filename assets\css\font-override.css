/* Font Override for Different Pages */

/* TH Sarabun Font Face Declarations */
@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

/* Default font for all pages including homepage (TH Sarabun) */
body {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.navbar-brand {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.nav-link {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.btn {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .card-title {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .card-text {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .form-label {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .form-control {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .table th,
body:not(.homepage) .table td {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .badge {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .alert {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .dropdown-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .modal-title,
body:not(.homepage) .modal-body {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .breadcrumb-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .page-link {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .list-group-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .progress-bar {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .tooltip-inner,
body:not(.homepage) .popover-body {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .lead {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) small,
body:not(.homepage) .small {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Homepage now uses TH Sarabun like other pages */
/* Removed MN Bak kut teh font override for homepage */

/* Special adjustments for TH Sarabun for all pages - Larger Fonts */
body {
    font-weight: 400;
    line-height: 1.6;
    font-size: 1.3em; /* เพิ่มขนาดตัวอักษรพื้นฐานให้ใหญ่ขึ้น */
}

h1 {
    font-weight: 600;
    font-size: 3.2rem; /* เพิ่มจาก 2.8rem */
}

h2 {
    font-weight: 600;
    font-size: 2.6rem; /* เพิ่มจาก 2.3rem */
}

h3 {
    font-weight: 600;
    font-size: 2.2rem; /* เพิ่มจาก 1.9rem */
}

h4 {
    font-weight: 600;
    font-size: 1.8rem; /* เพิ่มจาก 1.6rem */
}

h5 {
    font-weight: 600;
    font-size: 1.5rem; /* เพิ่มจาก 1.35rem */
}

h6 {
    font-weight: 600;
    font-size: 1.3rem; /* เพิ่มจาก 1.15rem */
}

.navbar-brand {
    font-weight: 700;
    font-size: 2.3rem; /* เพิ่มจาก 2rem */
}

.btn {
    font-weight: 500;
    font-size: 1.2em; /* เพิ่มจาก 1.05em */
    padding: 0.6rem 1.2rem; /* เพิ่ม padding */
}

.nav-link {
    font-size: 1.2em; /* เพิ่มจาก 1.05em */
}

.card-title {
    font-size: 1.5rem; /* เพิ่มจาก 1.3rem */
    font-weight: 600;
}

.card-text {
    font-size: 1.1rem; /* เพิ่มขนาด card text */
}

.form-label {
    font-size: 1.2em; /* เพิ่มจาก 1.05em */
    font-weight: 500;
}

.form-control {
    font-size: 1.15em; /* เพิ่มจาก 1.05em */
    padding: 0.6rem 0.9rem; /* เพิ่ม padding */
}

.table th,
.table td {
    font-size: 1.15em; /* เพิ่มจาก 1.05em */
    padding: 0.8rem; /* เพิ่ม padding */
}

.badge {
    font-size: 1em; /* เพิ่มจาก 0.9em */
    font-weight: 500;
    padding: 0.4rem 0.7rem; /* เพิ่ม padding */
}

.alert {
    font-size: 1.2em; /* เพิ่มจาก 1.05em */
    padding: 1.2rem; /* เพิ่ม padding */
}

.dropdown-item {
    font-size: 1.15em; /* เพิ่มจาก 1.02em */
    padding: 0.6rem 1.2rem; /* เพิ่ม padding */
}

.modal-title {
    font-size: 1.6rem; /* เพิ่มจาก 1.4rem */
    font-weight: 600;
}

.modal-body {
    font-size: 1.2em; /* เพิ่มจาก 1.05em */
}

body:not(.homepage) .breadcrumb-item {
    font-size: 1.02em; /* เพิ่มขนาด breadcrumb */
}

body:not(.homepage) .page-link {
    font-size: 1.02em; /* เพิ่มขนาด pagination */
}

body:not(.homepage) .list-group-item {
    font-size: 1.05em; /* เพิ่มขนาด list group */
}

.lead {
    font-size: 1.5rem; /* เพิ่มจาก 1.35rem */
    font-weight: 500;
    line-height: 1.5;
}

small, .small {
    font-size: 1em; /* เพิ่มจาก 0.9em */
}

/* ปรับขนาด display text */
body:not(.homepage) .display-1 {
    font-size: 5.5rem;
}

body:not(.homepage) .display-2 {
    font-size: 4.5rem;
}

body:not(.homepage) .display-3 {
    font-size: 3.5rem;
}

body:not(.homepage) .display-4 {
    font-size: 2.8rem;
}

body:not(.homepage) .display-5 {
    font-size: 2.3rem;
}

body:not(.homepage) .display-6 {
    font-size: 1.9rem;
}

/* Responsive adjustments - Larger fonts for all devices */
@media (max-width: 768px) {
    body {
        font-size: 1.2em; /* เพิ่มขนาดพื้นฐาน */
    }

    h1 {
        font-size: 2.5rem; /* เพิ่มขนาด */
    }

    h2 {
        font-size: 2.1rem; /* เพิ่มขนาด */
    }

    h3 {
        font-size: 1.8rem;
    }

    .navbar-brand {
        font-size: 1.9rem; /* เพิ่มขนาด */
    }

    .btn {
        font-size: 1.1em; /* เพิ่มขนาด */
        padding: 0.5rem 1rem;
    }

    .card-title {
        font-size: 1.3rem;
    }

    .form-control {
        font-size: 1.1em;
    }

    .table th, .table td {
        font-size: 1.1em;
    }
}

@media (max-width: 576px) {
    body {
        font-size: 1.15em; /* เพิ่มขนาดพื้นฐาน */
    }

    h1 {
        font-size: 2.2rem; /* เพิ่มขนาด */
    }

    h2 {
        font-size: 1.9rem; /* เพิ่มขนาด */
    }

    h3 {
        font-size: 1.6rem;
    }

    .navbar-brand {
        font-size: 1.7rem; /* เพิ่มขนาด */
    }

    .btn {
        font-size: 1.05em; /* เพิ่มขนาด */
        padding: 0.45rem 0.9rem;
    }

    .card-title {
        font-size: 1.2rem;
    }

    .form-control {
        font-size: 1.05em;
    }

    .table th, .table td {
        font-size: 1em;
        padding: 0.6rem 0.4rem;
    }
}
