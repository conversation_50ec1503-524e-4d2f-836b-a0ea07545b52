<?php
// Check if user is admin
if(!isset($_SESSION['user_id'])) {
    header('Location: index.php?page=login');
    exit;
}

$database = new Database();
$conn = $database->getConnection();

$query = "SELECT role FROM users WHERE id = :user_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$user || $user['role'] != 'admin') {
    header('Location: index.php');
    exit;
}

// Get all users with their media server accounts
$query = "SELECT u.id, u.username, u.full_name, u.email, u.status, u.created_at,
                 ea.emby_user_id, ea.emby_username, ea.emby_password, ea.status as emby_status,
                 ja.jellyfin_user_id, ja.jellyfin_username, ja.jellyfin_password, ja.status as jellyfin_status
          FROM users u
          LEFT JOIN emby_accounts ea ON u.id = ea.user_id
          LEFT JOIN jellyfin_accounts ja ON u.id = ja.user_id
          WHERE u.role = 'user'
          ORDER BY u.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle manual account creation
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_accounts'])) {
    $user_id = intval($_POST['user_id']);
    
    // Get user info
    $user_query = "SELECT * FROM users WHERE id = :user_id";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bindParam(':user_id', $user_id);
    $user_stmt->execute();
    $target_user = $user_stmt->fetch(PDO::FETCH_ASSOC);
    
    if($target_user) {
        require_once 'classes/UserManager.php';
        $userManager = new UserManager();
        
        // Create media server accounts manually
        $success_messages = [];
        $error_messages = [];
        
        // Check if accounts already exist
        $emby_exists = false;
        $jellyfin_exists = false;
        
        foreach($users as $user_data) {
            if($user_data['id'] == $user_id) {
                $emby_exists = !empty($user_data['emby_user_id']);
                $jellyfin_exists = !empty($user_data['jellyfin_user_id']);
                break;
            }
        }
        
        // Create Emby account if not exists
        if(!$emby_exists) {
            $emby_result = $userManager->createEmbyAccount($user_id, $target_user['username'], 'default123');
            if($emby_result['success']) {
                $success_messages[] = 'สร้างบัญชี Emby สำเร็จ';
            } else {
                $error_messages[] = 'ไม่สามารถสร้างบัญชี Emby ได้: ' . ($emby_result['message'] ?? 'Unknown error');
            }
        }
        
        // Create Jellyfin account if not exists
        if(!$jellyfin_exists) {
            $jellyfin_result = $userManager->createJellyfinAccount($user_id, $target_user['username'], 'default123');
            if($jellyfin_result['success']) {
                $success_messages[] = 'สร้างบัญชี Jellyfin สำเร็จ';
            } else {
                $error_messages[] = 'ไม่สามารถสร้างบัญชี Jellyfin ได้: ' . ($jellyfin_result['message'] ?? 'Unknown error');
            }
        }
        
        // Refresh data
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">จัดการบัญชี Media Server</h2>
            <p class="text-muted">ตรวจสอบและจัดการบัญชี Emby และ Jellyfin ของผู้ใช้</p>
        </div>
    </div>
    
    <?php if(isset($success_messages) && !empty($success_messages)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo implode('<br>', $success_messages); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if(isset($error_messages) && !empty($error_messages)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo implode('<br>', $error_messages); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>รายชื่อผู้ใช้และบัญชี Media Server
                    </h5>
                </div>
                <div class="card-body">
                    <?php if(empty($users)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">ไม่มีผู้ใช้ในระบบ</h5>
                        <p class="text-muted">ยังไม่มีผู้ใช้สมัครสมาชิก</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>ผู้ใช้</th>
                                    <th>Emby Account</th>
                                    <th>Jellyfin Account</th>
                                    <th>Password Sync</th>
                                    <th>สถานะ</th>
                                    <th>การจัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($users as $user_data): ?>
                                <tr>
                                    <td><?php echo $user_data['id']; ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($user_data['username']); ?></strong>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($user_data['full_name']); ?>
                                        </small>
                                        <?php if($user_data['email']): ?>
                                        <br><small class="text-muted">
                                            <?php echo htmlspecialchars($user_data['email']); ?>
                                        </small>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <!-- Emby Account -->
                                    <td>
                                        <?php if($user_data['emby_user_id']): ?>
                                        <div class="small">
                                            <div><strong>Username:</strong> <?php echo htmlspecialchars($user_data['emby_username']); ?></div>
                                            <div><strong>Password:</strong> <code><?php echo htmlspecialchars(substr($user_data['emby_password'], 0, 3) . '***'); ?></code></div>
                                            <div><strong>Status:</strong> 
                                                <span class="badge bg-<?php echo $user_data['emby_status'] == 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo $user_data['emby_status']; ?>
                                                </span>
                                            </div>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">ไม่มีบัญชี</span>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <!-- Jellyfin Account -->
                                    <td>
                                        <?php if($user_data['jellyfin_user_id']): ?>
                                        <div class="small">
                                            <div><strong>Username:</strong> <?php echo htmlspecialchars($user_data['jellyfin_username']); ?></div>
                                            <div><strong>Password:</strong> <code><?php echo htmlspecialchars(substr($user_data['jellyfin_password'], 0, 3) . '***'); ?></code></div>
                                            <div><strong>Status:</strong> 
                                                <span class="badge bg-<?php echo $user_data['jellyfin_status'] == 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo $user_data['jellyfin_status']; ?>
                                                </span>
                                            </div>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">ไม่มีบัญชี</span>
                                        <?php endif; ?>
                                    </td>
                                    
                                    <!-- Password Sync -->
                                    <td class="text-center">
                                        <?php
                                        $emby_has_password = !empty($user_data['emby_password']);
                                        $jellyfin_has_password = !empty($user_data['jellyfin_password']);
                                        $passwords_match = $user_data['emby_password'] === $user_data['jellyfin_password'];
                                        
                                        if($emby_has_password && $jellyfin_has_password) {
                                            if($passwords_match) {
                                                echo '<span class="badge bg-success">✅ Synced</span>';
                                            } else {
                                                echo '<span class="badge bg-danger">❌ Not Synced</span>';
                                            }
                                        } elseif($emby_has_password || $jellyfin_has_password) {
                                            echo '<span class="badge bg-warning">⚠️ Partial</span>';
                                        } else {
                                            echo '<span class="badge bg-secondary">➖ No Accounts</span>';
                                        }
                                        ?>
                                    </td>
                                    
                                    <!-- Status -->
                                    <td>
                                        <span class="badge bg-<?php echo $user_data['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $user_data['status']; ?>
                                        </span>
                                    </td>
                                    
                                    <!-- Actions -->
                                    <td>
                                        <?php if(!$user_data['emby_user_id'] || !$user_data['jellyfin_user_id']): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="user_id" value="<?php echo $user_data['id']; ?>">
                                            <button type="submit" name="create_accounts" class="btn btn-sm btn-primary" 
                                                    onclick="return confirm('สร้างบัญชี Media Server สำหรับผู้ใช้นี้?')">
                                                <i class="fas fa-plus me-1"></i>สร้างบัญชี
                                            </button>
                                        </form>
                                        <?php else: ?>
                                        <span class="text-success small">
                                            <i class="fas fa-check me-1"></i>ครบถ้วน
                                        </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">ผู้ใช้ทั้งหมด</h6>
                            <h4 class="mb-0"><?php echo count($users); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">บัญชี Emby</h6>
                            <h4 class="mb-0"><?php echo count(array_filter($users, function($u) { return !empty($u['emby_user_id']); })); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tv fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">บัญชี Jellyfin</h6>
                            <h4 class="mb-0"><?php echo count(array_filter($users, function($u) { return !empty($u['jellyfin_user_id']); })); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-play fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Password Synced</h6>
                            <h4 class="mb-0">
                                <?php 
                                echo count(array_filter($users, function($u) { 
                                    return !empty($u['emby_password']) && !empty($u['jellyfin_password']) && 
                                           $u['emby_password'] === $u['jellyfin_password']; 
                                })); 
                                ?>
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-sync fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
