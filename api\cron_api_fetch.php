<?php
/**
 * Cron Job Script for Fetching API Data from PayNoi
 * 
 * This script is designed to be run via cron job to periodically
 * fetch transaction data from the PayNoi API
 */

// Configuration
$config = [
    'api_key' => '6413172832bf53f8427c9271971365822c3a0579e9da214cc4f12f0667584446',
    'record_key' => '100568',
    'api_url' => 'https://paynoi.com/api_line',
    'log_file' => __DIR__ . '/logs/api_fetch.log',
    'data_file' => __DIR__ . '/data/transactions.json'
];

// Create directories if they don't exist
$directories = [
    dirname($config['log_file']),
    dirname($config['data_file'])
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

/**
 * Log message with timestamp
 */
function logMessage($message, $level = 'INFO') {
    global $config;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    
    // Write to log file
    file_put_contents($config['log_file'], $logEntry, FILE_APPEND | LOCK_EX);
    
    // Also output to console if running from command line
    if (php_sapi_name() === 'cli') {
        echo $logEntry;
    }
}

/**
 * Fetch data from PayNoi API
 */
function fetchApiData() {
    global $config;
    
    logMessage("Starting API fetch process");
    
    // Build API URL with parameters
    $url = $config['api_url'] . '?' . http_build_query([
        'api_key' => $config['api_key'],
        'record_key' => $config['record_key']
    ]);
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_USERAGENT => 'PayNoi-Cron-Fetcher/1.0',
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Check for cURL errors
    if ($response === false) {
        logMessage("cURL Error: $error", 'ERROR');
        return false;
    }
    
    // Check HTTP status code
    if ($httpCode !== 200) {
        logMessage("HTTP Error: Received status code $httpCode", 'ERROR');
        return false;
    }
    
    // Parse JSON response
    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        logMessage("JSON Parse Error: " . json_last_error_msg(), 'ERROR');
        return false;
    }
    
    // Check API response status
    if (!isset($data['status']) || $data['status'] !== 'success') {
        $status = $data['status'] ?? 'unknown';
        logMessage("API Error: Status is '$status'", 'ERROR');
        return false;
    }
    
    logMessage("API fetch successful. Processing " . count($data['data']) . " transactions");
    return $data;
}

/**
 * Process and save transaction data
 */
function processTransactionData($apiData) {
    global $config;

    $transactions = [];
    $totalTransactions = 0;

    foreach ($apiData['data'] as $transaction) {
        $transactions[] = [
            'trans_id' => $transaction['trans_id'] ?? null,
            'bankaccount' => $transaction['bankaccount'] ?? '',
            'amount' => $transaction['amount'] ?? 0,
            'currency' => $transaction['currency'] ?? '',
            'date' => $transaction['date'] ?? '',
            'balance' => $transaction['balance'] ?? 0,
            'type' => $transaction['type'] ?? '',
            'fetched_at' => date('Y-m-d H:i:s')
        ];
        $totalTransactions++;
    }
    
    // Save to JSON file
    $saveData = [
        'last_updated' => date('Y-m-d H:i:s'),
        'total_transactions' => $totalTransactions,
        'transactions' => $transactions
    ];
    
    $jsonData = json_encode($saveData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if (file_put_contents($config['data_file'], $jsonData, LOCK_EX) === false) {
        logMessage("Failed to save transaction data to file", 'ERROR');
        return false;
    }
    
    logMessage("Successfully processed and saved $totalTransactions transactions");
    return true;
}

/**
 * Main execution function
 */
function main() {
    logMessage("=== Cron Job Started ===");
    
    try {
        // Fetch data from API
        $apiData = fetchApiData();
        if ($apiData === false) {
            logMessage("Failed to fetch API data", 'ERROR');
            return 1;
        }
        
        // Process and save data
        if (!processTransactionData($apiData)) {
            logMessage("Failed to process transaction data", 'ERROR');
            return 1;
        }
        
        logMessage("=== Cron Job Completed Successfully ===");
        return 0;
        
    } catch (Exception $e) {
        logMessage("Unexpected error: " . $e->getMessage(), 'ERROR');
        return 1;
    }
}

// Execute main function if script is run directly
if (php_sapi_name() === 'cli' || !isset($_SERVER['HTTP_HOST'])) {
    exit(main());
}
?>
