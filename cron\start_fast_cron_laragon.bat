@echo off
REM Fast Cron Runner for Laragon - รันทุก 5 วินาที

echo ========================================
echo Fast Cron Job - Laragon PHP 8.3.16
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Set the PHP path (adjust this path according to your Laragon installation)
set PHP_PATH=C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe

REM Check if PHP exists
if not exist "%PHP_PATH%" (
    echo ERROR: PHP not found at %PHP_PATH%
    echo.
    echo Please check if the path is correct or run find_laragon_php.bat
    echo to find your actual PHP installation path.
    echo.
    pause
    exit /b 1
)

echo Using PHP: %PHP_PATH%
echo.

REM Test PHP
"%PHP_PATH%" -v >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: PHP test failed!
    echo Path: %PHP_PATH%
    pause
    exit /b 1
)

echo [OK] PHP test passed
"%PHP_PATH%" -v | findstr "PHP"
echo.

echo กำลังรันงาน:
echo - ดึงข้อมูล Transaction จาก API
echo - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
echo.
echo กด Ctrl+C เพื่อหยุด
echo ========================================

REM Create logs directory if not exists
if not exist "logs" mkdir logs

:loop
    REM Run fast cron
    "%PHP_PATH%" fast_cron.php
    
    REM Check if command failed
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ERROR: Fast cron failed with exit code %ERRORLEVEL%
        echo Check logs/fast_cron.log for details
        echo.
        pause
        goto end
    )
    
    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul
    
goto loop

:end
echo.
echo ========================================
echo Fast Cron Job - Stopped
echo Time: %date% %time%
echo ========================================
