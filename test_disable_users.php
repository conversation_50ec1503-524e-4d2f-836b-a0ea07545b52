<?php
// Session already started in index.php

// ตรวจสอบสิทธิ์แอดมิน
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ?page=login');
    exit();
}

require_once 'config/db_config.php';

// ทดสอบแบบง่าย โดยไม่ใช้ SubscriptionManager
echo "<h2>🔧 ตรวจสอบการตั้งค่า API</h2>";

// ตรวจสอบว่าไฟล์ API classes มีอยู่หรือไม่
$emby_file = 'classes/EmbyAPI.php';
$jellyfin_file = 'classes/JellyfinAPI.php';

echo "<p><strong>EmbyAPI.php:</strong> " . (file_exists($emby_file) ? '✅ มีอยู่' : '❌ ไม่มี') . "</p>";
echo "<p><strong>JellyfinAPI.php:</strong> " . (file_exists($jellyfin_file) ? '✅ มีอยู่' : '❌ ไม่มี') . "</p>";

// ทดสอบการสร้าง API instances
try {
    require_once $emby_file;
    require_once $jellyfin_file;

    // ใส่ URL และ API key ของคุณที่นี่
    $emby_url = 'http://localhost:8096';  // เปลี่ยนเป็น URL ของ Emby server
    $emby_key = 'your_emby_api_key';      // เปลี่ยนเป็น API key ของ Emby
    $jellyfin_url = 'http://localhost:8097';  // เปลี่ยนเป็น URL ของ Jellyfin server
    $jellyfin_key = 'your_jellyfin_api_key';  // เปลี่ยนเป็น API key ของ Jellyfin

    echo "<h3>🔑 การตั้งค่า API</h3>";
    echo "<p><strong>Emby URL:</strong> $emby_url</p>";
    echo "<p><strong>Emby Key:</strong> " . (strlen($emby_key) > 10 ? substr($emby_key, 0, 10) . '...' : $emby_key) . "</p>";
    echo "<p><strong>Jellyfin URL:</strong> $jellyfin_url</p>";
    echo "<p><strong>Jellyfin Key:</strong> " . (strlen($jellyfin_key) > 10 ? substr($jellyfin_key, 0, 10) . '...' : $jellyfin_key) . "</p>";

    if ($emby_key === 'your_emby_api_key' || $jellyfin_key === 'your_jellyfin_api_key') {
        echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0;'>";
        echo "⚠️ <strong>กรุณาแก้ไข API keys ในไฟล์นี้ก่อนทดสอบ</strong>";
        echo "<br>1. แก้ไข <code>\$emby_url</code> และ <code>\$emby_key</code>";
        echo "<br>2. แก้ไข <code>\$jellyfin_url</code> และ <code>\$jellyfin_key</code>";
        echo "</div>";
    }

} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาดในการโหลด API classes: " . $e->getMessage() . "</p>";
}

echo "<h1>🧪 ทดสอบการ Disable Users</h1>";

$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
}

$subscription_manager = new SubscriptionManager();

// ทดสอบการ disable users
if (isset($_POST['test_disable'])) {
    $user_id = intval($_POST['user_id']);
    
    echo "<h2>🔄 กำลังทดสอบการ disable users...</h2>";
    echo "<p><strong>User ID:</strong> $user_id</p>";
    
    $result = $subscription_manager->disableUsersForExpiredSubscription($user_id);
    
    echo "<h3>📊 ผลลัพธ์:</h3>";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    if ($result['success']) {
        echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;'>";
        echo "✅ Disable users สำเร็จ";
        echo "<br>Emby: " . ($result['emby'] ? 'สำเร็จ' : 'ล้มเหลว');
        echo "<br>Jellyfin: " . ($result['jellyfin'] ? 'สำเร็จ' : 'ล้มเหลว');
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>";
        echo "❌ Disable users ล้มเหลว: " . ($result['message'] ?? 'ไม่ทราบสาเหตุ');
        echo "</div>";
    }
}

// ทดสอบการ enable users
if (isset($_POST['test_enable'])) {
    $user_id = intval($_POST['user_id']);
    
    echo "<h2>🔄 กำลังทดสอบการ enable users...</h2>";
    echo "<p><strong>User ID:</strong> $user_id</p>";
    
    $result = $subscription_manager->enableUsersForRenewedSubscription($user_id);
    
    echo "<h3>📊 ผลลัพธ์:</h3>";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    if ($result['success']) {
        echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;'>";
        echo "✅ Enable users สำเร็จ";
        echo "<br>Emby: " . ($result['emby'] ? 'สำเร็จ' : 'ล้มเหลว');
        echo "<br>Jellyfin: " . ($result['jellyfin'] ? 'สำเร็จ' : 'ล้มเหลว');
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;'>";
        echo "❌ Enable users ล้มเหลว: " . ($result['message'] ?? 'ไม่ทราบสาเหตุ');
        echo "</div>";
    }
}

// ดึงข้อมูล users ที่มี media server accounts
try {
    $query = "SELECT DISTINCT u.id, u.username, u.full_name,
                     ea.emby_user_id, ja.jellyfin_user_id,
                     us.status as subscription_status
              FROM users u
              LEFT JOIN emby_accounts ea ON u.id = ea.user_id
              LEFT JOIN jellyfin_accounts ja ON u.id = ja.user_id
              LEFT JOIN user_subscriptions us ON u.id = us.user_id
              WHERE (ea.emby_user_id IS NOT NULL OR ja.jellyfin_user_id IS NOT NULL)
              ORDER BY u.id";
    
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "<p>❌ ไม่มี users ที่มี media server accounts</p>";
    } else {
        echo "<h2>👥 Users ที่มี Media Server Accounts</h2>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Full Name</th>";
        echo "<th style='padding: 8px;'>Emby ID</th>";
        echo "<th style='padding: 8px;'>Jellyfin ID</th>";
        echo "<th style='padding: 8px;'>Subscription</th>";
        echo "<th style='padding: 8px;'>Actions</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>" . ($user['full_name'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>" . ($user['emby_user_id'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>" . ($user['jellyfin_user_id'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>" . ($user['subscription_status'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>";
            
            // ปุ่ม Disable
            echo "<form method='POST' style='display: inline; margin-right: 5px;'>";
            echo "<input type='hidden' name='user_id' value='{$user['id']}'>";
            echo "<button type='submit' name='test_disable' style='background: #dc3545; color: white; padding: 5px 10px; border: none; border-radius: 4px; cursor: pointer;' onclick='return confirm(\"Disable users สำหรับ {$user['username']}?\")'>Disable</button>";
            echo "</form>";
            
            // ปุ่ม Enable
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='user_id' value='{$user['id']}'>";
            echo "<button type='submit' name='test_enable' style='background: #28a745; color: white; padding: 5px 10px; border: none; border-radius: 4px; cursor: pointer;' onclick='return confirm(\"Enable users สำหรับ {$user['username']}?\")'>Enable</button>";
            echo "</form>";
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ ข้อผิดพลาดในการดึงข้อมูล users: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🔗 ลิงก์</h3>";
echo "<ul>";
echo "<li><a href='?page=admin_subscriptions'>กลับหน้า Admin Subscriptions</a></li>";
echo "<li><a href='test_disable_users.php'>รีเฟรชหน้านี้</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>⚠️ ไฟล์นี้เป็นไฟล์ทดสอบ ให้ลบออกหลังใช้เสร็จ</small></p>";
?>
