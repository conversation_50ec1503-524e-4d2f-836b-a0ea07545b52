@echo off
REM Cron Job Menu - เลือกวิธีรัน

:menu
cls
echo ========================================
echo Media Server Cron Jobs - Menu
echo ========================================
echo.
echo Please choose how to run the cron jobs:
echo.
echo 1. Auto-detect PHP (try to find automatically)
echo 2. Use system PATH (php command)
echo 3. Use custom PHP path (enter manually)
echo 4. Find Laragon PHP (search for Laragon)
echo 5. Test PHP installations
echo.
echo 0. Exit
echo.
echo ========================================

set /p choice=Enter your choice (0-5): 

if "%choice%"=="1" goto auto_detect
if "%choice%"=="2" goto use_path
if "%choice%"=="3" goto custom_path
if "%choice%"=="4" goto find_laragon
if "%choice%"=="5" goto test_php
if "%choice%"=="0" goto exit
goto menu

:auto_detect
echo.
echo Starting auto-detect Fast Cron...
start_fast_cron.bat
goto menu

:use_path
echo.
echo Starting PATH-based Fast Cron...
start_fast_cron_path.bat
goto menu

:custom_path
echo.
echo Starting custom path Fast Cron...
start_fast_cron_custom.bat
goto menu

:find_laragon
echo.
echo Searching for Laragon PHP...
find_laragon_php.bat
pause
goto menu

:test_php
echo.
echo Testing PHP installations...
find_php.bat
pause
goto menu

:exit
echo.
echo Goodbye!
exit /b 0
