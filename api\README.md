# PayNoi API Cron Job System

ระบบดึงข้อมูลจาก PayNoi API อัตโนมัติด้วย Cron Job

## ไฟล์ในระบบ

### ไฟล์หลัก
- `cron_api_fetch.php` - สคริปต์หลักสำหรับดึงข้อมูลจาก API (รันครั้งเดียว)
- `continuous_cron.php` - สคริปต์สำหรับรันต่อเนื่องทุกๆ 5 วินาที
- `view_data.php` - หน้าเว็บสำหรับดูข้อมูลธุรกรรม
- `view_logs.php` - หน้าเว็บสำหรับดู Log การทำงาน (cron ปกติ)
- `view_continuous_logs.php` - หน้าเว็บสำหรับดู Log การทำงาน (continuous cron)

### ไฟล์สำหรับ Windows
- `run_cron.bat` - รัน cron ครั้งเดียว
- `run_continuous_cron.bat` - รัน continuous cron (ทุก 5 วินาที)
- `setup_windows_scheduler.bat` - ตั้งค่า Windows Task Scheduler

### ไฟล์สำหรับ Linux/macOS
- `setup_cron.sh` - สคริปต์ช่วยตั้งค่า Cron Job
- `run_continuous_cron.sh` - รัน continuous cron (ทุก 5 วินาที)

### ไฟล์เสริม
- `debug_api.php` - ไฟล์สำหรับ debug API

### ไฟล์เดิม
- `api.php` - สคริปต์เดิมสำหรับทดสอบ API

## การติดตั้งและใช้งาน

### 1. ทดสอบการทำงาน

#### สำหรับ Windows (Laragon):
```batch
# รัน cron ครั้งเดียว
run_cron.bat

# รัน continuous cron (ทุก 5 วินาที)
run_continuous_cron.bat

# หรือรันโดยตรง
C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe cron_api_fetch.php
C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe continuous_cron.php
```

#### สำหรับ Linux/macOS:
```bash
# รัน cron ครั้งเดียว
php cron_api_fetch.php

# รัน continuous cron (ทุก 5 วินาที)
chmod +x run_continuous_cron.sh
./run_continuous_cron.sh

# หรือใช้สคริปต์ setup
chmod +x setup_cron.sh
./setup_cron.sh
```

### 2. ตั้งค่า Scheduled Task

#### สำหรับ Windows:
```batch
# ใช้ batch file สำหรับตั้งค่า Windows Task Scheduler
setup_windows_scheduler.bat
```

หรือตั้งค่าด้วยตนเอง:
1. เปิด Task Scheduler (taskschd.msc)
2. สร้าง Basic Task
3. ตั้งค่า Trigger ตามต้องการ
4. ตั้งค่า Action เป็น Start a program: `C:\path\to\your\project\run_cron.bat`

#### สำหรับ Linux/macOS (Cron Job):

เปิด crontab editor:
```bash
crontab -e
```

เพิ่มบรรทัดใดบรรทัดหนึ่งต่อไปนี้:

```bash
# ทุก 5 นาที
*/5 * * * * /usr/bin/php /path/to/your/project/cron_api_fetch.php

# ทุก 15 นาที
*/15 * * * * /usr/bin/php /path/to/your/project/cron_api_fetch.php

# ทุก 30 นาที
*/30 * * * * /usr/bin/php /path/to/your/project/cron_api_fetch.php

# ทุกชั่วโมง
0 * * * * /usr/bin/php /path/to/your/project/cron_api_fetch.php

# ทุกวันเวลา 02:00
0 2 * * * /usr/bin/php /path/to/your/project/cron_api_fetch.php
```

### 3. รัน Continuous Cron (ทุก 5 วินาที)

สำหรับการดึงข้อมูลบ่อยมาก (ทุก 5 วินาที) ใช้ continuous cron แทน:

#### Windows:
```batch
run_continuous_cron.bat
```

#### Linux/macOS:
```bash
chmod +x run_continuous_cron.sh
./run_continuous_cron.sh
```

**หมายเหตุ:** Continuous cron จะรันต่อเนื่องจนกว่าจะหยุดด้วย Ctrl+C

### 4. ดูผลลัพธ์

- เปิดเว็บเบราว์เซอร์ไปที่ `http://your-domain/view_data.php`
- ดู Regular Cron Log ได้ที่ `http://your-domain/view_logs.php`
- ดู Continuous Cron Log ได้ที่ `http://your-domain/view_continuous_logs.php`

## โครงสร้างไฟล์ที่สร้างขึ้น

```
project/
├── cron_api_fetch.php
├── view_data.php
├── view_logs.php
├── setup_cron.sh
├── api.php (เดิม)
├── data/
│   └── transactions.json
└── logs/
    └── api_fetch.log
```

## คุณสมบัติ

### cron_api_fetch.php
- ดึงข้อมูลจาก PayNoi API (รันครั้งเดียว)
- บันทึก Log การทำงาน
- จัดเก็บข้อมูลในรูปแบบ JSON
- จัดการ Error และ Exception
- รองรับการรันผ่าน Command Line และ Web

### continuous_cron.php
- ดึงข้อมูลจาก PayNoi API อย่างต่อเนื่องทุกๆ 5 วินาที
- รันได้สูงสุด 24 ชั่วโมง (ปรับได้)
- ตรวจสอบการใช้ memory และจัดการ graceful shutdown
- บันทึก Log แยกต่างหาก (continuous_cron.log)
- แสดงสถิติการทำงานทุก 60 cycles (5 นาที)
- รองรับ signal handling สำหรับการหยุดอย่างปลอดภัย

### view_data.php
- แสดงข้อมูลธุรกรรมในรูปแบบตาราง
- แสดงสถิติข้อมูล
- รีเฟรชข้อมูลได้
- UI ที่สวยงามและใช้งานง่าย

### view_logs.php
- แสดง Log การทำงาน (regular cron)
- แยกประเภท Log (INFO, WARNING, ERROR)
- แสดงสถิติ Log
- ล้าง Log ได้

### view_continuous_logs.php
- แสดง Log การทำงาน (continuous cron)
- แสดงสถานะการทำงาน (Running/Stopped)
- Auto refresh ทุก 10 วินาที
- แสดงเฉพาะ 200 บรรทัดล่าสุด
- ตรวจสอบว่า continuous cron ยังทำงานอยู่หรือไม่

## การกำหนดค่า

แก้ไขค่าต่างๆ ในไฟล์ `cron_api_fetch.php`:

```php
$config = [
    'api_key' => 'your_api_key_here',
    'record_key' => 'your_record_key_here',
    'api_url' => 'https://paynoi.com/api_line',
    'log_file' => __DIR__ . '/logs/api_fetch.log',
    'data_file' => __DIR__ . '/data/transactions.json'
];
```

## การตรวจสอบ Cron Job

```bash
# ดู Cron Job ที่ตั้งไว้
crontab -l

# ดู Log ของ Cron (บน Ubuntu/Debian)
sudo tail -f /var/log/cron.log

# ดู Log ของ Cron (บน CentOS/RHEL)
sudo tail -f /var/log/cron
```

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **Permission denied**
   ```bash
   chmod +x setup_cron.sh
   chmod 755 cron_api_fetch.php
   ```

2. **PHP path ไม่ถูกต้อง**
   ```bash
   which php  # หา path ของ PHP
   ```

3. **Directory ไม่สามารถสร้างได้**
   ```bash
   chmod 755 .
   ```

### การตรวจสอบ Log

- ดู Log ในไฟล์: `logs/api_fetch.log`
- ดู Log ผ่านเว็บ: `view_logs.php`
- ตรวจสอบ System Log ของ Cron

## ความปลอดภัย

- API Key และ Record Key ถูกเก็บในไฟล์ PHP (ไม่สามารถเข้าถึงจากเว็บได้โดยตรง)
- ข้อมูลถูกเก็บในโฟลเดอร์ `data/` และ `logs/`
- ควรตั้งค่า Permission ของไฟล์ให้เหมาะสม

## การสำรองข้อมูล

แนะนำให้สำรองข้อมูลในโฟลเดอร์:
- `data/transactions.json` - ข้อมูลธุรกรรม
- `logs/api_fetch.log` - Log การทำงาน
