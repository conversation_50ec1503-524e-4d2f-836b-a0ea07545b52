-- Create processed_transactions table for slip verification
CREATE TABLE IF NOT EXISTS processed_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trans_id VARCHAR(255) NOT NULL UNIQUE,
    topup_id INT,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_trans_id (trans_id),
    INDEX idx_topup_id (topup_id),
    INDEX idx_processed_at (processed_at),
    
    FOREIGN KEY (topup_id) REFERENCES topups(id) ON DELETE SET NULL
);

-- Add slip_data column to topups table (safe method)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'topups'
     AND column_name = 'slip_data'
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column slip_data already exists"',
    'ALTER TABLE topups ADD COLUMN slip_data TEXT COMMENT "JSON data from slip verification API"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add approved_at column to topups table (safe method)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'topups'
     AND column_name = 'approved_at'
     AND table_schema = DATABASE()) > 0,
    'SELECT "Column approved_at already exists"',
    'ALTER TABLE topups ADD COLUMN approved_at TIMESTAMP NULL COMMENT "When the topup was approved"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
