#!/bin/bash
# Main Cron Job Runner for Linux
# รันงานหลักทั้งหมด: ดึง transaction, ตรวจสลิป, เช็ค subscription

echo "========================================"
echo "Main Cron Job - Starting"
echo "Time: $(date)"
echo "========================================"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Run the main cron script
php main_cron.php
EXIT_CODE=$?

# Check exit code
if [ $EXIT_CODE -eq 0 ]; then
    echo ""
    echo "========================================"
    echo "Main Cron Job - COMPLETED SUCCESSFULLY"
    echo "Time: $(date)"
    echo "========================================"
else
    echo ""
    echo "========================================"
    echo "Main Cron Job - COMPLETED WITH ERRORS"
    echo "Exit Code: $EXIT_CODE"
    echo "Time: $(date)"
    echo "========================================"
fi

exit $EXIT_CODE
