<?php
require_once 'classes/PaymentManager.php';

// Handle package purchase
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['package_id'])) {
    $package_id = $_POST['package_id'] ?? 0;
    $_SESSION['purchase_debug'][] = 'Processing package purchase...';

    if($package_id) {
        // Check if this is a trial package
        $database = new Database();
        $conn = $database->getConnection();

        $package_query = "SELECT * FROM packages WHERE id = :package_id";
        $package_stmt = $conn->prepare($package_query);
        $package_stmt->bindParam(':package_id', $package_id);
        $package_stmt->execute();
        $selected_package = $package_stmt->fetch(PDO::FETCH_ASSOC);

        // Check if user has already used trial
        $is_trial = ($selected_package && $selected_package['duration_days'] == 1 && $selected_package['price'] == 0);

        if($is_trial && $user['trial_used']) {
            $error_message = 'คุณได้ใช้แพ็คเกจทดลองไปแล้ว ไม่สามารถซื้อซ้ำได้';
        } else {
            $paymentManager = new PaymentManager();

            if($is_trial) {
                // Mark trial as used
                $trial_query = "UPDATE users SET trial_used = 1 WHERE id = :user_id";
                $trial_stmt = $conn->prepare($trial_query);
                $trial_stmt->bindParam(':user_id', $user['id']);
                $trial_stmt->execute();
            }

            $result = $paymentManager->purchasePackage($user['id'], $package_id);

            if($result['success']) {
                $success_message = $result['message'];

                // Refresh user balance
                $query = "SELECT balance FROM users WHERE id = :user_id";
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':user_id', $user['id']);
                $stmt->execute();
                $user_data = $stmt->fetch(PDO::FETCH_ASSOC);
                $user['balance'] = $user_data['balance'];
            } else {
                $error_message = $result['message'];
            }
        }
    }
}

// Get all packages
$database = new Database();
$conn = $database->getConnection();

$query = "SELECT * FROM packages WHERE status = 'active' ORDER BY price ASC";
$stmt = $conn->prepare($query);
$stmt->execute();
$packages = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get user's current subscription
require_once 'classes/SubscriptionManager.php';
$subscriptionManager = new SubscriptionManager();
$active_subscription = $subscriptionManager->getUserActiveSubscription($user['id']);
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">แพ็คเกจบริการ</h2>
            <p class="text-muted">เลือกแพ็คเกจที่เหมาะสมกับความต้องการของคุณ</p>
        </div>
    </div>
    
    <!-- User Balance -->
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-wallet me-2"></i>
                        <strong>ยอดเงินคงเหลือ: <?php
                            $balance = floatval($user['balance']);
                            echo number_format($balance, 0, '.', ',') . ' ฿';
                        ?></strong>
                    </div>
                    <a href="?page=topup" class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-1"></i>เติมเงิน
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Current Subscription Alert -->
    <?php if($active_subscription): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success">
                <h6 class="alert-heading">
                    <i class="fas fa-check-circle me-2"></i>แพ็คเกจปัจจุบัน
                </h6>
                <p class="mb-2">
                    คุณกำลังใช้แพ็คเกจ <strong><?php echo htmlspecialchars($active_subscription['package_name']); ?></strong>
                    หมดอายุวันที่ <strong><?php echo date('d/m/Y', strtotime($active_subscription['end_date'])); ?></strong>
                </p>
                <p class="mb-0">
                    <small class="text-muted">
                        หากซื้อแพ็คเกจใหม่ ระยะเวลาจะถูกเพิ่มต่อจากวันหมดอายุปัจจุบัน
                    </small>
                </p>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Messages -->
    <?php if(isset($success_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if(isset($error_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>


    
    <!-- Packages Grid -->
    <div class="row g-4">
        <?php foreach($packages as $index => $package): ?>
        <?php
        $is_trial = ($package['duration_days'] == 1 && $package['price'] == 0);
        $trial_used = $user['trial_used'] ?? false;
        ?>
        <div class="col-md-6 col-lg-4" data-animate="fade-in" style="animation-delay: <?php echo $index * 0.1; ?>s;">
            <div class="card h-100 <?php echo $is_trial ? 'border-success' : ($index == 1 ? 'border-primary' : ''); ?> position-relative card-hover">
                <?php if($is_trial): ?>
                <div class="position-absolute top-0 start-50 translate-middle">
                    <span class="badge bg-success pulse">ทดลองฟรี</span>
                </div>
                <?php elseif($index == 1): ?>
                <div class="position-absolute top-0 start-50 translate-middle">
                    <span class="badge bg-primary pulse">แนะนำ</span>
                </div>
                <?php endif; ?>
                
                <div class="card-body text-center p-4">
                    <h5 class="card-title text-primary"><?php echo htmlspecialchars($package['name']); ?></h5>
                    
                    <div class="mb-3">
                        <span class="h2 text-dark"><?php echo number_format($package['price'], 0); ?></span>
                        <span class="text-muted">฿</span>
                    </div>
                    
                    <p class="card-text text-muted"><?php echo htmlspecialchars($package['description']); ?></p>
                    
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2">
                            <i class="fas fa-calendar text-success me-2"></i>
                            <strong><?php echo $package['duration_days']; ?></strong> วัน
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-devices text-success me-2"></i>
                            <strong><?php echo $package['max_devices']; ?></strong> อุปกรณ์
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-video text-success me-2"></i>
                            คุณภาพ <strong><?php echo $package['quality_limit']; ?></strong>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-server text-success me-2"></i>
                            Emby + Jellyfin
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-headset text-success me-2"></i>
                            รองรับ 24/7
                        </li>
                    </ul>
                    
                    <?php if($is_trial && $trial_used): ?>
                    <button class="btn btn-outline-secondary btn-lg w-100" disabled>
                        <i class="fas fa-times me-2"></i>ใช้แล้ว
                    </button>
                    <div class="mt-2">
                        <small class="text-muted">
                            คุณได้ใช้แพ็คเกจทดลองไปแล้ว
                        </small>
                    </div>
                    <?php elseif($user['balance'] >= $package['price']): ?>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="package_id" value="<?php echo $package['id']; ?>">
                        <button type="submit" name="purchase_package" class="btn <?php echo $is_trial ? 'btn-success' : 'btn-primary'; ?> btn-lg w-100"
                                onclick="return confirm('คุณต้องการ<?php echo $is_trial ? 'ทดลองใช้' : 'ซื้อ'; ?>แพ็คเกจ <?php echo htmlspecialchars($package['name']); ?> <?php echo $is_trial ? 'ฟรี' : 'ในราคา ' . number_format($package['price'], 2) . ' ฿'; ?> หรือไม่?')">
                            <i class="fas fa-<?php echo $is_trial ? 'gift' : 'shopping-cart'; ?> me-2"></i><?php echo $is_trial ? 'ทดลองฟรี' : 'ซื้อแพ็คเกจ'; ?>
                        </button>
                    </form>
                    <?php else: ?>
                    <button class="btn btn-outline-secondary btn-lg w-100" disabled>
                        <i class="fas fa-wallet me-2"></i>ยอดเงินไม่เพียงพอ
                    </button>
                    <div class="mt-2">
                        <small class="text-muted">
                            ต้องเติมเงินอีก <?php echo number_format($package['price'] - $user['balance'], 2); ?> ฿
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="card-footer bg-light text-center">
                    <small class="text-muted">
                        <i class="fas fa-calculator me-1"></i>
                        <?php echo number_format($package['price'] / $package['duration_days'], 2); ?> ฿/วัน
                    </small>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Package Features Comparison -->
    <div class="row mt-5">
        <div class="col">
            <h3 class="fw-bold text-center mb-4">เปรียบเทียบแพ็คเกจ</h3>
            
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="table-dark">
                        <tr>
                            <th>คุณสมบัติ</th>
                            <?php foreach($packages as $package): ?>
                            <th class="text-center"><?php echo htmlspecialchars($package['name']); ?></th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>ราคา</strong></td>
                            <?php foreach($packages as $package): ?>
                            <td class="text-center"><?php echo number_format($package['price'], 0); ?> ฿</td>
                            <?php endforeach; ?>
                        </tr>
                        <tr>
                            <td><strong>ระยะเวลา</strong></td>
                            <?php foreach($packages as $package): ?>
                            <td class="text-center"><?php echo $package['duration_days']; ?> วัน</td>
                            <?php endforeach; ?>
                        </tr>
                        <tr>
                            <td><strong>จำนวนอุปกรณ์</strong></td>
                            <?php foreach($packages as $package): ?>
                            <td class="text-center"><?php echo $package['max_devices']; ?> เครื่อง</td>
                            <?php endforeach; ?>
                        </tr>
                        <tr>
                            <td><strong>คุณภาพวิดีโอ</strong></td>
                            <?php foreach($packages as $package): ?>
                            <td class="text-center"><?php echo $package['quality_limit']; ?></td>
                            <?php endforeach; ?>
                        </tr>
                        <tr>
                            <td><strong>Emby Server</strong></td>
                            <?php foreach($packages as $package): ?>
                            <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            <?php endforeach; ?>
                        </tr>
                        <tr>
                            <td><strong>Jellyfin Server</strong></td>
                            <?php foreach($packages as $package): ?>
                            <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            <?php endforeach; ?>
                        </tr>
                        <tr>
                            <td><strong>การสนับสนุน</strong></td>
                            <?php foreach($packages as $package): ?>
                            <td class="text-center">24/7</td>
                            <?php endforeach; ?>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- FAQ Section -->
    <div class="row mt-5">
        <div class="col">
            <h3 class="fw-bold text-center mb-4">คำถามที่พบบ่อย</h3>
            
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq0">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse0">
                            แพ็คเกจทดลอง 1 วัน คืออะไร?
                        </button>
                    </h2>
                    <div id="collapse0" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            แพ็คเกจทดลอง 1 วัน เป็นแพ็คเกจฟรีสำหรับผู้ใช้ใหม่ที่ต้องการทดลองใช้บริการก่อนตัดสินใจซื้อแพ็คเกจจริง
                            <strong>สามารถใช้ได้เพียงครั้งเดียวต่อบัญชี</strong> และจะหมดอายุภายใน 24 ชั่วโมง
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq1">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                            หากซื้อแพ็คเกจใหม่ขณะที่ยังมีแพ็คเกจเก่าอยู่จะเป็นอย่างไร?
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            ระยะเวลาของแพ็คเกจใหม่จะถูกเพิ่มต่อจากวันหมดอายุของแพ็คเกจปัจจุบัน ไม่มีการสูญเสียเวลาที่เหลืออยู่
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq2">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                            สามารถใช้งานทั้ง Emby และ Jellyfin พร้อมกันได้หรือไม่?
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            ได้ครับ เมื่อซื้อแพ็คเกจแล้ว คุณจะได้รับบัญชีสำหรับทั้ง Emby และ Jellyfin พร้อมใช้งาน
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq3">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                            หากแพ็คเกจหมดอายุจะเกิดอะไรขึ้น?
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            เมื่อแพ็คเกจหมดอายุ ระบบจะเปลี่ยนรหัสผ่านของบัญชี Emby และ Jellyfin อัตโนมัติ เพื่อป้องกันการใช้งานโดยไม่ได้รับอนุญาต
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
