/* Font Override for Different Pages */

/* TH Sarabun Font Face Declarations */
@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'TH Sarabun';
    src: url('../../fonts/THSarabun BoldItalic.ttf') format('truetype');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

/* Default font for all pages including homepage (TH Sarabun) */
body {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.navbar-brand {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.nav-link {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.btn {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .card-title {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .card-text {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .form-label {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .form-control {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .table th,
body:not(.homepage) .table td {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .badge {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .alert {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .dropdown-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .modal-title,
body:not(.homepage) .modal-body {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .breadcrumb-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .page-link {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .list-group-item {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .progress-bar {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .tooltip-inner,
body:not(.homepage) .popover-body {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) .lead {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

body:not(.homepage) small,
body:not(.homepage) .small {
    font-family: 'TH Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Homepage now uses TH Sarabun like other pages */
/* Removed MN Bak kut teh font override for homepage */

/* Special adjustments for TH Sarabun for all pages */
body {
    font-weight: 400;
    line-height: 1.6;
    font-size: 1.1em; /* เพิ่มขนาดตัวอักษรพื้นฐาน */
}

h1 {
    font-weight: 600;
    font-size: 2.8rem;
}

h2 {
    font-weight: 600;
    font-size: 2.3rem;
}

h3 {
    font-weight: 600;
    font-size: 1.9rem;
}

h4 {
    font-weight: 600;
    font-size: 1.6rem;
}

h5 {
    font-weight: 600;
    font-size: 1.35rem;
}

h6 {
    font-weight: 600;
    font-size: 1.15rem;
}

.navbar-brand {
    font-weight: 700;
    font-size: 2rem;
}

.btn {
    font-weight: 500;
    font-size: 1.05em;
}

.nav-link {
    font-size: 1.05em;
}

body:not(.homepage) .card-title {
    font-size: 1.3rem; /* เพิ่มขนาด card title */
}

body:not(.homepage) .form-label {
    font-size: 1.05em; /* เพิ่มขนาด form label */
}

body:not(.homepage) .form-control {
    font-size: 1.05em; /* เพิ่มขนาด form input */
}

body:not(.homepage) .table th,
body:not(.homepage) .table td {
    font-size: 1.05em; /* เพิ่มขนาดตาราง */
}

body:not(.homepage) .badge {
    font-size: 0.9em; /* ปรับขนาด badge */
}

body:not(.homepage) .alert {
    font-size: 1.05em; /* เพิ่มขนาด alert */
}

body:not(.homepage) .dropdown-item {
    font-size: 1.02em; /* เพิ่มขนาด dropdown */
}

body:not(.homepage) .modal-title {
    font-size: 1.4rem; /* เพิ่มขนาด modal title */
}

body:not(.homepage) .modal-body {
    font-size: 1.05em; /* เพิ่มขนาด modal body */
}

body:not(.homepage) .breadcrumb-item {
    font-size: 1.02em; /* เพิ่มขนาด breadcrumb */
}

body:not(.homepage) .page-link {
    font-size: 1.02em; /* เพิ่มขนาด pagination */
}

body:not(.homepage) .list-group-item {
    font-size: 1.05em; /* เพิ่มขนาด list group */
}

body:not(.homepage) .lead {
    font-size: 1.35rem; /* เพิ่มขนาด lead text */
}

body:not(.homepage) small,
body:not(.homepage) .small {
    font-size: 0.9em; /* ปรับขนาด small text */
}

/* ปรับขนาด display text */
body:not(.homepage) .display-1 {
    font-size: 5.5rem;
}

body:not(.homepage) .display-2 {
    font-size: 4.5rem;
}

body:not(.homepage) .display-3 {
    font-size: 3.5rem;
}

body:not(.homepage) .display-4 {
    font-size: 2.8rem;
}

body:not(.homepage) .display-5 {
    font-size: 2.3rem;
}

body:not(.homepage) .display-6 {
    font-size: 1.9rem;
}

/* Responsive adjustments for TH Sarabun to match MN Bak kut teh */
@media (max-width: 768px) {
    body:not(.homepage) {
        font-size: 1.05em; /* รักษาขนาดพื้นฐาน */
    }

    body:not(.homepage) h1 {
        font-size: 2.2rem; /* เท่ากับ MN Bak kut teh responsive */
    }

    body:not(.homepage) h2 {
        font-size: 1.9rem;
    }

    body:not(.homepage) .navbar-brand {
        font-size: 1.7rem; /* เพิ่มขนาดให้เท่า MN Bak kut teh */
    }

    body:not(.homepage) .btn {
        font-size: 1.02em;
    }
}

@media (max-width: 576px) {
    body:not(.homepage) {
        font-size: 1.02em;
    }

    body:not(.homepage) h1 {
        font-size: 2rem; /* เท่ากับ MN Bak kut teh responsive */
    }

    body:not(.homepage) h2 {
        font-size: 1.7rem;
    }

    body:not(.homepage) .navbar-brand {
        font-size: 1.5rem; /* เพิ่มขนาดให้เท่า MN Bak kut teh */
    }

    body:not(.homepage) .btn {
        font-size: 1em;
    }
}
