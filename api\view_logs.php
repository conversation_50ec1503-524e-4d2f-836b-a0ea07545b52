<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayNoi API Logs</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .log-container {
            max-height: 600px;
            overflow-y: auto;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        .log-line {
            padding: 5px 15px;
            border-bottom: 1px solid #e9ecef;
            white-space: pre-wrap;
        }
        .log-line:hover {
            background: #e9ecef;
        }
        .log-info {
            color: #0066cc;
        }
        .log-error {
            color: #dc3545;
            background: #f8d7da;
        }
        .log-warning {
            color: #856404;
            background: #fff3cd;
        }
        .log-timestamp {
            color: #666;
            font-weight: bold;
        }
        .no-logs {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PayNoi API Logs</h1>
            <p>ประวัติการทำงานของ Cron Job</p>
        </div>

        <?php
        $logFile = __DIR__ . '/logs/api_fetch.log';
        
        // Handle clear logs request
        if (isset($_GET['clear']) && $_GET['clear'] == '1') {
            if (file_exists($logFile)) {
                file_put_contents($logFile, '');
                echo '<div style="background: #d4edda; color: #155724; padding: 15px; margin: 20px; border-radius: 4px;">
                        Log ถูกล้างเรียบร้อยแล้ว
                      </div>';
            }
        }
        
        // Check if log file exists
        if (!file_exists($logFile)) {
            echo '<div class="no-logs">
                    <h3>ไม่พบไฟล์ Log</h3>
                    <p>ยังไม่มีการรัน Cron Job หรือไฟล์ Log ยังไม่ถูกสร้าง</p>
                  </div>';
            exit;
        }
        
        // Read log file
        $logContent = file_get_contents($logFile);
        if (empty($logContent)) {
            echo '<div class="no-logs">
                    <h3>ไฟล์ Log ว่างเปล่า</h3>
                    <p>ยังไม่มีข้อมูล Log</p>
                  </div>';
            exit;
        }
        
        // Parse log lines
        $logLines = array_filter(explode("\n", $logContent));
        $logLines = array_reverse($logLines); // Show newest first
        
        // Count log levels
        $infoCount = 0;
        $errorCount = 0;
        $warningCount = 0;
        $totalLines = count($logLines);
        
        foreach ($logLines as $line) {
            if (strpos($line, '[ERROR]') !== false) {
                $errorCount++;
            } elseif (strpos($line, '[WARNING]') !== false) {
                $warningCount++;
            } elseif (strpos($line, '[INFO]') !== false) {
                $infoCount++;
            }
        }
        
        // Get file info
        $fileSize = filesize($logFile);
        $lastModified = date('Y-m-d H:i:s', filemtime($logFile));
        ?>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($totalLines); ?></div>
                <div class="stat-label">บรรทัดทั้งหมด</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" style="color: #0066cc;"><?php echo number_format($infoCount); ?></div>
                <div class="stat-label">INFO</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" style="color: #856404;"><?php echo number_format($warningCount); ?></div>
                <div class="stat-label">WARNING</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" style="color: #dc3545;"><?php echo number_format($errorCount); ?></div>
                <div class="stat-label">ERROR</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($fileSize / 1024, 1); ?> KB</div>
                <div class="stat-label">ขนาดไฟล์</div>
            </div>
        </div>

        <div class="controls">
            <a href="view_data.php" class="btn">กลับไปดูข้อมูล</a>
            <a href="?refresh=1" class="btn">รีเฟรช</a>
            <a href="?clear=1" class="btn btn-danger" onclick="return confirm('คุณแน่ใจหรือไม่ที่จะล้าง Log ทั้งหมด?')">ล้าง Log</a>
        </div>

        <div class="log-container">
            <?php foreach ($logLines as $line): ?>
                <?php
                $cssClass = 'log-line';
                if (strpos($line, '[ERROR]') !== false) {
                    $cssClass .= ' log-error';
                } elseif (strpos($line, '[WARNING]') !== false) {
                    $cssClass .= ' log-warning';
                } elseif (strpos($line, '[INFO]') !== false) {
                    $cssClass .= ' log-info';
                }
                
                // Highlight timestamp
                $line = preg_replace('/(\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\])/', '<span class="log-timestamp">$1</span>', $line);
                ?>
                <div class="<?php echo $cssClass; ?>"><?php echo $line; ?></div>
            <?php endforeach; ?>
        </div>

        <div style="text-align: center; padding: 10px; background: #e9ecef; color: #666; font-size: 14px;">
            อัปเดตล่าสุด: <?php echo $lastModified; ?>
        </div>
    </div>

    <?php
    // Auto refresh if requested
    if (isset($_GET['refresh']) && $_GET['refresh'] == '1') {
        echo '<script>
            setTimeout(function() {
                window.location.href = "view_logs.php";
            }, 2000);
        </script>';
    }
    ?>
</body>
</html>
