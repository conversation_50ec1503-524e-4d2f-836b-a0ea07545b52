# PowerShell Script สำหรับรันการตรวจสอบสลิปอัตโนมัติทุก 5 วินาที

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Automatic Slip Verification Service" -ForegroundColor Cyan
Write-Host "  รันทุก 5 วินาที" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host

# เปลี่ยนไปยัง directory ของโปรเจค
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
Set-Location $ProjectRoot

# ตรวจสอบว่ามี PHP หรือไม่
try {
    $phpVersion = php --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ พบ PHP แล้ว" -ForegroundColor Green
    } else {
        throw "PHP not found"
    }
} catch {
    Write-Host "❌ ไม่พบ PHP ในระบบ" -ForegroundColor Red
    Write-Host "กรุณาติดตั้ง PHP หรือเพิ่ม PHP ใน PATH" -ForegroundColor Yellow
    Read-Host "กด Enter เพื่อออก"
    exit 1
}

Write-Host "🚀 เริ่มการตรวจสอบสลิปอัตโนมัติ..." -ForegroundColor Green
Write-Host "📝 Log จะถูกบันทึกใน cron/logs/slip_verification_auto.log" -ForegroundColor Yellow
Write-Host
Write-Host "กด Ctrl+C เพื่อหยุดการทำงาน" -ForegroundColor Magenta
Write-Host

# จัดการ Ctrl+C
$Host.UI.RawUI.KeyAvailable = $false
[Console]::TreatControlCAsInput = $true

try {
    while ($true) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "[$timestamp] กำลังตรวจสอบ..." -ForegroundColor White
        
        # รัน PHP script
        & php cron/slip_verification_auto.php
        
        # ตรวจสอบว่าผู้ใช้กด Ctrl+C หรือไม่
        if ([Console]::KeyAvailable) {
            $key = [Console]::ReadKey($true)
            if ($key.Key -eq "C" -and $key.Modifiers -eq "Control") {
                break
            }
        }
        
        # รอ 5 วินาที
        Start-Sleep -Seconds 5
    }
} catch {
    Write-Host "เกิดข้อผิดพลาด: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Write-Host
    Write-Host "🛑 หยุดการทำงานแล้ว" -ForegroundColor Yellow
    Read-Host "กด Enter เพื่อออก"
}
