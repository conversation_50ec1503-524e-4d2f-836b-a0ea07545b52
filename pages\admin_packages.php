<?php
// Handle package actions
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $database = new Database();
    $conn = $database->getConnection();
    
    if(isset($_POST['add_package'])) {
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $price = floatval($_POST['price'] ?? 0);
        $duration_days = intval($_POST['duration_days'] ?? 0);
        $max_devices = intval($_POST['max_devices'] ?? 1);
        $quality_limit = trim($_POST['quality_limit'] ?? '');
        
        if($name && $price > 0 && $duration_days > 0) {
            $query = "INSERT INTO packages (name, description, price, duration_days, max_devices, quality_limit) 
                      VALUES (:name, :description, :price, :duration_days, :max_devices, :quality_limit)";
            
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':price', $price);
            $stmt->bindParam(':duration_days', $duration_days);
            $stmt->bindParam(':max_devices', $max_devices);
            $stmt->bindParam(':quality_limit', $quality_limit);
            
            if($stmt->execute()) {
                $success_message = 'เพิ่มแพ็คเกจสำเร็จ';
            } else {
                $error_message = 'เกิดข้อผิดพลาดในการเพิ่มแพ็คเกจ';
            }
        } else {
            $error_message = 'กรุณากรอกข้อมูลให้ครบถ้วน';
        }
    }
    
    if(isset($_POST['toggle_status'])) {
        $package_id = intval($_POST['package_id'] ?? 0);
        $new_status = $_POST['new_status'] ?? '';
        
        if($package_id && in_array($new_status, ['active', 'inactive'])) {
            $query = "UPDATE packages SET status = :status WHERE id = :package_id";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':status', $new_status);
            $stmt->bindParam(':package_id', $package_id);
            
            if($stmt->execute()) {
                $success_message = 'อัปเดตสถานะแพ็คเกจสำเร็จ';
            } else {
                $error_message = 'เกิดข้อผิดพลาดในการอัปเดตสถานะ';
            }
        }
    }
}

// Get all packages
$database = new Database();
$conn = $database->getConnection();

$query = "SELECT p.*, 
          COUNT(us.id) as total_purchases,
          SUM(CASE WHEN us.status = 'active' AND us.end_date >= CURDATE() THEN 1 ELSE 0 END) as active_subscriptions
          FROM packages p 
          LEFT JOIN user_subscriptions us ON p.id = us.package_id 
          GROUP BY p.id 
          ORDER BY p.price ASC";

$stmt = $conn->prepare($query);
$stmt->execute();
$packages = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">จัดการแพ็คเกจ</h2>
            <p class="text-muted">เพิ่ม แก้ไข และจัดการแพ็คเกจบริการ</p>
        </div>
    </div>
    
    <!-- Messages -->
    <?php if(isset($success_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if(isset($error_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Add Package Form -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus me-2"></i>เพิ่มแพ็คเกจใหม่</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อแพ็คเกจ <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">ราคา (บาท) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" min="1" step="0.01" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">รายละเอียด</label>
                            <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="duration_days" class="form-label">ระยะเวลา (วัน) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="duration_days" name="duration_days" min="1" required>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="max_devices" class="form-label">จำนวนอุปกรณ์สูงสุด <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="max_devices" name="max_devices" min="1" value="1" required>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="quality_limit" class="form-label">คุณภาพวิดีโอ</label>
                                    <select class="form-control" id="quality_limit" name="quality_limit">
                                        <option value="720p">720p</option>
                                        <option value="1080p">1080p</option>
                                        <option value="4K">4K</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="add_package" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เพิ่มแพ็คเกจ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Packages List -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>แพ็คเกจทั้งหมด</h5>
                </div>
                <div class="card-body">
                    <?php if(!empty($packages)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>ชื่อแพ็คเกจ</th>
                                    <th>ราคา</th>
                                    <th>ระยะเวลา</th>
                                    <th>อุปกรณ์</th>
                                    <th>คุณภาพ</th>
                                    <th>ยอดขาย</th>
                                    <th>ใช้งานอยู่</th>
                                    <th>สถานะ</th>
                                    <th>การดำเนินการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($packages as $package): ?>
                                <tr>
                                    <td><?php echo $package['id']; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($package['name']); ?></strong>
                                        <?php if($package['description']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($package['description']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo number_format($package['price'], 0); ?> ฿</strong>
                                        <br><small class="text-muted"><?php echo number_format($package['price'] / $package['duration_days'], 2); ?> ฿/วัน</small>
                                    </td>
                                    <td><?php echo $package['duration_days']; ?> วัน</td>
                                    <td><?php echo $package['max_devices']; ?> เครื่อง</td>
                                    <td><?php echo $package['quality_limit']; ?></td>
                                    <td>
                                        <span class="badge bg-info"><?php echo number_format($package['total_purchases']); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success"><?php echo number_format($package['active_subscriptions']); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $package['status'] == 'active' ? 'bg-success' : 'bg-secondary'; ?>">
                                            <?php echo $package['status'] == 'active' ? 'เปิดใช้งาน' : 'ปิดใช้งาน'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="package_id" value="<?php echo $package['id']; ?>">
                                            <input type="hidden" name="new_status" value="<?php echo $package['status'] == 'active' ? 'inactive' : 'active'; ?>">
                                            <button type="submit" name="toggle_status" 
                                                    class="btn btn-sm <?php echo $package['status'] == 'active' ? 'btn-warning' : 'btn-success'; ?>"
                                                    onclick="return confirm('คุณต้องการเปลี่ยนสถานะแพ็คเกจนี้หรือไม่?')">
                                                <i class="fas <?php echo $package['status'] == 'active' ? 'fa-pause' : 'fa-play'; ?> me-1"></i>
                                                <?php echo $package['status'] == 'active' ? 'ปิดใช้งาน' : 'เปิดใช้งาน'; ?>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-box-open text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">ยังไม่มีแพ็คเกจ</h6>
                        <p class="text-muted">เพิ่มแพ็คเกจแรกของคุณ</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
