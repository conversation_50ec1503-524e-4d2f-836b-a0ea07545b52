<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayNoi Transaction Data</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .amount {
            font-weight: bold;
        }
        .amount.positive {
            color: #28a745;
        }
        .amount.negative {
            color: #dc3545;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 20px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
        }
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .last-updated {
            text-align: center;
            padding: 10px;
            background: #e9ecef;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PayNoi Transaction Data</h1>
            <p>ข้อมูลธุรกรรมจาก PayNoi API</p>
        </div>

        <?php
        $dataFile = __DIR__ . '/data/transactions.json';
        $logFile = __DIR__ . '/logs/api_fetch.log';
        
        // Check if data file exists
        if (!file_exists($dataFile)) {
            echo '<div class="error">ไม่พบไฟล์ข้อมูล กรุณารันสคริปต์ cron_api_fetch.php ก่อน</div>';
            exit;
        }
        
        // Read and decode JSON data
        $jsonData = file_get_contents($dataFile);
        $data = json_decode($jsonData, true);
        
        if (!$data) {
            echo '<div class="error">ไม่สามารถอ่านข้อมูลได้ ไฟล์ JSON อาจเสียหาย</div>';
            exit;
        }
        
        $transactions = $data['transactions'] ?? [];
        $lastUpdated = $data['last_updated'] ?? 'ไม่ทราบ';
        $totalTransactions = count($transactions);
        
        // Calculate statistics
        $totalAmount = 0;
        $positiveCount = 0;
        $negativeCount = 0;
        
        foreach ($transactions as $transaction) {
            $amount = floatval($transaction['amount']);
            $totalAmount += $amount;
            if ($amount > 0) {
                $positiveCount++;
            } elseif ($amount < 0) {
                $negativeCount++;
            }
        }
        ?>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($totalTransactions); ?></div>
                <div class="stat-label">ธุรกรรมทั้งหมด</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($positiveCount); ?></div>
                <div class="stat-label">รายรับ</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($negativeCount); ?></div>
                <div class="stat-label">รายจ่าย</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo number_format($totalAmount, 2); ?></div>
                <div class="stat-label">ยอดรวม (บาท)</div>
            </div>
        </div>

        <div class="controls">
            <a href="?refresh=1" class="btn btn-success">รีเฟรชข้อมูล</a>
            <a href="cron_api_fetch.php" class="btn" target="_blank">ดึงข้อมูลใหม่</a>
            <?php if (file_exists($logFile)): ?>
            <a href="view_logs.php" class="btn" target="_blank">ดู Log</a>
            <?php endif; ?>
        </div>

        <?php if (empty($transactions)): ?>
            <div class="no-data">
                <h3>ไม่มีข้อมูลธุรกรรม</h3>
                <p>กรุณารันสคริปต์ดึงข้อมูลก่อน</p>
            </div>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>Transaction ID</th>
                        <th>วันที่</th>
                        <th>บัญชี</th>
                        <th>จำนวน</th>
                        <th>สกุลเงิน</th>
                        <th>ยอดคงเหลือ</th>
                        <th>ประเภท</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transactions as $transaction): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($transaction['trans_id'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars($transaction['date']); ?></td>
                        <td><?php echo htmlspecialchars($transaction['bankaccount']); ?></td>
                        <td class="amount <?php echo floatval($transaction['amount']) >= 0 ? 'positive' : 'negative'; ?>">
                            <?php echo number_format(floatval($transaction['amount']), 2); ?>
                        </td>
                        <td><?php echo htmlspecialchars($transaction['currency']); ?></td>
                        <td><?php echo number_format(floatval($transaction['balance']), 2); ?></td>
                        <td><?php echo htmlspecialchars($transaction['type']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>

        <div class="last-updated">
            อัปเดตล่าสุด: <?php echo htmlspecialchars($lastUpdated); ?>
        </div>
    </div>

    <?php
    // Handle refresh request
    if (isset($_GET['refresh']) && $_GET['refresh'] == '1') {
        echo '<script>
            setTimeout(function() {
                window.location.href = "view_data.php";
            }, 2000);
        </script>';
    }
    ?>
</body>
</html>
