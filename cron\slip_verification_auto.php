<?php
/**
 * Automatic Slip Verification Cron Job
 * รันทุก 5 วินาที เพื่อตรวจสอบและอนุมัติการเติมเงินอัตโนมัติ
 */

// ป้องกันการเรียกจาก web browser
if (php_sapi_name() !== 'cli' && !isset($_GET['force'])) {
    http_response_code(403);
    die('Access denied. This script can only be run from command line.');
}

// เปลี่ยน working directory ไปยัง root ของโปรเจค
$script_dir = dirname(__FILE__);
$project_root = dirname($script_dir);
chdir($project_root);

// Include required files
require_once 'classes/SlipVerificationManager.php';

// สร้าง log file
$log_file = $script_dir . '/logs/slip_verification_auto.log';
$log_dir = dirname($log_file);

if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

function writeLog($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    
    // แสดงใน console ด้วย (สำหรับ debug)
    echo $log_message;
}

try {
    writeLog("=== เริ่มการตรวจสอบสลิปอัตโนมัติ ===");
    
    // สร้าง SlipVerificationManager
    $slipManager = new SlipVerificationManager();
    writeLog("✅ สร้าง SlipVerificationManager สำเร็จ");
    
    // ตรวจสอบและอนุมัติอัตโนมัติ
    $result = $slipManager->processAutoApproval();
    
    if ($result['success']) {
        $processed = $result['processed'];
        $approved = $result['approved'];
        
        writeLog("✅ การตรวจสอบเสร็จสิ้น: ประมวลผล {$processed} รายการ, อนุมัติ {$approved} รายการ");
        
        // แสดงรายละเอียดการอนุมัติ
        if ($approved > 0 && !empty($result['results'])) {
            foreach ($result['results'] as $res) {
                if ($res['approved']) {
                    writeLog("💰 อนุมัติสำเร็จ: Trans ID {$res['trans_id']}, จำนวน {$res['amount']} ฿");
                }
            }
        }
        
        // แสดงรายละเอียดที่ไม่สามารถอนุมัติได้ (เฉพาะเมื่อมีปัญหา)
        if ($processed > $approved && !empty($result['results'])) {
            $failed_count = 0;
            foreach ($result['results'] as $res) {
                if (!$res['approved']) {
                    $failed_count++;
                    if ($failed_count <= 3) { // แสดงเฉพาะ 3 รายการแรก
                        writeLog("⚠️ ไม่สามารถอนุมัติ: Trans ID {$res['trans_id']}, เหตุผล: {$res['reason']}");
                    }
                }
            }
            if ($failed_count > 3) {
                writeLog("⚠️ และอีก " . ($failed_count - 3) . " รายการที่ไม่สามารถอนุมัติได้");
            }
        }
        
    } else {
        writeLog("❌ การตรวจสอบล้มเหลว: " . $result['message']);
    }
    
    writeLog("=== สิ้นสุดการตรวจสอบ ===");
    
} catch (Exception $e) {
    writeLog("💥 เกิดข้อผิดพลาด: " . $e->getMessage());
    writeLog("📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine());
    
    // ส่งอีเมลแจ้งเตือน (ถ้าต้องการ)
    // mail('<EMAIL>', 'Slip Verification Error', $e->getMessage());
}

// ทำความสะอาด log เก่า (เก็บไว้ 7 วัน)
$log_content = file_get_contents($log_file);
$lines = explode("\n", $log_content);

// เก็บเฉพาะ log ล่าสุด 1000 บรรทัด
if (count($lines) > 1000) {
    $recent_lines = array_slice($lines, -1000);
    file_put_contents($log_file, implode("\n", $recent_lines));
    writeLog("🧹 ทำความสะอาด log เก่า");
}

?>
