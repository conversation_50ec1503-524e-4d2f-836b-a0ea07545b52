/**
 * Media Server Management System - Animation JavaScript
 * Enhanced user experience with smooth animations and interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all animations
    initializeAnimations();
    initializeCounters();
    initializeProgressBars();
    initializeFormAnimations();
    initializeCardHovers();
    initializeButtonAnimations();
    initializeScrollAnimations();
    
});

/**
 * Initialize basic animations
 */
function initializeAnimations() {
    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
    
    // Add slide animations to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('slide-in-up');
        }, index * 100);
    });
    
    // Add bounce animation to alerts
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        alert.classList.add('alert-animated');
    });
}

/**
 * Initialize counter animations
 */
function initializeCounters() {
    const counters = document.querySelectorAll('.counter');
    
    counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                counter.textContent = Math.floor(current).toLocaleString();
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target.toLocaleString();
                counter.classList.add('counter-animate');
            }
        };
        
        // Start animation when element is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateCounter();
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(counter);
    });
}

/**
 * Initialize progress bar animations
 */
function initializeProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');
    
    progressBars.forEach(bar => {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.parentElement.classList.add('progress-animated');
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(bar);
    });
}

/**
 * Initialize form animations
 */
function initializeFormAnimations() {
    // Floating label effect
    const formInputs = document.querySelectorAll('.form-control');
    
    formInputs.forEach(input => {
        // Add focus animation
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // Check if input has value on load
        if (input.value) {
            input.parentElement.classList.add('focused');
        }
    });
    
    // Form submission animation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                // Add loading animation
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>กำลังดำเนินการ...';
                submitBtn.disabled = true;

                // Add form shake animation for validation errors
                const requiredFields = this.querySelectorAll('[required]');
                let hasErrors = false;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('shake');
                        field.style.borderColor = '#dc3545';
                        hasErrors = true;

                        setTimeout(() => {
                            field.classList.remove('shake');
                            field.style.borderColor = '';
                        }, 500);
                    }
                });

                if (hasErrors) {
                    e.preventDefault();
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    showNotification('กรุณากรอกข้อมูลให้ครบถ้วน', 'danger');
                }
            }
        });

        // Add success animation when form is valid
        form.addEventListener('input', function(e) {
            const field = e.target;
            if (field.checkValidity()) {
                field.style.borderColor = '#28a745';
                field.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
            } else {
                field.style.borderColor = '';
                field.style.boxShadow = '';
            }
        });
    });
}

/**
 * Initialize card hover animations
 */
function initializeCardHovers() {
    const cards = document.querySelectorAll('.card');
    
    cards.forEach(card => {
        // Add hover class for CSS animations
        card.classList.add('card-hover');
        
        // Add click ripple effect
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.classList.add('ripple');
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * Initialize button animations
 */
function initializeButtonAnimations() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.classList.add('btn-animated');
        
        // Add loading state for async operations
        if (button.dataset.loading) {
            button.addEventListener('click', function() {
                if (!this.disabled) {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<span class="loading-spinner me-2"></span>กำลังโหลด...';
                    this.disabled = true;
                    
                    // Re-enable after 3 seconds (adjust as needed)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 3000);
                }
            });
        }
    });
}

/**
 * Initialize scroll-based animations
 */
function initializeScrollAnimations() {
    const animatedElements = document.querySelectorAll('[data-animate]');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const animationType = entry.target.dataset.animate;
                entry.target.classList.add(animationType);
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

/**
 * Utility function to add shake animation to elements
 */
function shakeElement(element) {
    element.classList.add('shake');
    setTimeout(() => {
        element.classList.remove('shake');
    }, 500);
}

/**
 * Utility function to show success animation
 */
function showSuccessAnimation(element) {
    element.classList.add('bounce-in');
    setTimeout(() => {
        element.classList.remove('bounce-in');
    }, 600);
}

/**
 * Smooth scroll to element
 */
function smoothScrollTo(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

/**
 * QR Code animation
 */
function animateQRCode() {
    const qrContainer = document.querySelector('.qr-code-container');
    const qrImage = document.querySelector('.qr-code-animation');

    if (qrContainer) {
        qrContainer.classList.add('qr-code-container');
    }

    if (qrImage) {
        // Add sparkle effect around QR code
        createSparkleEffect(qrImage);

        // Add hover effect
        qrImage.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) rotate(5deg)';
            this.style.filter = 'drop-shadow(0 0 20px rgba(102, 126, 234, 0.5))';
        });

        qrImage.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
            this.style.filter = 'none';
        });
    }
}

/**
 * Create sparkle effect around element
 */
function createSparkleEffect(element) {
    const sparkleCount = 8;
    const container = element.parentElement;

    for (let i = 0; i < sparkleCount; i++) {
        const sparkle = document.createElement('div');
        sparkle.className = 'sparkle';
        sparkle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            pointer-events: none;
            animation: sparkleFloat 3s infinite;
            animation-delay: ${i * 0.3}s;
        `;

        // Position sparkles around the QR code
        const angle = (i / sparkleCount) * 2 * Math.PI;
        const radius = 150;
        const x = Math.cos(angle) * radius;
        const y = Math.sin(angle) * radius;

        sparkle.style.left = `calc(50% + ${x}px)`;
        sparkle.style.top = `calc(50% + ${y}px)`;

        container.appendChild(sparkle);

        // Remove sparkle after animation
        setTimeout(() => {
            if (sparkle.parentElement) {
                sparkle.remove();
            }
        }, 3000);
    }

    // Add sparkle animation CSS if not exists
    if (!document.querySelector('#sparkle-styles')) {
        const style = document.createElement('style');
        style.id = 'sparkle-styles';
        style.textContent = `
            @keyframes sparkleFloat {
                0%, 100% {
                    opacity: 0;
                    transform: translateY(0px) scale(0);
                }
                50% {
                    opacity: 1;
                    transform: translateY(-20px) scale(1);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Table row highlight animation
 */
function highlightTableRow(row) {
    row.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
    setTimeout(() => {
        row.style.backgroundColor = '';
    }, 1000);
}

/**
 * Modal entrance animation
 */
function animateModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.addEventListener('shown.bs.modal', function() {
            this.querySelector('.modal-content').classList.add('bounce-in');
        });
    }
}

/**
 * Notification animation
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-animated position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Loading overlay
 */
function showLoadingOverlay() {
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center text-white">
                <div class="loading-spinner mb-3" style="width: 50px; height: 50px; border-width: 5px;"></div>
                <h5>กำลังโหลด...</h5>
            </div>
        </div>
    `;
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        z-index: 9999;
        display: flex;
    `;
    
    document.body.appendChild(overlay);
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.remove();
    }
}

// Export functions for global use
window.MediaAnimations = {
    shake: shakeElement,
    success: showSuccessAnimation,
    scrollTo: smoothScrollTo,
    qrCode: animateQRCode,
    highlightRow: highlightTableRow,
    modal: animateModal,
    notify: showNotification,
    showLoading: showLoadingOverlay,
    hideLoading: hideLoadingOverlay
};
