@echo off
REM Cron Job Menu - เลือกวิธีรัน

:menu
cls
echo ========================================
echo Media Server Cron Jobs - Menu
echo ========================================
echo.
echo Choose what to run:
echo.
echo 1. Fast Cron (Transaction + Slip Verification) - Every 5 seconds
echo 2. Slow Cron (Subscription Check) - Run once
echo 3. Slip Verification Only - Every 5 seconds
echo 4. Test PHP Installation
echo 5. Find Laragon PHP Path
echo.
echo 0. Exit
echo.
echo ========================================

set /p choice=Enter your choice (0-5): 

if "%choice%"=="1" goto fast_cron
if "%choice%"=="2" goto slow_cron
if "%choice%"=="3" goto slip_only
if "%choice%"=="4" goto test_php
if "%choice%"=="5" goto find_php
if "%choice%"=="0" goto exit
goto menu

:fast_cron
echo.
echo Starting Fast Cron (Transaction + Slip Verification)...
echo This will run continuously every 5 seconds.
echo Press Ctrl+C to stop.
echo.
pause
start_fast_cron_laragon.bat
goto menu

:slow_cron
echo.
echo Starting Slow Cron (Subscription Check)...
run_slow_cron_laragon.bat
pause
goto menu

:slip_only
echo.
echo Starting Slip Verification Only...
echo This will run continuously every 5 seconds.
echo Press Ctrl+C to stop.
echo.
pause
start_slip_verification_5sec.bat
goto menu

:test_php
echo.
echo Testing PHP Installation...
echo.
set PHP_PATH=C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe

if exist "%PHP_PATH%" (
    echo [FOUND] PHP at: %PHP_PATH%
    "%PHP_PATH%" -v
    echo.
    echo [OK] PHP is working correctly!
) else (
    echo [ERROR] PHP not found at: %PHP_PATH%
    echo.
    echo Please check if Laragon is installed or run option 5 to find PHP.
)
echo.
pause
goto menu

:find_php
echo.
echo Searching for Laragon PHP...
find_laragon_php.bat
pause
goto menu

:exit
echo.
echo Goodbye!
exit /b 0
