<?php
/**
 * Fast Cron Script - รันทุก 5 วินาที
 * 
 * งานที่ทำ:
 * 1. ดึงข้อมูล transaction จาก API
 * 2. ตรวจสอบและอนุมัติสลิปอัตโนมัติ
 * 
 * วิธีใช้:
 * - Windows: php fast_cron.php
 * - Linux: php fast_cron.php
 * - Continuous: ใช้ start_fast_cron.bat/sh
 */

// Set timezone
date_default_timezone_set('Asia/Bangkok');

// Include required files
require_once __DIR__ . '/../config/db_config.php';

// Check if required classes exist
$required_classes = [
    __DIR__ . '/../classes/PaymentManager.php',
    __DIR__ . '/../classes/SlipVerificationManager.php'
];

foreach ($required_classes as $class_file) {
    if (file_exists($class_file)) {
        require_once $class_file;
    }
}

// Log file
$log_file = __DIR__ . '/logs/fast_cron.log';

// Ensure log directory exists
if (!file_exists(dirname($log_file))) {
    mkdir(dirname($log_file), 0755, true);
}

/**
 * Log function
 */
function logMessage($message, $log_file) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry; // Also output to console
}

/**
 * 1. ดึงข้อมูล Transaction จาก API
 */
function fetchTransactions($log_file) {
    try {
        // Check if API script exists
        $api_script = __DIR__ . '/../api/cron_api_fetch.php';
        
        if (!file_exists($api_script)) {
            return true; // Skip silently if API script not found
        }
        
        // Capture output from API script
        ob_start();
        $result = include $api_script;
        $output = ob_get_clean();
        
        if ($output && trim($output) !== '') {
            logMessage("API: " . trim($output), $log_file);
        }
        
        return true;
        
    } catch (Exception $e) {
        logMessage("ERROR API: " . $e->getMessage(), $log_file);
        return false;
    }
}

/**
 * 2. ตรวจสอบและอนุมัติสลิปอัตโนมัติ
 */
function processSlipVerification($log_file) {
    try {
        // Check if SlipVerificationManager class exists
        if (!class_exists('SlipVerificationManager')) {
            return true; // Skip silently if class not found
        }
        
        $database = new Database();
        $conn = $database->getConnection();
        
        if (!$conn) {
            logMessage("ERROR: ไม่สามารถเชื่อมต่อฐานข้อมูลได้", $log_file);
            return false;
        }
        
        $slipManager = new SlipVerificationManager();
        $result = $slipManager->processAutoVerification();
        
        if ($result['success']) {
            if (isset($result['processed']) && $result['processed'] > 0) {
                logMessage("SLIP: อนุมัติ {$result['processed']} รายการ", $log_file);
            }
            // Don't log if no slips processed (to reduce log noise)
        } else {
            logMessage("ERROR SLIP: " . $result['message'], $log_file);
        }
        
        return $result['success'];
        
    } catch (Exception $e) {
        logMessage("ERROR SLIP: " . $e->getMessage(), $log_file);
        return false;
    }
}

// ===== MAIN EXECUTION =====

$success_count = 0;
$total_tasks = 2;

// Task 1: Fetch Transactions
if (fetchTransactions($log_file)) {
    $success_count++;
}

// Task 2: Process Slip Verification
if (processSlipVerification($log_file)) {
    $success_count++;
}

// Only log summary if there were errors or activities
if ($success_count < $total_tasks) {
    logMessage("⚠️ Fast Cron: {$success_count}/{$total_tasks} งานสำเร็จ", $log_file);
    exit(1); // Partial failure
}

exit(0); // Success
?>
