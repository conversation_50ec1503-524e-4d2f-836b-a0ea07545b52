#!/bin/bash
# Fast Cron Runner - รันทุก 5 วินาที
# งาน: ดึง transaction + ตรวจสลิป

echo "========================================"
echo "Fast Cron Job - Starting (Every 5 seconds)"
echo "Time: $(date)"
echo "========================================"
echo ""
echo "กำลังรันงาน:"
echo "- ดึงข้อมูล Transaction จาก API"
echo "- ตรวจสอบและอนุมัติสลิปอัตโนมัติ"
echo ""
echo "กด Ctrl+C เพื่อหยุด"
echo "========================================"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Create logs directory if not exists
mkdir -p logs

# Trap Ctrl+C
trap 'echo -e "\n========================================"; echo "Fast Cron Job - Stopped"; echo "Time: $(date)"; echo "========================================"; exit 0' INT

# Main loop
while true; do
    # Run fast cron
    php fast_cron.php
    
    # Wait 5 seconds
    sleep 5
done
