<?php
// Get all users
$database = new Database();
$conn = $database->getConnection();

$query = "SELECT u.*, 
          (SELECT COUNT(*) FROM user_subscriptions us WHERE us.user_id = u.id AND us.status = 'active' AND us.end_date >= CURDATE()) as active_subscriptions,
          (SELECT SUM(amount) FROM top_ups t WHERE t.user_id = u.id AND t.status = 'approved') as total_topups
          FROM users u 
          ORDER BY u.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats_query = "SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
    COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_30d
    FROM users";

$stmt = $conn->prepare($stats_query);
$stmt->execute();
$stats = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">จัดการผู้ใช้งาน</h2>
            <p class="text-muted">ดูรายชื่อและจัดการบัญชีผู้ใช้ทั้งหมด</p>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">ผู้ใช้ทั้งหมด</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['total_users']); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">ผู้ใช้ที่ใช้งานได้</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['active_users']); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">ผู้ดูแลระบบ</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['admin_users']); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-shield fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">สมาชิกใหม่ (30 วัน)</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['new_users_30d']); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>รายชื่อผู้ใช้ทั้งหมด</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>ชื่อผู้ใช้</th>
                                    <th>ชื่อ-นามสกุล</th>
                                    <th>อีเมล</th>
                                    <th>ยอดเงิน</th>
                                    <th>แพ็คเกจ</th>
                                    <th>เติมเงินรวม</th>
                                    <th>สถานะ</th>
                                    <th>วันที่สมัคร</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($users as $user_item): ?>
                                <tr>
                                    <td><?php echo $user_item['id']; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($user_item['username']); ?></strong>
                                        <?php if($user_item['role'] == 'admin'): ?>
                                        <span class="badge bg-warning ms-1">Admin</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($user_item['full_name']); ?></td>
                                    <td>
                                        <small><?php echo htmlspecialchars($user_item['email']); ?></small>
                                    </td>
                                    <td>
                                        <span class="text-primary"><?php echo number_format($user_item['balance'], 2); ?> ฿</span>
                                    </td>
                                    <td>
                                        <?php if($user_item['active_subscriptions'] > 0): ?>
                                        <span class="badge bg-success">มีแพ็คเกจ</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">ไม่มี</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo number_format($user_item['total_topups'] ?? 0, 2); ?> ฿
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $user_item['status'] == 'active' ? 'bg-success' : 'bg-secondary'; ?>">
                                            <?php echo $user_item['status'] == 'active' ? 'ใช้งานได้' : $user_item['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo date('d/m/Y', strtotime($user_item['created_at'])); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
