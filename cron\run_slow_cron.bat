@echo off
REM Slow Cron Runner - รันทุก 30 นาที
REM งาน: ตรวจสอบ subscription ที่หมดอายุ

echo ========================================
echo Slow Cron Job - Starting
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Run the slow cron script
php slow_cron.php

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED SUCCESSFULLY
    echo Time: %date% %time%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Slow Cron Job - COMPLETED WITH ERRORS
    echo Exit Code: %ERRORLEVEL%
    echo Time: %date% %time%
    echo ========================================
)

REM Keep window open for 3 seconds if run manually
timeout /t 3 /nobreak >nul

exit /b %ERRORLEVEL%
