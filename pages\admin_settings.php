<?php
// Handle settings update
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_settings'])) {
    $database = new Database();
    $conn = $database->getConnection();
    $settings = new SystemSettings($conn);
    
    $settings_to_update = [
        'site_name' => $_POST['site_name'] ?? '',
        'admin_email' => $_POST['admin_email'] ?? '',
        'emby_server_url' => $_POST['emby_server_url'] ?? '',
        'emby_api_key' => $_POST['emby_api_key'] ?? '',
        'jellyfin_server_url' => $_POST['jellyfin_server_url'] ?? '',
        'jellyfin_api_key' => $_POST['jellyfin_api_key'] ?? '',
        'promptpay_phone' => $_POST['promptpay_phone'] ?? '',
        'expired_password' => $_POST['expired_password'] ?? ''
    ];
    
    $success_count = 0;
    foreach($settings_to_update as $key => $value) {
        if($settings->set($key, $value)) {
            $success_count++;
        }
    }
    
    if($success_count > 0) {
        $success_message = 'อัปเดตการตั้งค่าสำเร็จ';
    } else {
        $error_message = 'เกิดข้อผิดพลาดในการอัปเดตการตั้งค่า';
    }
}

// Test API connections
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['test_apis'])) {
    require_once 'classes/EmbyAPI.php';
    require_once 'classes/JellyfinAPI.php';
    
    $database = new Database();
    $conn = $database->getConnection();
    $settings = new SystemSettings($conn);
    
    $test_results = [];
    
    // Test Emby API
    $emby_url = $settings->get('emby_server_url');
    $emby_key = $settings->get('emby_api_key');
    
    if($emby_url && $emby_key) {
        try {
            $emby_api = new EmbyAPI($emby_url, $emby_key);
            $emby_test = $emby_api->testConnection();
            $test_results['emby'] = $emby_test;
        } catch(Exception $e) {
            $test_results['emby'] = false;
            $test_results['emby_error'] = $e->getMessage();
        }
    } else {
        $test_results['emby'] = false;
        $test_results['emby_error'] = 'URL หรือ API Key ไม่ได้ตั้งค่า';
    }
    
    // Test Jellyfin API
    $jellyfin_url = $settings->get('jellyfin_server_url');
    $jellyfin_key = $settings->get('jellyfin_api_key');
    
    if($jellyfin_url && $jellyfin_key) {
        try {
            $jellyfin_api = new JellyfinAPI($jellyfin_url, $jellyfin_key);
            $jellyfin_test = $jellyfin_api->testConnection();
            $test_results['jellyfin'] = $jellyfin_test;
        } catch(Exception $e) {
            $test_results['jellyfin'] = false;
            $test_results['jellyfin_error'] = $e->getMessage();
        }
    } else {
        $test_results['jellyfin'] = false;
        $test_results['jellyfin_error'] = 'URL หรือ API Key ไม่ได้ตั้งค่า';
    }
}

// Get current settings
$database = new Database();
$conn = $database->getConnection();
$settings = new SystemSettings($conn);
$current_settings = $settings->getAll();

// Convert to associative array for easier access
$settings_array = [];
foreach($current_settings as $setting) {
    $settings_array[$setting['setting_key']] = $setting['setting_value'];
}
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">ตั้งค่าระบบ</h2>
            <p class="text-muted">กำหนดค่าการทำงานของระบบ</p>
        </div>
    </div>
    
    <!-- Messages -->
    <?php if(isset($success_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if(isset($error_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- API Test Results -->
    <?php if(isset($test_results)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-flask me-2"></i>ผลการทดสอบ API</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Emby API</h6>
                            <?php if($test_results['emby']): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>เชื่อมต่อสำเร็จ
                            </div>
                            <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>เชื่อมต่อไม่สำเร็จ
                                <?php if(isset($test_results['emby_error'])): ?>
                                <br><small><?php echo htmlspecialchars($test_results['emby_error']); ?></small>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Jellyfin API</h6>
                            <?php if($test_results['jellyfin']): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>เชื่อมต่อสำเร็จ
                            </div>
                            <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>เชื่อมต่อไม่สำเร็จ
                                <?php if(isset($test_results['jellyfin_error'])): ?>
                                <br><small><?php echo htmlspecialchars($test_results['jellyfin_error']); ?></small>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- Settings Form -->
        <div class="col-md-8">
            <form method="POST">
                <!-- General Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>การตั้งค่าทั่วไป</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="site_name" class="form-label">ชื่อเว็บไซต์</label>
                            <input type="text" class="form-control" id="site_name" name="site_name" 
                                   value="<?php echo htmlspecialchars($settings_array['site_name'] ?? ''); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="admin_email" class="form-label">อีเมลผู้ดูแลระบบ</label>
                            <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                   value="<?php echo htmlspecialchars($settings_array['admin_email'] ?? ''); ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Emby Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tv me-2"></i>การตั้งค่า Emby</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="emby_server_url" class="form-label">Emby Server URL</label>
                            <input type="url" class="form-control" id="emby_server_url" name="emby_server_url"
                                   value="<?php echo htmlspecialchars($settings_array['emby_server_url'] ?? ''); ?>"
                                   placeholder="https://emby.embyjames.xyz">
                        </div>
                        
                        <div class="mb-3">
                            <label for="emby_api_key" class="form-label">Emby API Key</label>
                            <input type="text" class="form-control" id="emby_api_key" name="emby_api_key" 
                                   value="<?php echo htmlspecialchars($settings_array['emby_api_key'] ?? ''); ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Jellyfin Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-play me-2"></i>การตั้งค่า Jellyfin</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="jellyfin_server_url" class="form-label">Jellyfin Server URL</label>
                            <input type="url" class="form-control" id="jellyfin_server_url" name="jellyfin_server_url"
                                   value="<?php echo htmlspecialchars($settings_array['jellyfin_server_url'] ?? ''); ?>"
                                   placeholder="https://jellyfin.embyjames.xyz">
                        </div>
                        
                        <div class="mb-3">
                            <label for="jellyfin_api_key" class="form-label">Jellyfin API Key</label>
                            <input type="text" class="form-control" id="jellyfin_api_key" name="jellyfin_api_key" 
                                   value="<?php echo htmlspecialchars($settings_array['jellyfin_api_key'] ?? ''); ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Payment Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>การตั้งค่าการชำระเงิน</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="promptpay_phone" class="form-label">เบอร์โทรศัพท์ PromptPay</label>
                            <input type="tel" class="form-control" id="promptpay_phone" name="promptpay_phone"
                                   value="<?php echo htmlspecialchars($settings_array['promptpay_phone'] ?? ''); ?>"
                                   placeholder="0812345678" maxlength="10">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                กรอกเบอร์โทรศัพท์ที่ลงทะเบียน PromptPay ไว้ (10 หลัก เริ่มต้นด้วย 0)
                            </div>
                            <?php if(!empty($settings_array['promptpay_phone'])): ?>
                            <div class="mt-2">
                                <small class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    ตั้งค่าแล้ว: <?php echo htmlspecialchars($settings_array['promptpay_phone']); ?>
                                </small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Security Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>การตั้งค่าความปลอดภัย</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="expired_password" class="form-label">รหัสผ่านสำหรับบัญชีที่หมดอายุ</label>
                            <input type="text" class="form-control" id="expired_password" name="expired_password" 
                                   value="<?php echo htmlspecialchars($settings_array['expired_password'] ?? ''); ?>">
                            <div class="form-text">รหัสผ่านที่จะใช้เมื่อแพ็คเกจหมดอายุ</div>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid">
                    <button type="submit" name="update_settings" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>บันทึกการตั้งค่า
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Quick Actions -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-tools me-2"></i>เครื่องมือ</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <form method="POST">
                            <button type="submit" name="test_apis" class="btn btn-info w-100">
                                <i class="fas fa-flask me-2"></i>ทดสอบ API
                            </button>
                        </form>
                        
                        <a href="cron/check_subscriptions.php" target="_blank" class="btn btn-warning">
                            <i class="fas fa-clock me-2"></i>รัน Cron Job
                        </a>
                        
                        <a href="?page=admin_users" class="btn btn-outline-primary">
                            <i class="fas fa-users me-2"></i>จัดการผู้ใช้
                        </a>

                        <a href="?page=admin_subscriptions" class="btn btn-outline-info">
                            <i class="fas fa-calendar-alt me-2"></i>จัดการ Subscriptions
                        </a>

                        <a href="?page=admin_topups" class="btn btn-outline-success">
                            <i class="fas fa-money-bill me-2"></i>อนุมัติเติมเงิน
                        </a>

                        <a href="?page=remote_access_control" class="btn btn-outline-warning">
                            <i class="fas fa-wifi me-2"></i>Remote Access Control
                        </a>

                        <hr>

                        <a href="setup_api_settings.php" class="btn btn-info">
                            <i class="fas fa-plug me-2"></i>ตั้งค่า API
                        </a>

                        <a href="check_api_settings.php" class="btn btn-outline-info">
                            <i class="fas fa-search me-2"></i>ตรวจสอบ API
                        </a>

                        <a href="fix_api_issues.php" class="btn btn-outline-warning">
                            <i class="fas fa-wrench me-2"></i>แก้ไขปัญหา API
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- System Info -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>ข้อมูลระบบ</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                    <p class="mb-2"><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                    <p class="mb-2"><strong>Database:</strong> MySQL</p>
                    <p class="mb-0"><strong>Timezone:</strong> <?php echo date_default_timezone_get(); ?></p>
                </div>
            </div>
        </div>
    </div>
</div>
