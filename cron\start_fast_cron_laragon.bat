@echo off
REM Fast Cron Runner for Laragon - รันทุก 5 วินาที

echo ========================================
echo Fast Cron Job - Laragon Version
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Try to find Laragon PHP (check common versions)
set PHP_PATH=
for %%v in (php-8.3.* php-8.2.* php-8.1.* php-8.0.* php-7.4.*) do (
    if exist "C:\laragon\bin\php\%%v\php.exe" (
        set PHP_PATH=C:\laragon\bin\php\%%v\php.exe
        goto found_php
    )
)

REM If not found in versioned folders, try direct path
if exist "C:\laragon\bin\php\php.exe" (
    set PHP_PATH=C:\laragon\bin\php\php.exe
    goto found_php
)

REM If still not found, show error
echo ERROR: Laragon PHP not found!
echo.
echo Searched locations:
echo - C:\laragon\bin\php\php-8.3.*\php.exe
echo - C:\laragon\bin\php\php-8.2.*\php.exe
echo - C:\laragon\bin\php\php-8.1.*\php.exe
echo - C:\laragon\bin\php\php-8.0.*\php.exe
echo - C:\laragon\bin\php\php-7.4.*\php.exe
echo - C:\laragon\bin\php\php.exe
echo.
echo Please check if Laragon is installed correctly
echo or run find_php.bat to locate PHP
echo.
pause
exit /b 1

:found_php
echo Using PHP: %PHP_PATH%
echo.

REM Test PHP
"%PHP_PATH%" -v >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: PHP test failed!
    echo Path: %PHP_PATH%
    pause
    exit /b 1
)

echo กำลังรันงาน:
echo - ดึงข้อมูล Transaction จาก API
echo - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
echo.
echo กด Ctrl+C เพื่อหยุด
echo ========================================

REM Create logs directory if not exists
if not exist "logs" mkdir logs

:loop
    REM Run fast cron
    "%PHP_PATH%" fast_cron.php
    
    REM Check if command failed
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ERROR: Fast cron failed with exit code %ERRORLEVEL%
        echo Check logs/fast_cron.log for details
        echo.
        pause
        goto end
    )
    
    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul
    
goto loop

:end
echo.
echo ========================================
echo Fast Cron Job - Stopped
echo Time: %date% %time%
echo ========================================
