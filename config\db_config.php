<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'media_server_management');
define('DB_USER', 'root');
define('DB_PASS', 'Wxmujwsofu@1234');
define('DB_CHARSET', 'utf8mb4');

// Database Connection Class
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }
}

// System Settings
class SystemSettings {
    private $conn;
    private $table_name = "system_settings";

    public function __construct($db) {
        $this->conn = $db;
    }

    public function get($key) {
        $query = "SELECT setting_value FROM " . $this->table_name . " WHERE setting_key = :key LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->execute();
        
        if($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['setting_value'];
        }
        return null;
    }

    public function set($key, $value, $description = '') {
        $query = "INSERT INTO " . $this->table_name . " (setting_key, setting_value, description) 
                  VALUES (:key, :value, :description) 
                  ON DUPLICATE KEY UPDATE setting_value = :value2, description = :description2";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->bindParam(':value', $value);
        $stmt->bindParam(':value2', $value);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':description2', $description);
        
        return $stmt->execute();
    }

    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY setting_key";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function initializeDefaults() {
        $defaults = [
            'site_name' => 'ดูหนังออนไลน์ 24hr.',
            'admin_email' => '<EMAIL>',
            'emby_server_url' => 'https://emby.embyjames.xyz',
            'emby_api_key' => 'd2499d0eacfe4ccbba940836be91a9f1',
            'jellyfin_server_url' => 'https://jellyfin.embyjames.xyz',
            'jellyfin_api_key' => '',
            'promptpay_phone' => '',
            'expired_password' => 'expired123'
        ];

        try {
            foreach($defaults as $key => $value) {
                // Check if setting exists
                $existing = $this->get($key);
                if($existing === null) {
                    $this->set($key, $value);
                }
            }

            return true;

        } catch(Exception $e) {
            error_log('SystemSettings initializeDefaults error: ' . $e->getMessage());
            return false;
        }
    }
}
?>
