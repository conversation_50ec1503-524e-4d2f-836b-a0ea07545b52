#!/bin/bash

# PayNoi API Continuous Cron Runner for Linux/macOS
# This script runs the PHP script continuously every 5 seconds

echo "========================================"
echo "PayNoi API Continuous Data Fetcher"
echo "Running every 5 seconds"
echo "========================================"
echo ""

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PHP_SCRIPT="$SCRIPT_DIR/continuous_cron.php"

echo "Script location: $PHP_SCRIPT"
echo ""

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "Error: PHP is not installed or not in PATH"
    exit 1
fi

echo "PHP version: $(php -v | head -n 1)"
echo ""

# Check if script exists
if [ ! -f "$PHP_SCRIPT" ]; then
    echo "Error: continuous_cron.php not found"
    exit 1
fi

echo "Starting continuous cron job..."
echo "Press Ctrl+C to stop"
echo ""

# Function to handle cleanup on exit
cleanup() {
    echo ""
    echo "========================================"
    echo "Continuous cron stopped"
    echo "========================================"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Run the continuous cron script
php "$PHP_SCRIPT"

# Check the exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "========================================"
    echo "Continuous cron stopped normally"
    echo "========================================"
else
    echo ""
    echo "========================================"
    echo "ERROR: Continuous cron stopped with error"
    echo "========================================"
    echo "Check the logs for more information"
fi
