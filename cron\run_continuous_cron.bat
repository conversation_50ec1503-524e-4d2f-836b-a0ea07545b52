@echo off
REM PayNoi API Continuous Cron Runner for Windows
REM This batch file runs the PHP script continuously every 5 seconds

echo ========================================
echo PayNoi API Continuous Data Fetcher
echo Running every 5 seconds
echo ========================================
echo.

REM Set the PHP path (adjust this path according to your Laragon installation)
set PHP_PATH=C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe

REM Check if PHP exists
if not exist "%PHP_PATH%" (
    echo Error: PHP not found at %PHP_PATH%
    echo Please update the PHP_PATH in this batch file
    pause
    exit /b 1
)

echo PHP found at: %PHP_PATH%
echo.

echo Starting continuous cron job...
echo Press Ctrl+C to stop
echo.

REM Run the continuous cron script
"%PHP_PATH%" continuous_cron.php

REM Check the exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Continuous cron stopped normally
    echo ========================================
) else (
    echo.
    echo ========================================
    echo ERROR: Continuous cron stopped with error
    echo ========================================
    echo Check the logs for more information
)

echo.
echo Press any key to exit...
pause >nul
