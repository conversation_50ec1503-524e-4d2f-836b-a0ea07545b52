# Main Cron Job - สคริปต์รวมทุกงาน

## 📋 ภาพรวม

สคริปต์ `main_cron.php` รวมงานทั้งหมดไว้ในไฟล์เดียว:

1. **ดึงข้อมูล Transaction** จาก API
2. **ตรวจสอบและอนุมัติสลิป** อัตโนมัติ
3. **ตรวจสอบ Subscription** ที่หมดอายุและอัพเดทสถานะ

## 🚀 วิธีใช้งาน

### Windows

#### วิธีที่ 1: Batch File
```bash
# รันครั้งเดียว
run_main_cron.bat

# หรือ double-click ที่ไฟล์
```

#### วิธีที่ 2: PowerShell
```powershell
# รันครั้งเดียว
.\run_main_cron.ps1

# หรือ
powershell -ExecutionPolicy Bypass -File run_main_cron.ps1
```

#### วิธีที่ 3: PHP โดยตรง
```bash
php main_cron.php
```

### Linux/macOS

#### วิธีที่ 1: Shell Script
```bash
# ให้สิทธิ์ execute (ครั้งแรกเท่านั้น)
chmod +x run_main_cron.sh

# รันสคริปต์
./run_main_cron.sh
```

#### วิธีที่ 2: PHP โดยตรง
```bash
php main_cron.php
```

## ⏰ ตั้งค่า Cron Job

### Linux/macOS Cron

```bash
# แก้ไข crontab
crontab -e

# เพิ่มบรรทัดนี้เพื่อรันทุก 5 นาที
*/5 * * * * /usr/bin/php /path/to/your/project/cron/main_cron.php

# หรือใช้ shell script
*/5 * * * * /path/to/your/project/cron/run_main_cron.sh
```

### Windows Task Scheduler

1. เปิด **Task Scheduler**
2. สร้าง **Basic Task**
3. ตั้งชื่อ: "Media Server Main Cron"
4. Trigger: **Daily** → **Repeat every: 5 minutes**
5. Action: **Start a program**
   - Program: `C:\path\to\php.exe`
   - Arguments: `C:\path\to\your\project\cron\main_cron.php`
   - Start in: `C:\path\to\your\project\cron`

หรือใช้ batch file:
- Program: `C:\path\to\your\project\cron\run_main_cron.bat`

## 📊 การทำงาน

### ลำดับการทำงาน:
1. **Fetch Transactions** → ดึงข้อมูลจาก API
2. **Slip Verification** → ตรวจสอบและอนุมัติสลิป
3. **Subscription Check** → เช็ควันหมดอายุ

### Exit Codes:
- **0** = สำเร็จทั้งหมด
- **1** = มีข้อผิดพลาดบางส่วน

## 📝 Log Files

### ตำแหน่ง Log:
```
cron/logs/main_cron.log
```

### ตัวอย่าง Log:
```
[2025-06-23 17:30:00] 🚀 เริ่มต้น Main Cron Job
[2025-06-23 17:30:01] === เริ่มดึงข้อมูล Transaction ===
[2025-06-23 17:30:02] API Fetch Output: Fetched 5 transactions
[2025-06-23 17:30:02] === เสร็จสิ้นการดึงข้อมูล Transaction ===
[2025-06-23 17:30:03] === เริ่มตรวจสอบสลิปอัตโนมัติ ===
[2025-06-23 17:30:04] อนุมัติสลิปอัตโนมัติ: 2 รายการ
[2025-06-23 17:30:04] === เสร็จสิ้นการตรวจสอบสลิป ===
[2025-06-23 17:30:05] === เริ่มตรวจสอบ Subscription ที่หมดอายุ ===
[2025-06-23 17:30:06] อัพเดท subscription ที่หมดอายุ: 1 รายการ
[2025-06-23 17:30:06] === เสร็จสิ้นการตรวจสอบ Subscription ===
[2025-06-23 17:30:06] 📊 สรุปผลการทำงาน:
[2025-06-23 17:30:06] ✅ งานที่สำเร็จ: 3/3
[2025-06-23 17:30:06] 🎉 Main Cron Job เสร็จสิ้นสมบูรณ์
```

## 🔧 การแก้ไขปัญหา

### ปัญหาที่พบบ่อย:

1. **PHP ไม่พบ**
   ```bash
   # ตรวจสอบ PHP path
   which php
   # หรือ
   where php
   ```

2. **Permission Denied (Linux)**
   ```bash
   chmod +x run_main_cron.sh
   chmod 755 main_cron.php
   ```

3. **Log ไม่สร้าง**
   ```bash
   # สร้าง directory logs
   mkdir -p cron/logs
   chmod 755 cron/logs
   ```

## 📈 ข้อดี

### เปรียบเทียบกับการรันแยก:
- ✅ **รันครั้งเดียว** แทนที่จะรัน 3 ครั้ง
- ✅ **Log รวม** ง่ายต่อการติดตาม
- ✅ **Error handling** ที่ดีกว่า
- ✅ **Performance** ดีกว่า (เชื่อมต่อ DB ครั้งเดียว)
- ✅ **ง่ายต่อการจัดการ** Cron Job

## ⚠️ ข้อควรระวัง

1. **ไม่ควรรันพร้อมกัน** กับสคริปต์เก่า
2. **ตรวจสอบ timezone** ให้ถูกต้อง
3. **ตรวจสอบ log** เป็นประจำ
4. **Backup ข้อมูล** ก่อนใช้งานครั้งแรก
