<?php
require_once 'classes/SlipVerificationManager.php';

// ตรวจสอบสิทธิ์ admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ?page=login');
    exit;
}

$message = '';
$error_message = '';

try {
    $slipManager = new SlipVerificationManager();
} catch (Exception $e) {
    $error_message = "ไม่สามารถเชื่อมต่อระบบตรวจสอบสลิป: " . $e->getMessage();
    $slipManager = null;
}

// Handle manual verification
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['manual_verify'])) {
    if ($slipManager === null) {
        $error_message = "ระบบตรวจสอบสลิปไม่พร้อมใช้งาน";
    } else {
        try {
            $result = $slipManager->processAutoApproval();

            if ($result['success']) {
                $message = "การตรวจสอบเสร็จสิ้น: ประมวลผล {$result['processed']} รายการ, อนุมัติ {$result['approved']} รายการ";

                // แสดงรายการที่อนุมัติสำเร็จ
                if ($result['approved'] > 0 && !empty($result['results'])) {
                    $approved_list = [];
                    foreach ($result['results'] as $res) {
                        if ($res['approved']) {
                            $approved_list[] = "Trans ID {$res['trans_id']} จำนวน {$res['amount']} ฿";
                        }
                    }
                    if (!empty($approved_list)) {
                        $message .= "<br><br>รายการที่อนุมัติ:<br>" . implode("<br>", $approved_list);
                    }
                }
            } else {
                $error_message = "การตรวจสอบล้มเหลว: " . $result['message'];
            }
        } catch (Exception $e) {
            $error_message = "เกิดข้อผิดพลาด: " . $e->getMessage();
        }
    }
}

// ดึงข้อมูลธุรกรรมล่าสุด
if ($slipManager !== null) {
    $transactionResult = $slipManager->getTransactionsFromFile();
    $transactions = $transactionResult['success'] ? $transactionResult['data'] : [];
    $lastUpdated = $transactionResult['last_updated'] ?? null;
} else {
    $transactions = [];
    $lastUpdated = null;
}

// ดึงข้อมูลการเติมเงินที่รอการอนุมัติ
$database = new Database();
$conn = $database->getConnection();

$query = "SELECT t.*, u.username
          FROM top_ups t
          JOIN users u ON t.user_id = u.id
          WHERE t.status = 'pending'
          ORDER BY t.created_at DESC
          LIMIT 20";
$stmt = $conn->prepare($query);
$stmt->execute();
$pending_topups = $stmt->fetchAll(PDO::FETCH_ASSOC);

// ดึงข้อมูลการเติมเงินที่อนุมัติแล้วล่าสุด
$query = "SELECT t.*, u.username, pt.trans_id
          FROM top_ups t
          JOIN users u ON t.user_id = u.id
          LEFT JOIN processed_transactions pt ON t.id = pt.topup_id
          WHERE t.status = 'approved'
          ORDER BY t.approved_at DESC
          LIMIT 20";
$stmt = $conn->prepare($query);
$stmt->execute();
$approved_topups = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-receipt me-2"></i>ระบบตรวจสอบสลิปอัตโนมัติ</h2>
                <form method="POST" class="d-inline">
                    <input type="hidden" name="manual_verify" value="1">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sync-alt me-2"></i>ตรวจสอบทันที
                    </button>
                </form>
            </div>

            <?php if($message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <?php if($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- สถานะระบบ -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">ธุรกรรมล่าสุด</h5>
                                    <h3><?php echo count($transactions); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-list fa-2x"></i>
                                </div>
                            </div>
                            <?php if($lastUpdated): ?>
                            <small>อัปเดตล่าสุด: <?php echo $lastUpdated; ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">รอการอนุมัติ</h5>
                                    <h3><?php echo count($pending_topups); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                            <small>การเติมเงินที่รอการอนุมัติ</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">อนุมัติแล้ว</h5>
                                    <h3><?php echo count($approved_topups); ?></h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check fa-2x"></i>
                                </div>
                            </div>
                            <small>การเติมเงินที่อนุมัติแล้ว (20 รายการล่าสุด)</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- แท็บ -->
            <ul class="nav nav-tabs" id="verificationTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions" type="button" role="tab">
                        <i class="fas fa-list me-2"></i>ธุรกรรมจากธนาคาร
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
                        <i class="fas fa-clock me-2"></i>รอการอนุมัติ
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="approved-tab" data-bs-toggle="tab" data-bs-target="#approved" type="button" role="tab">
                        <i class="fas fa-check me-2"></i>อนุมัติแล้ว
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="verificationTabsContent">
                <!-- ธุรกรรมจากธนาคาร -->
                <div class="tab-pane fade show active" id="transactions" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">ธุรกรรมจากธนาคาร</h5>
                        </div>
                        <div class="card-body">
                            <?php if(empty($transactions)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">ไม่มีข้อมูลธุรกรรม</p>
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Transaction ID</th>
                                            <th>บัญชี</th>
                                            <th>จำนวน</th>
                                            <th>วันที่</th>
                                            <th>ประเภท</th>
                                            <th>ยอดคงเหลือ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($transactions as $transaction): ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($transaction['trans_id']); ?></code></td>
                                            <td><?php echo htmlspecialchars($transaction['bankaccount']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $transaction['type'] == 'เงินเข้า' ? 'success' : 'danger'; ?>">
                                                    <?php echo number_format($transaction['amount'], 2); ?> ฿
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($transaction['date']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $transaction['type'] == 'เงินเข้า' ? 'success' : 'secondary'; ?>">
                                                    <?php echo htmlspecialchars($transaction['type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo number_format($transaction['balance'], 2); ?> ฿</td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- รอการอนุมัติ -->
                <div class="tab-pane fade" id="pending" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">การเติมเงินที่รอการอนุมัติ</h5>
                        </div>
                        <div class="card-body">
                            <?php if(empty($pending_topups)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <p class="text-muted">ไม่มีการเติมเงินที่รอการอนุมัติ</p>
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>ผู้ใช้</th>
                                            <th>จำนวน</th>
                                            <th>วันที่สร้าง</th>
                                            <th>สถานะ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($pending_topups as $topup): ?>
                                        <tr>
                                            <td><?php echo $topup['id']; ?></td>
                                            <td><?php echo htmlspecialchars($topup['username']); ?></td>
                                            <td>
                                                <span class="badge bg-warning">
                                                    <?php echo number_format($topup['amount'], 2); ?> ฿
                                                </span>
                                                <?php
                                                // แสดงจำนวนเงินเดิมหากมีทศนิยม
                                                $decimal_part = $topup['amount'] - floor($topup['amount']);
                                                if($decimal_part > 0): ?>
                                                <br><small class="text-muted">(เดิม: <?php echo number_format(floor($topup['amount']), 0); ?> ฿)</small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($topup['created_at'])); ?></td>
                                            <td>
                                                <span class="badge bg-warning">รอการอนุมัติ</span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- อนุมัติแล้ว -->
                <div class="tab-pane fade" id="approved" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">การเติมเงินที่อนุมัติแล้ว</h5>
                        </div>
                        <div class="card-body">
                            <?php if(empty($approved_topups)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">ไม่มีการเติมเงินที่อนุมัติแล้ว</p>
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>ผู้ใช้</th>
                                            <th>จำนวน</th>
                                            <th>วันที่อนุมัติ</th>
                                            <th>Transaction ID</th>
                                            <th>สถานะ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($approved_topups as $topup): ?>
                                        <tr>
                                            <td><?php echo $topup['id']; ?></td>
                                            <td><?php echo htmlspecialchars($topup['username']); ?></td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?php echo number_format($topup['amount'], 2); ?> ฿
                                                </span>
                                                <?php
                                                // แสดงจำนวนเงินเดิมหากมีทศนิยม
                                                $decimal_part = $topup['amount'] - floor($topup['amount']);
                                                if($decimal_part > 0): ?>
                                                <br><small class="text-muted">(เดิม: <?php echo number_format(floor($topup['amount']), 0); ?> ฿)</small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $topup['approved_at'] ? date('d/m/Y H:i', strtotime($topup['approved_at'])) : '-'; ?></td>
                                            <td>
                                                <?php if($topup['trans_id']): ?>
                                                <code><?php echo htmlspecialchars($topup['trans_id']); ?></code>
                                                <?php else: ?>
                                                <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">อนุมัติแล้ว</span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>
