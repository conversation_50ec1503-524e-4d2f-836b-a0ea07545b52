@echo off
REM Fast Cron Runner - รันทุก 5 วินาที
REM งาน: ดึง transaction + ตรวจสลิป

echo ========================================
echo Fast Cron Job - Starting (Every 5 seconds)
echo Time: %date% %time%
echo ========================================
echo.
echo กำลังรันงาน:
echo - ดึงข้อมูล Transaction จาก API
echo - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
echo.
echo กด Ctrl+C เพื่อหยุด
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Create logs directory if not exists
if not exist "logs" mkdir logs

:loop
    REM Run fast cron
    php fast_cron.php
    
    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul
    
    REM Check if user wants to stop (this won't work in loop, but kept for reference)
    REM if %ERRORLEVEL% NEQ 0 goto end
    
goto loop

:end
echo.
echo ========================================
echo Fast Cron Job - Stopped
echo Time: %date% %time%
echo ========================================
pause
