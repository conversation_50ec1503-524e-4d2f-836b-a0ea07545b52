<?php
require_once 'classes/EmbyAPI.php';

// Check if user is logged in
if(!$is_logged_in) {
    header('Location: ?page=login');
    exit;
}

$success_message = '';
$error_message = '';

// Handle password sync request
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sync_password'])) {
    try {
        // Get user's Emby account
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "SELECT * FROM emby_accounts WHERE user_id = :user_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user['id']);
        $stmt->execute();
        $emby_account = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if(!$emby_account) {
            throw new Exception('ไม่พบบัญชี Emby ของคุณ');
        }
        
        // Get current password from form or use stored password
        $password = $_POST['password'] ?? $emby_account['emby_password'];
        
        if(empty($password)) {
            throw new Exception('กรุณากรอกรหัสผ่าน');
        }
        
        // Initialize Emby API
        $settings = new Settings();
        $emby_api = new EmbyAPI(
            $settings->get('emby_server_url'),
            $settings->get('emby_api_key')
        );
        
        // Try to sync password to Emby server
        error_log('Sync Password: Starting for user ' . $emby_account['emby_username'] . ' (ID: ' . $emby_account['emby_user_id'] . ')');
        
        $result = $emby_api->updateUserPassword($emby_account['emby_user_id'], $password);
        
        if($result['success']) {
            // Update password in database if it was changed
            if($password !== $emby_account['emby_password']) {
                $update_query = "UPDATE emby_accounts SET emby_password = :password, updated_at = NOW() WHERE user_id = :user_id";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bindParam(':password', $password);
                $update_stmt->bindParam(':user_id', $user['id']);
                $update_stmt->execute();
            }
            
            // Verify password was set correctly
            $verify_result = $emby_api->verifyUserPassword($emby_account['emby_user_id'], $emby_account['emby_username'], $password);
            
            if($verify_result['success']) {
                $success_message = 'ซิงค์รหัสผ่าน Emby สำเร็จ และตรวจสอบการเข้าสู่ระบบได้แล้ว';
            } else {
                $success_message = 'ซิงค์รหัสผ่าน Emby สำเร็จ แต่ไม่สามารถตรวจสอบการเข้าสู่ระบบได้';
                error_log('Password sync succeeded but verification failed: ' . $verify_result['message']);
            }
            
        } else {
            throw new Exception('ไม่สามารถซิงค์รหัสผ่านได้: ' . ($result['message'] ?? 'Unknown error'));
        }
        
    } catch(Exception $e) {
        $error_message = $e->getMessage();
        error_log('Sync Password Error: ' . $e->getMessage());
    }
}

// Get user's Emby account for display
$database = new Database();
$conn = $database->getConnection();

$query = "SELECT * FROM emby_accounts WHERE user_id = :user_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$emby_account = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-sync-alt me-2"></i>ซิงค์รหัสผ่าน Emby</h5>
                </div>
                <div class="card-body">
                    <?php if($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if($emby_account): ?>
                    <div class="mb-4">
                        <h6>ข้อมูลบัญชี Emby</h6>
                        <ul class="list-unstyled">
                            <li><strong>Username:</strong> <?php echo htmlspecialchars($emby_account['emby_username']); ?></li>
                            <li><strong>Emby User ID:</strong> <?php echo htmlspecialchars($emby_account['emby_user_id']); ?></li>
                            <li><strong>Server:</strong> <?php echo htmlspecialchars($emby_account['server_url']); ?></li>
                            <li><strong>รหัสผ่านในฐานข้อมูล:</strong> <?php echo $emby_account['emby_password'] ? 'มี (' . strlen($emby_account['emby_password']) . ' ตัวอักษร)' : 'ไม่มี'; ?></li>
                        </ul>
                    </div>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="password" class="form-label">รหัสผ่านที่ต้องการซิงค์</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       value="<?php echo htmlspecialchars($emby_account['emby_password'] ?? ''); ?>" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility()">
                                    <i class="fas fa-eye" id="password-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                ค่าเริ่มต้นคือรหัสผ่านที่บันทึกในฐานข้อมูล คุณสามารถเปลี่ยนเป็นรหัสผ่านใหม่ได้
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="?page=dashboard" class="btn btn-secondary">กลับ</a>
                            <button type="submit" name="sync_password" class="btn btn-primary">
                                <i class="fas fa-sync-alt me-2"></i>ซิงค์รหัสผ่าน
                            </button>
                        </div>
                    </form>
                    
                    <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ไม่พบบัญชี Emby ของคุณ กรุณาติดต่อผู้ดูแลระบบ
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Instructions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>คำแนะนำ</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>ฟังก์ชันนี้จะพยายามตั้งรหัสผ่านใน Emby Server ให้ตรงกับรหัสผ่านที่คุณระบุ</li>
                        <li>หากรหัสผ่านในฐานข้อมูลไม่ทำงาน ให้ลองใส่รหัสผ่านที่คุณใช้เข้าสู่ระบบเว็บไซต์</li>
                        <li>หลังจากซิงค์สำเร็จ คุณควรจะสามารถเข้า Emby ได้ด้วยรหัสผ่านที่ระบุ</li>
                        <li>หากยังไม่สามารถเข้าได้ กรุณาติดต่อผู้ดูแลระบบ</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePasswordVisibility() {
    const passwordField = document.getElementById('password');
    const eyeIcon = document.getElementById('password-eye');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}
</script>
