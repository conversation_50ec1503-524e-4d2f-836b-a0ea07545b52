@echo off
REM Fast Cron Runner using PATH - รันทุก 5 วินาที

echo ========================================
echo Fast Cron Job - Using System PATH
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Test if php is available in PATH
php -v >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: PHP not found in system PATH!
    echo.
    echo Please add PHP to your system PATH or use a specific batch file:
    echo - start_fast_cron_laragon.bat (for Laragon)
    echo - start_fast_cron_xampp.bat (for XAMPP)
    echo.
    echo To add PHP to PATH in Laragon:
    echo 1. Open Laragon
    echo 2. Click "Menu" button
    echo 3. Click "Tools" ^> "Path" ^> "Add Laragon to Path"
    echo.
    pause
    exit /b 1
)

echo Using PHP from PATH:
php -v | findstr "PHP"
echo.

echo กำลังรันงาน:
echo - ดึงข้อมูล Transaction จาก API
echo - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
echo.
echo กด Ctrl+C เพื่อหยุด
echo ========================================

REM Create logs directory if not exists
if not exist "logs" mkdir logs

:loop
    REM Run fast cron
    php fast_cron.php
    
    REM Check if command failed
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ERROR: Fast cron failed with exit code %ERRORLEVEL%
        echo Check logs/fast_cron.log for details
        echo.
        pause
        goto end
    )
    
    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul
    
goto loop

:end
echo.
echo ========================================
echo Fast Cron Job - Stopped
echo Time: %date% %time%
echo ========================================
