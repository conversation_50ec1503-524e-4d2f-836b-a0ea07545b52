@echo off
REM Fast Cron Runner with Custom PHP Path - รันทุก 5 วินาที

echo ========================================
echo Fast Cron Job - Custom PHP Path
echo Time: %date% %time%
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Check if custom PHP path is already set
if exist "php_path.txt" (
    set /p PHP_PATH=<php_path.txt
    echo Using saved PHP path: %PHP_PATH%
    goto test_php
)

echo Please enter the full path to your PHP executable.
echo.
echo Examples:
echo - C:\laragon\bin\php\php-8.2.12\php.exe
echo - C:\xampp\php\php.exe
echo - C:\wamp64\bin\php\php8.2.12\php.exe
echo.
echo (Run find_laragon_php.bat first to find your PHP path)
echo.

set /p PHP_PATH=Enter PHP path: 

REM Save the path for future use
echo %PHP_PATH% > php_path.txt
echo.
echo PHP path saved to php_path.txt

:test_php
REM Test PHP
echo.
echo Testing PHP...
"%PHP_PATH%" -v >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: PHP test failed!
    echo Path: %PHP_PATH%
    echo.
    echo Please check the path and try again.
    echo Delete php_path.txt to enter a new path.
    pause
    exit /b 1
)

echo [OK] PHP test passed
"%PHP_PATH%" -v | findstr "PHP"
echo.

echo กำลังรันงาน:
echo - ดึงข้อมูล Transaction จาก API
echo - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
echo.
echo กด Ctrl+C เพื่อหยุด
echo ========================================

REM Create logs directory if not exists
if not exist "logs" mkdir logs

:loop
    REM Run fast cron
    "%PHP_PATH%" fast_cron.php
    
    REM Check if command failed
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ERROR: Fast cron failed with exit code %ERRORLEVEL%
        echo Check logs/fast_cron.log for details
        echo.
        pause
        goto end
    )
    
    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul
    
goto loop

:end
echo.
echo ========================================
echo Fast Cron Job - Stopped
echo Time: %date% %time%
echo ========================================
