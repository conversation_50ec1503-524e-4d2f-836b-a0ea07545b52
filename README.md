# Media Server Management System

ระบบจัดการ Media Server สำหรับ Emby และ Jellyfin ด้วย PHP + MySQL

## 🎯 คุณสมบัติหลัก

### 👥 สำหรับผู้ใช้ทั่วไป
- ✅ **สมัครสมาชิกอัตโนมัติ**: เมื่อสมัครสมาชิกแล้ว ระบบจะสร้างบัญชี Emby และ Jellyfin อัตโนมัติ
- ✅ **เติมเงินด้วย PromptPay**: สร้าง QR Code PromptPay สำหรับการเติมเงิน
- ✅ **ซื้อแพ็คเกจ**: เลือกซื้อแพ็คเกจที่เหมาะสมกับความต้องการ
- ✅ **จัดการโปรไฟล์**: แก้ไขข้อมูลส่วนตัวและเปลี่ยนรหัสผ่าน
- ✅ **ติดตามสถานะ**: ดูสถานะแพ็คเกจ ยอดเงินคงเหลือ และประวัติการใช้งาน

### 🛡️ สำหรับผู้ดูแลระบบ (Admin)
- ✅ **อนุมัติการเติมเงิน**: ตรวจสอบและอนุมัติ/ปฏิเสธการเติมเงิน
- ✅ **จัดการผู้ใช้**: ดูรายชื่อผู้ใช้และจัดการบัญชี
- ✅ **จัดการแพ็คเกจ**: เพิ่ม/แก้ไข/ลบแพ็คเกจบริการ
- ✅ **ตั้งค่าระบบ**: กำหนดค่าต่างๆ ของระบบ

### 🤖 ระบบอัตโนมัติ
- ✅ **ตรวจสอบวันหมดอายุ**: ระบบจะตรวจสอบและเปลี่ยนรหัสผ่าน Emby/Jellyfin เมื่อหมดอายุ
- ✅ **คืนสิทธิ์อัตโนมัติ**: เมื่อต่ออายุแพ็คเกจ ระบบจะคืนรหัสผ่านเดิมให้อัตโนมัติ

## การติดตั้ง

### ความต้องการของระบบ
- PHP 7.4 หรือสูงกว่า
- MySQL 5.7 หรือสูงกว่า
- Web Server (Apache/Nginx)
- cURL extension
- PDO MySQL extension

### ขั้นตอนการติดตั้ง

1. **Clone หรือ Download โปรเจค**
   ```bash
   git clone <repository-url>
   cd media-server-management
   ```

2. **สร้างฐานข้อมูล**
   - สร้างฐานข้อมูล MySQL ใหม่
   - Import ไฟล์ `database.sql`
   ```sql
   mysql -u root -p < database.sql
   ```

3. **กำหนดค่าฐานข้อมูล**
   - แก้ไขไฟล์ `config/database.php`
   - ใส่ข้อมูลการเชื่อมต่อฐานข้อมูลที่ถูกต้อง

4. **ตั้งค่า Cron Job** (สำหรับตรวจสอบวันหมดอายุอัตโนมัติ)
   ```bash
   # เพิ่มใน crontab เพื่อรันทุกวันเวลา 02:00
   0 2 * * * /usr/bin/php /path/to/your/project/cron/check_subscriptions.php
   ```

5. **ตั้งค่าระบบ**
   - เข้าสู่ระบบด้วยบัญชี Admin (admin/admin123)
   - ไปที่หน้า "ตั้งค่าระบบ" เพื่อกำหนดค่าต่างๆ

## การกำหนดค่า

### API Keys
ในไฟล์ `database.sql` มีการตั้งค่า API Keys เริ่มต้น:
- **Emby API Key**: `d2499d0eacfe4ccbba940836be91a9f1`
- **Jellyfin API Key**: `e30753ff625847e0bf7163df609ebaf6`

### PromptPay
- **เบอร์โทรศัพท์**: `0820722972`
- **รหัสผ่านหมดอายุ**: `Wxmujwsofu@1234`

### บัญชีเริ่มต้น
- **Admin**: username: `admin`, password: `admin123`

## โครงสร้างไฟล์

```
media-server-management/
├── classes/                 # PHP Classes
│   ├── EmbyAPI.php         # Emby API Handler
│   ├── JellyfinAPI.php     # Jellyfin API Handler
│   ├── UserManager.php     # User Management
│   ├── PaymentManager.php  # Payment & Top-up Management
│   └── SubscriptionManager.php # Subscription Management
├── config/                 # Configuration Files
│   └── database.php        # Database Configuration
├── cron/                   # Cron Jobs
│   └── check_subscriptions.php # Check Expired Subscriptions
├── pages/                  # Web Pages
│   ├── home.php           # Homepage
│   ├── login.php          # Login Page
│   ├── register.php       # Registration Page
│   ├── dashboard.php      # User Dashboard
│   ├── packages.php       # Packages Page
│   ├── topup.php          # Top-up Page
│   ├── profile.php        # User Profile
│   └── admin_*.php        # Admin Pages
├── index.php              # Main Entry Point
├── database.sql           # Database Schema
└── README.md             # This File
```

## การใช้งาน

### สำหรับผู้ใช้ทั่วไป

1. **สมัครสมาชิก**
   - กรอกข้อมูลส่วนตัว
   - ระบบจะสร้างบัญชี Emby และ Jellyfin อัตโนมัติ

2. **เติมเงิน**
   - เลือกจำนวนเงินที่ต้องการเติม
   - สแกน QR Code PromptPay เพื่อชำระเงิน
   - รอการอนุมัติจาก Admin

3. **ซื้อแพ็คเกจ**
   - เลือกแพ็คเกจที่เหมาะสม
   - ยืนยันการซื้อ
   - เริ่มใช้งาน Media Server ได้ทันที

### สำหรับผู้ดูแลระบบ

1. **อนุมัติการเติมเงิน**
   - ตรวจสอบรายการเติมเงินที่รอดำเนินการ
   - อนุมัติหรือปฏิเสธตามความเหมาะสม

2. **จัดการแพ็คเกจ**
   - เพิ่มแพ็คเกจใหม่
   - แก้ไขราคาและรายละเอียด
   - เปิด/ปิดการใช้งานแพ็คเกจ

## API Endpoints

### Emby API
- สร้างผู้ใช้: `POST /emby/Users/<USER>
- อัปเดตรหัสผ่าน: `POST /emby/Users/<USER>/Password`
- จัดการสิทธิ์: `POST /emby/Users/<USER>/Policy`

### Jellyfin API
- สร้างผู้ใช้: `POST /Users/<USER>
- อัปเดตรหัสผ่าน: `POST /Users/<USER>/Password`
- จัดการสิทธิ์: `POST /Users/<USER>/Policy`

## 🔧 การแก้ไขปัญหา

### ❗ ปัญหาที่พบบ่อย

#### 1. **ไม่สามารถเชื่อมต่อฐานข้อมูลได้**
```
Connection error: SQLSTATE[HY000] [1049] Unknown database
```
**แก้ไข:**
- ตรวจสอบการตั้งค่าใน `config/db_config.php`
- สร้างฐานข้อมูล `media_server_management`
- Import ไฟล์ `database.sql`

#### 2. **cURL Extension ไม่ทำงาน**
```
Fatal error: Call to undefined function curl_init()
```
**แก้ไข:**
- เปิด Laragon → PHP → Extensions → เปิดใช้ curl
- หรือแก้ไข `php.ini`: ลบ `;` หน้า `extension=curl`
- Restart Laragon

#### 3. **Header Warning**
```
Warning: Cannot modify header information - headers already sent
```
**แก้ไข:** ✅ แก้ไขแล้วในเวอร์ชันนี้

#### 4. **API ไม่ทำงาน**
- ตรวจสอบ API Keys ใน Admin Settings
- ตรวจสอบว่า Emby/Jellyfin Server ทำงานอยู่
- ตรวจสอบ URL ให้ถูกต้อง

#### 5. **Cron Job ไม่ทำงาน**
- ตรวจสอบการตั้งค่า crontab
- ตรวจสอบ path ของ PHP และไฟล์

### 📋 Log Files
- Cron job logs: `logs/cron_subscriptions.log`
- Web server error logs: ตรวจสอบ error log ของ web server

## การพัฒนาต่อ

### เพิ่มฟีเจอร์ใหม่
1. สร้าง class ใหม่ใน `classes/`
2. เพิ่มหน้าใหม่ใน `pages/`
3. อัปเดต navigation ใน `index.php`

### การปรับแต่ง UI
- ใช้ Bootstrap 5 สำหรับ styling
- แก้ไข CSS ใน `<style>` section ของ `index.php`

## ใบอนุญาต

โปรเจคนี้เป็น Open Source สามารถนำไปใช้และแก้ไขได้ตามต้องการ

## การสนับสนุน

หากมีปัญหาหรือข้อสงสัย สามารถติดต่อผู้พัฒนาได้
