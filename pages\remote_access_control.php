<?php
// Check admin access
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: index.php?page=login');
    exit;
}

require_once 'classes/SubscriptionManager.php';
require_once 'classes/EmbyAPI.php';
require_once 'classes/JellyfinAPI.php';

$database = new Database();
$conn = $database->getConnection();
$settings = new SystemSettings($conn);
$subscriptionManager = new SubscriptionManager();

// Initialize variables - EXACT SAME as test_remote_access.php
$message = '';
$message_type = '';
$test_results = [];



// Handle form submissions
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    
    // Enable Remote Access for User
    if(isset($_POST['test_enable']) && isset($_POST['test_user_id'])) {
        $user_id = $_POST['test_user_id'];

        // Get username for better messages
        $user_query = "SELECT username FROM users WHERE id = :user_id";
        $user_stmt = $conn->prepare($user_query);
        $user_stmt->bindParam(':user_id', $user_id);
        $user_stmt->execute();
        $user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);
        $username = $user_data ? $user_data['username'] : "User ID {$user_id}";

        // Use EXACT SAME method as test_remote_access.php
        $message = "🟢 เปิด Remote Access สำหรับ {$username} (User ID: {$user_id})";
        $test_results['enable'] = $subscriptionManager->enableRemoteAccess($user_id);
        $message_type = 'success';
    }

    // Disable Remote Access for User
    if(isset($_POST['test_disable']) && isset($_POST['test_user_id'])) {
        $user_id = $_POST['test_user_id'];

        // Get username for better messages
        $user_query = "SELECT username FROM users WHERE id = :user_id";
        $user_stmt = $conn->prepare($user_query);
        $user_stmt->bindParam(':user_id', $user_id);
        $user_stmt->execute();
        $user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);
        $username = $user_data ? $user_data['username'] : "User ID {$user_id}";

        // Use EXACT SAME method as test_remote_access.php
        $message = "🟡 ปิด Remote Access สำหรับ {$username} (User ID: {$user_id})";
        $test_results['disable'] = $subscriptionManager->disableRemoteAccess($user_id);
        $message_type = 'warning';
    }

    // Check Remote Access Status for User
    if(isset($_POST['check_status']) && isset($_POST['test_user_id'])) {
        $user_id = $_POST['test_user_id'];

        // Get username for better messages
        $user_query = "SELECT username FROM users WHERE id = :user_id";
        $user_stmt = $conn->prepare($user_query);
        $user_stmt->bindParam(':user_id', $user_id);
        $user_stmt->execute();
        $user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);
        $username = $user_data ? $user_data['username'] : "User ID {$user_id}";

        $message = "🔍 ตรวจสอบสถานะ Remote Access สำหรับ {$username} (User ID: {$user_id})";
        $message_type = 'info';

        // Get user's media server accounts - exact same query as test_remote_access.php
        $query = "SELECT ea.emby_user_id, ja.jellyfin_user_id
                  FROM users u
                  LEFT JOIN emby_accounts ea ON u.id = ea.user_id
                  LEFT JOIN jellyfin_accounts ja ON u.id = ja.user_id
                  WHERE u.id = :user_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $user_accounts = $stmt->fetch(PDO::FETCH_ASSOC);

        if($user_accounts) {
            // Check Emby status - exact same code as test_remote_access.php
            if($user_accounts['emby_user_id']) {
                $emby_url = $settings->get('emby_server_url') ?: 'https://emby.embyjames.xyz';
                $emby_key = $settings->get('emby_api_key') ?: 'd2499d0eacfe4ccbba940836be91a9f1';

                try {
                    $emby_api = new EmbyAPI($emby_url, $emby_key);
                    $emby_policy = $emby_api->getUserPolicy($user_accounts['emby_user_id']);
                    $test_results['emby_status'] = $emby_policy;
                } catch(Exception $e) {
                    $test_results['emby_error'] = $e->getMessage();
                }
            }

            // Check Jellyfin status - exact same code as test_remote_access.php
            if($user_accounts['jellyfin_user_id']) {
                $jellyfin_url = $settings->get('jellyfin_server_url') ?: 'https://jellyfin.embyjames.xyz';
                $jellyfin_key = $settings->get('jellyfin_api_key') ?: 'e30753ff625847e0bf7163df609ebaf6';

                try {
                    $jellyfin_api = new JellyfinAPI($jellyfin_url, $jellyfin_key);
                    $jellyfin_policy = $jellyfin_api->getUserPolicy($user_accounts['jellyfin_user_id']);
                    $test_results['jellyfin_status'] = $jellyfin_policy;
                } catch(Exception $e) {
                    $test_results['jellyfin_error'] = $e->getMessage();
                }
            }
        }
    }

    // Run Subscription Check
    if(isset($_POST['run_subscription_check'])) {
        $expired_result = $subscriptionManager->checkExpiredSubscriptions();
        $renewed_result = $subscriptionManager->checkRenewedSubscriptions();
        
        $check_message = "ตรวจสอบ Subscription เสร็จสิ้น - ";
        $check_message .= "หมดอายุ: " . ($expired_result['processed'] ?? 0) . " รายการ, ";
        $check_message .= "ต่ออายุ: " . ($renewed_result['processed'] ?? 0) . " รายการ";
        
        $message = $check_message;
        $message_type = 'info';
    }

    // Check Inactive Subscriptions and Disable Remote Access
    if(isset($_POST['check_inactive_subscriptions'])) {
        $inactive_result = $subscriptionManager->checkInactiveSubscriptions();

        if($inactive_result['success']) {
            $message = "🔍 ตรวจสอบ subscription ไม่ active สำเร็จ - ปิด Remote Access ให้ {$inactive_result['processed']} รายการ";
            $message_type = 'warning';

            if($inactive_result['processed'] > 0) {
                $test_results['inactive_check'] = $inactive_result['results'];
            }
        } else {
            $message = "❌ เกิดข้อผิดพลาดในการตรวจสอบ subscription ไม่ active: " . $inactive_result['message'];
            $message_type = 'danger';
        }
    }
}

// Initialize APIs for status checking FIRST
$emby_api = null;
$jellyfin_api = null;
$emby_connection = false;
$jellyfin_connection = false;
$emby_error = null;
$jellyfin_error = null;

// Initialize Emby API
$emby_url = $settings->get('emby_server_url');
$emby_key = $settings->get('emby_api_key');

// Fallback to hardcoded working values if database values are empty
if(empty($emby_url)) {
    $emby_url = 'https://emby.embyjames.xyz';
}
if(empty($emby_key)) {
    $emby_key = 'd2499d0eacfe4ccbba940836be91a9f1';
}

if($emby_url && $emby_key) {
    try {
        require_once 'classes/EmbyAPI.php';
        $emby_api = new EmbyAPI($emby_url, $emby_key);
        $emby_connection = $emby_api->testConnection();
    } catch(Exception $e) {
        $emby_error = $e->getMessage();
        $emby_api = null;
        $emby_connection = false;
    }
}

// Initialize Jellyfin API
$jellyfin_url = $settings->get('jellyfin_server_url');
$jellyfin_key = $settings->get('jellyfin_api_key');

// Fallback to hardcoded working values if database values are empty
if(empty($jellyfin_url)) {
    $jellyfin_url = 'https://jellyfin.embyjames.xyz';
}
if(empty($jellyfin_key)) {
    $jellyfin_key = 'e30753ff625847e0bf7163df609ebaf6';
}

if($jellyfin_url && $jellyfin_key) {
    try {
        require_once 'classes/JellyfinAPI.php';
        $jellyfin_api = new JellyfinAPI($jellyfin_url, $jellyfin_key);
        $jellyfin_connection = $jellyfin_api->testConnection();
    } catch(Exception $e) {
        $jellyfin_error = $e->getMessage();
        $jellyfin_api = null;
        $jellyfin_connection = false;
    }
}

// Auto-check and disable remote access for inactive subscriptions
$auto_disabled_count = 0;
$auto_disabled_users = [];

try {
    $inactive_result = $subscriptionManager->checkInactiveSubscriptions();
    if($inactive_result['success'] && $inactive_result['processed'] > 0) {
        $auto_disabled_count = $inactive_result['processed'];
        $auto_disabled_users = $inactive_result['results'];

        // Set message for auto-disabled users
        if(!$message) {
            $message = "🔄 ปิด Remote Access อัตโนมัติสำหรับผู้ใช้ที่ไม่มี subscription active จำนวน {$auto_disabled_count} รายการ";
            $message_type = 'info';
            $test_results['auto_disabled'] = $auto_disabled_users;
        }
    }
} catch(Exception $e) {
    error_log('Auto-disable remote access failed: ' . $e->getMessage());
}

// Get users with media server accounts from database
$query = "SELECT u.id, u.username, u.email,
                 us.status as subscription_status, us.end_date,
                 ea.emby_user_id, ea.status as emby_status,
                 ja.jellyfin_user_id, ja.status as jellyfin_status
          FROM users u
          LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
          LEFT JOIN emby_accounts ea ON u.id = ea.user_id
          LEFT JOIN jellyfin_accounts ja ON u.id = ja.user_id
          WHERE ea.emby_user_id IS NOT NULL OR ja.jellyfin_user_id IS NOT NULL
          ORDER BY u.username";

$stmt = $conn->prepare($query);
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get real-time data from media servers
$emby_users_data = [];
$jellyfin_users_data = [];

// Get Emby users data if API is available
if(isset($emby_api) && $emby_api && $emby_connection) {
    try {
        $emby_users_result = $emby_api->getAllUsers();
        if($emby_users_result['success']) {
            foreach($emby_users_result['data'] as $emby_user) {
                $emby_users_data[$emby_user['Id']] = $emby_user;
            }
        }
    } catch(Exception $e) {
        error_log('Failed to get Emby users data: ' . $e->getMessage());
    }
}

// Get Jellyfin users data if API is available
if(isset($jellyfin_api) && $jellyfin_api && $jellyfin_connection) {
    try {
        $jellyfin_users_result = $jellyfin_api->getAllUsers();
        if($jellyfin_users_result['success']) {
            foreach($jellyfin_users_result['data'] as $jellyfin_user) {
                $jellyfin_users_data[$jellyfin_user['Id']] = $jellyfin_user;
            }
        }
    } catch(Exception $e) {
        error_log('Failed to get Jellyfin users data: ' . $e->getMessage());
    }
}

// Debug: Check if we have any users
$debug_users_count = count($users);
if($debug_users_count == 0) {
    // Check if there are any users at all
    $debug_query = "SELECT COUNT(*) as total FROM users";
    $debug_stmt = $conn->prepare($debug_query);
    $debug_stmt->execute();
    $debug_total = $debug_stmt->fetch(PDO::FETCH_ASSOC)['total'];

    $debug_message = "No users with media server accounts found. Total users in database: " . $debug_total;
}

// APIs already initialized above
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">Remote Access Control</h2>
            <p class="text-muted">จัดการการเข้าถึงจากภายนอกของผู้ใช้</p>
        </div>
    </div>
    
    <!-- Messages - EXACT SAME as test_remote_access.php -->
    <?php if($message): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
                <i class="fas fa-info-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Test Results -->
    <?php if(!empty($test_results)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-flask me-2"></i>ผลการทำงาน Remote Access
                        <?php if(isset($test_results['auto_disabled'])): ?>
                        <span class="badge bg-info ms-2">Auto-disabled: <?php echo count($test_results['auto_disabled']); ?> users</span>
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if(isset($test_results['auto_disabled'])): ?>
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-robot me-2"></i>การปิด Remote Access อัตโนมัติ</h6>
                        <p class="mb-2">ระบบได้ปิด Remote Access อัตโนมัติสำหรับผู้ใช้ที่ไม่มี subscription active:</p>
                        <ul class="mb-0">
                            <?php foreach($test_results['auto_disabled'] as $user): ?>
                            <li><strong><?php echo htmlspecialchars($user['username']); ?></strong> (ID: <?php echo $user['user_id']; ?>)
                                <?php if($user['result']['success']): ?>
                                <span class="text-success">✅ สำเร็จ</span>
                                <?php else: ?>
                                <span class="text-danger">❌ ล้มเหลว</span>
                                <?php endif; ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <pre class="bg-light p-3 rounded"><?php echo htmlspecialchars(json_encode($test_results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- API Alert -->
    <?php if(!$emby_connection && !$jellyfin_connection): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>⚠️ API ไม่พร้อมใช้งาน</h5>
                <p class="mb-3">ไม่สามารถเชื่อมต่อกับ Emby และ Jellyfin API ได้ ทำให้ Remote Access Control ไม่สามารถทำงานได้</p>
                <div class="d-grid gap-2 d-md-flex">
                    <a href="fix_remote_access_api.php" class="btn btn-warning">
                        <i class="fas fa-wrench me-2"></i>แก้ไขปัญหาอัตโนมัติ
                    </a>
                    <a href="setup_api_settings.php" class="btn btn-info">
                        <i class="fas fa-magic me-2"></i>ตั้งค่า API ใหม่
                    </a>
                    <a href="check_api_status.php" class="btn btn-outline-secondary">
                        <i class="fas fa-search me-2"></i>ตรวจสอบปัญหา
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php elseif(!$emby_connection || !$jellyfin_connection): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>API บางตัวไม่พร้อมใช้งาน</h6>
                <p class="mb-2">
                    <?php if(!$emby_connection): ?>❌ Emby API ไม่ทำงาน<?php endif; ?>
                    <?php if(!$jellyfin_connection): ?>❌ Jellyfin API ไม่ทำงาน<?php endif; ?>
                </p>
                <a href="fix_remote_access_api.php" class="btn btn-sm btn-warning">
                    <i class="fas fa-wrench me-2"></i>แก้ไข
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- API Status -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-server me-2"></i>สถานะ API
                    <?php if($auto_disabled_count > 0): ?>
                    <span class="badge bg-warning ms-2">Auto-disabled: <?php echo $auto_disabled_count; ?> users</span>
                    <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Emby API</h6>
                            <?php if($emby_connection): ?>
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle me-2"></i>เชื่อมต่อสำเร็จ
                                <br><small><?php echo htmlspecialchars($emby_url); ?></small>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-times-circle me-2"></i>เชื่อมต่อไม่สำเร็จ
                                <?php if(isset($emby_error)): ?>
                                <br><small><?php echo htmlspecialchars($emby_error); ?></small>
                                <?php elseif(!$emby_url || !$emby_key): ?>
                                <br><small>ยังไม่ได้ตั้งค่า URL หรือ API Key</small>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <h6>Jellyfin API</h6>
                            <?php if($jellyfin_connection): ?>
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle me-2"></i>เชื่อมต่อสำเร็จ
                                <br><small><?php echo htmlspecialchars($jellyfin_url); ?></small>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-times-circle me-2"></i>เชื่อมต่อไม่สำเร็จ
                                <?php if(isset($jellyfin_error)): ?>
                                <br><small><?php echo htmlspecialchars($jellyfin_error); ?></small>
                                <?php elseif(!$jellyfin_url || !$jellyfin_key): ?>
                                <br><small>ยังไม่ได้ตั้งค่า URL หรือ API Key</small>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Control Panel -->
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tools me-2"></i>เครื่องมือควบคุม</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <form method="POST" class="d-inline">
                                <button type="submit" name="run_subscription_check" class="btn btn-primary w-100">
                                    <i class="fas fa-sync me-2"></i>ตรวจสอบ Subscription
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="POST" class="d-inline">
                                <button type="submit" name="check_inactive_subscriptions" class="btn btn-warning w-100">
                                    <i class="fas fa-user-slash me-2"></i>ปิด Remote Inactive
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <button onclick="location.reload()" class="btn btn-info w-100">
                                <i class="fas fa-refresh me-2"></i>รีเฟรช
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>ผู้ใช้ที่มีบัญชี Media Server</h5>
                </div>
                <div class="card-body">
                    <?php if(empty($users)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">ไม่พบผู้ใช้ที่มีบัญชี Media Server</p>
                        <?php if(isset($debug_message)): ?>
                        <div class="alert alert-info mt-3">
                            <small><?php echo htmlspecialchars($debug_message); ?></small>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ผู้ใช้</th>
                                    <th>Subscription</th>
                                    <th>Emby Status</th>
                                    <th>Jellyfin Status</th>
                                    <th>การจัดการ <small class="text-muted">(ON/OFF/CHECK)</small></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($users as $user): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($user['subscription_status'] == 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                        <br><small>ถึง: <?php echo $user['end_date']; ?></small>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($user['emby_user_id']): ?>
                                        <div>
                                            <span class="badge bg-<?php echo $user['emby_status'] == 'active' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($user['emby_status']); ?>
                                            </span>
                                            <?php if(isset($emby_api) && $emby_api && $emby_connection && isset($emby_users_data[$user['emby_user_id']])): ?>
                                            <br><small class="text-muted">
                                                <?php
                                                $emby_user_data = $emby_users_data[$user['emby_user_id']];
                                                if(isset($emby_user_data['Policy']['EnableRemoteAccess'])) {
                                                    $remote_enabled = $emby_user_data['Policy']['EnableRemoteAccess'];
                                                    echo $remote_enabled ? '🌐 Remote: ON' : '🏠 Remote: OFF';
                                                } else {
                                                    echo '❓ Remote: Unknown';
                                                }
                                                ?>
                                            </small>
                                            <?php else: ?>
                                            <br><small class="text-muted">❌ API ไม่พร้อมใช้งาน</small>
                                            <?php endif; ?>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">ไม่มีบัญชี</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($user['jellyfin_user_id']): ?>
                                        <div>
                                            <span class="badge bg-<?php echo $user['jellyfin_status'] == 'active' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($user['jellyfin_status']); ?>
                                            </span>
                                            <?php if(isset($jellyfin_api) && $jellyfin_api && $jellyfin_connection && isset($jellyfin_users_data[$user['jellyfin_user_id']])): ?>
                                            <br><small class="text-muted">
                                                <?php
                                                $jellyfin_user_data = $jellyfin_users_data[$user['jellyfin_user_id']];
                                                if(isset($jellyfin_user_data['Policy']['EnableRemoteAccess'])) {
                                                    $remote_enabled = $jellyfin_user_data['Policy']['EnableRemoteAccess'];
                                                    echo $remote_enabled ? '🌐 Remote: ON' : '🏠 Remote: OFF';
                                                } else {
                                                    echo '❓ Remote: Unknown';
                                                }
                                                ?>
                                            </small>
                                            <?php else: ?>
                                            <br><small class="text-muted">❌ API ไม่พร้อมใช้งาน</small>
                                            <?php endif; ?>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">ไม่มีบัญชี</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if(($user['emby_user_id'] && isset($emby_api) && $emby_connection) || ($user['jellyfin_user_id'] && isset($jellyfin_api) && $jellyfin_connection)): ?>
                                        <!-- Remote Access Control Buttons -->
                                        <div class="btn-group" role="group">
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="test_user_id" value="<?php echo $user['id']; ?>">
                                                <input type="hidden" name="test_enable" value="1">
                                                <button type="submit" class="btn btn-sm btn-success" title="เปิด Remote Access">
                                                    <i class="fas fa-wifi"></i> ON
                                                </button>
                                            </form>

                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="test_user_id" value="<?php echo $user['id']; ?>">
                                                <input type="hidden" name="test_disable" value="1">
                                                <button type="submit" class="btn btn-sm btn-warning" title="ปิด Remote Access">
                                                    <i class="fas fa-wifi-slash"></i> OFF
                                                </button>
                                            </form>

                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="test_user_id" value="<?php echo $user['id']; ?>">
                                                <input type="hidden" name="check_status" value="1">
                                                <button type="submit" class="btn btn-sm btn-info" title="ตรวจสอบสถานะ">
                                                    <i class="fas fa-search"></i> CHECK
                                                </button>
                                            </form>
                                        </div>
                                        <?php else: ?>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-secondary" disabled title="API ไม่พร้อมใช้งาน">
                                                <i class="fas fa-wifi"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-secondary" disabled title="API ไม่พร้อมใช้งาน">
                                                <i class="fas fa-wifi-slash"></i>
                                            </button>
                                        </div>
                                        <br><small class="text-muted">API ไม่พร้อมใช้งาน</small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

</div>
