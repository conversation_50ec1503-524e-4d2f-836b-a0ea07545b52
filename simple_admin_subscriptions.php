<?php
/**
 * หน้าทดสอบ admin subscriptions แบบง่าย
 */

session_start();

// ตรวจสอบสิทธิ์แอดมิน
if(!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo "<h1>❌ ไม่มีสิทธิ์เข้าถึง</h1>";
    echo "<p>กรุณา login ด้วยบัญชีแอดมิน</p>";
    echo "<a href='?page=login'>ไปหน้า Login</a>";
    exit();
}

echo "<h1>✅ เข้าถึงหน้าแอดมินได้แล้ว</h1>";
echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
echo "<p>Role: " . $_SESSION['role'] . "</p>";
echo "<p>Username: " . $_SESSION['username'] . "</p>";

// ทดสอบการเชื่อมต่อฐานข้อมูล
try {
    require_once 'config/db_config.php';
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "<h2>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</h2>";
        
        // ตรวจสอบตาราง user_subscriptions
        try {
            $query = "SHOW TABLES LIKE 'user_subscriptions'";
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $table_exists = $stmt->rowCount() > 0;
            
            if ($table_exists) {
                echo "<h3>✅ ตาราง user_subscriptions มีอยู่</h3>";
                
                // ดึงข้อมูลตัวอย่าง
                $query = "SELECT COUNT(*) as count FROM user_subscriptions";
                $stmt = $conn->prepare($query);
                $stmt->execute();
                $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                
                echo "<p>จำนวนข้อมูล: {$count} รายการ</p>";
                
                if ($count > 0) {
                    // แสดงข้อมูลตัวอย่าง
                    $query = "SELECT us.*, u.username 
                              FROM user_subscriptions us 
                              LEFT JOIN users u ON us.user_id = u.id 
                              LIMIT 5";
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<h4>ข้อมูลตัวอย่าง:</h4>";
                    echo "<table border='1' style='border-collapse: collapse;'>";
                    echo "<tr><th>ID</th><th>User ID</th><th>Username</th><th>Package ID</th><th>Status</th><th>Start Date</th><th>End Date</th></tr>";
                    foreach ($samples as $sample) {
                        echo "<tr>";
                        echo "<td>{$sample['id']}</td>";
                        echo "<td>{$sample['user_id']}</td>";
                        echo "<td>" . ($sample['username'] ?? 'N/A') . "</td>";
                        echo "<td>{$sample['package_id']}</td>";
                        echo "<td>{$sample['status']}</td>";
                        echo "<td>{$sample['start_date']}</td>";
                        echo "<td>{$sample['end_date']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
                
                echo "<h3>🎯 ทดสอบหน้าจริง</h3>";
                echo "<p><a href='?page=admin_subscriptions' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>เข้าหน้า Admin Subscriptions</a></p>";
                
            } else {
                echo "<h3>❌ ไม่มีตาราง user_subscriptions</h3>";
                echo "<p>กรุณารัน SQL เพื่อสร้างตาราง:</p>";
                echo "<pre>sql/create_user_subscriptions_table.sql</pre>";
            }
            
        } catch (Exception $e) {
            echo "<h3>❌ ข้อผิดพลาดในการตรวจสอบตาราง</h3>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<h2>❌ ไม่สามารถเชื่อมต่อฐานข้อมูลได้</h2>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ ข้อผิดพลาดฐานข้อมูล</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

echo "<h2>🔗 ลิงก์อื่นๆ</h2>";
echo "<ul>";
echo "<li><a href='debug_admin_subscriptions.php'>Debug Admin Subscriptions</a></li>";
echo "<li><a href='test_subscription_management.php'>Test Subscription Management</a></li>";
echo "<li><a href='?page=admin_users'>Admin Users</a></li>";
echo "<li><a href='?page=admin_settings'>Admin Settings</a></li>";
echo "</ul>";
?>
