@echo off
REM Fast Cron Runner - รันทุก 5 วินาที
REM งาน: ดึง transaction + ตรวจสลิป

echo ========================================
echo Fast Cron Job - Starting (Every 5 seconds)
echo Time: %date% %time%
echo ========================================
echo.
echo กำลังรันงาน:
echo - ดึงข้อมูล Transaction จาก API
echo - ตรวจสอบและอนุมัติสลิปอัตโนมัติ
echo.
echo กด Ctrl+C เพื่อหยุด
echo ========================================

REM Change to script directory
cd /d "%~dp0"

REM Create logs directory if not exists
if not exist "logs" mkdir logs

REM Try to find PHP executable
set PHP_PATH=php
if exist "C:\xampp\php\php.exe" set PHP_PATH=C:\xampp\php\php.exe
if exist "C:\wamp64\bin\php\php8.2.12\php.exe" set PHP_PATH=C:\wamp64\bin\php\php8.2.12\php.exe
if exist "C:\laragon\bin\php\php8.2.12\php.exe" set PHP_PATH=C:\laragon\bin\php\php8.2.12\php.exe

echo Using PHP: %PHP_PATH%
echo.

:loop
    REM Run fast cron
    "%PHP_PATH%" fast_cron.php

    REM Check if PHP command failed
    if %ERRORLEVEL% EQU 9009 (
        echo ERROR: PHP not found! Please install PHP or add it to PATH
        echo.
        echo Common PHP locations:
        echo - C:\xampp\php\php.exe
        echo - C:\wamp64\bin\php\[version]\php.exe
        echo - C:\laragon\bin\php\[version]\php.exe
        echo.
        pause
        goto end
    )

    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul

goto loop

:end
echo.
echo ========================================
echo Fast Cron Job - Stopped
echo Time: %date% %time%
echo ========================================
pause
